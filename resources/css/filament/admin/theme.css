@import "../../../../vendor/filament/filament/resources/css/theme.css";

@config './tailwind.config.js';

.fi-topbar>nav,
.fi-sidebar-header {
    @apply bg-transparent ring-0 shadow-none !important;
    transition: background-color 0.3s, top 0.3s;
}

.fi-topbar>nav.topbar-hovered,
.fi-sidebar-header.topbar-hovered {
    background-color: rgba(255, 255, 255, 0.75) !important;
}

:is(.dark .fi-topbar > nav.topbar-hovered,
    .dark .fi-sidebar-header.topbar-hovered) {
    background-color: rgba(10, 16, 33, 0.75) !important;
}

.fi-topbar>nav.topbar-scrolled,
.fi-sidebar-header.topbar-scrolled {
    background-color: rgba(255, 255, 255, 0.5) !important;
}

:is(.dark .fi-topbar > nav.topbar-scrolled,
    .dark .fi-sidebar-header.topbar-scrolled) {
    background-color: rgba(10, 16, 33, 0.5) !important;
}

::-webkit-scrollbar-corner {
    background-color: transparent;
}

::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background-color: transparent;
}

::-webkit-scrollbar-thumb {
    background-color: #eeeeee;
    border-radius: 10px;
    border: 2px solid transparent;
    background-clip: content-box;
}

::-webkit-scrollbar-thumb:hover {
    background-color: #e3e3e3;
}

/* Specific styling for table content scrollbars */
.fi-ta-content::-webkit-scrollbar {
    width: 5px;
    height: 5px;
}

.fi-ta-content::-webkit-scrollbar-thumb {
    background-color: #dddddd;
    border-radius: 10px;
    border: 1.5px solid transparent;
    background-clip: content-box;
}

.fi-ta-content::-webkit-scrollbar-thumb:hover {
    background-color: #cccccc;
}

.dark .fi-ta-content::-webkit-scrollbar-thumb {
    background-color: #cccccc;
}

.dark .fi-ta-content::-webkit-scrollbar-thumb:hover {
    background-color: #cccccc;
}

/* Additional styling for the specific class mentioned */
.fi-ta-content.relative.divide-y.divide-gray-200.overflow-x-auto.dark\:divide-white\/10.dark\:border-t-white\/10::-webkit-scrollbar,
.fi-ta-content.relative.divide-y.divide-gray-200.overflow-x-auto::-webkit-scrollbar {
    width: 7px;
    height: 7px;
}

.fi-ta-content.relative.divide-y.divide-gray-200.overflow-x-auto.dark\:divide-white\/10.dark\:border-t-white\/10::-webkit-scrollbar-thumb,
.fi-ta-content.relative.divide-y.divide-gray-200.overflow-x-auto::-webkit-scrollbar-thumb {
    background-color: #cccccc;
    border-radius: 8px;
    border: 1px solid transparent;
    background-clip: content-box;
}

:root {
    --livewire-progress-bar-color: #f6b130 !important;
}

/* Google Maps Autocomplete Styling to match Filament Select */
.pac-container {
    @apply bg-white dark:bg-gray-900 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg mt-1 z-50;
    font-family: inherit !important;
    border-radius: 0.5rem !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
}

.pac-item {
    @apply px-3 py-2 text-sm text-gray-900 dark:text-gray-100 cursor-pointer border-b border-gray-100 dark:border-gray-700;
    font-family: inherit !important;
    border-bottom: 1px solid rgb(243 244 246) !important;
    line-height: 1.5 !important;
}

.pac-item:last-child {
    @apply border-b-0;
}

.pac-item:hover,
.pac-item-selected {
    @apply bg-gray-50 dark:bg-gray-800 text-gray-900 dark:text-gray-100;
    background-color: rgb(249 250 251) !important;
}

.dark .pac-item:hover,
.dark .pac-item-selected {
    background-color: rgb(31 41 55) !important;
}

.pac-item-query {
    @apply text-gray-900 dark:text-gray-100 font-medium;
    color: inherit !important;
    font-size: inherit !important;
}

.pac-matched {
    @apply text-primary-600 dark:text-primary-400 font-semibold;
    color: rgb(234 179 8) !important;
    font-weight: 600 !important;
}

.dark .pac-matched {
    color: rgb(250 204 21) !important;
}

.pac-icon {
    @apply w-4 h-4 mr-3 opacity-60;
    width: 16px !important;
    height: 16px !important;
    margin-right: 12px !important;
    opacity: 0.6 !important;
}

.pac-icon-marker {
    @apply text-gray-500 dark:text-gray-400;
}

/* Ensure the autocomplete dropdown appears above other elements */
.pac-container {
    z-index: 9999 !important;
}

/* Style the input field when autocomplete is active */
input[data-autocomplete="true"]:focus {
    @apply ring-2 ring-primary-500 dark:ring-primary-400 border-primary-500 dark:border-primary-400;
}

/* Additional styling for better integration */
.google-autocomplete-input {
    @apply transition-all duration-200 ease-in-out;
}

/* Ensure proper spacing and alignment */
.pac-container.filament-autocomplete {
    margin-top: 4px !important;
    border: 1px solid rgb(209 213 219) !important;
    border-radius: 8px !important;
    overflow: hidden !important;
}

.dark .pac-container.filament-autocomplete {
    border-color: rgb(75 85 99) !important;
}

/* Improve the visual hierarchy of autocomplete items */
.pac-item .pac-item-query .pac-matched {
    font-weight: 600 !important;
}

/* Add subtle animation for better UX */
.pac-container {
    animation: fadeInDown 0.15s ease-out !important;
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-4px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Ensure consistent typography */
.pac-item,
.pac-item * {
    font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif !important;
}

.fi-body {
    @apply bg-[#fffdf4] dark:bg-gray-800;
}

.fi-sidebar {
    @apply bg-white dark:bg-gray-900 shadow-lg z-30 !important;
    transition-property: transform, width;
    transition-duration: 0.3s;
    transition-timing-function: ease;
    will-change: transform, width;
}

/* Desktop specific sidebar transitions */
@media (min-width: 1024px) {
    .fi-sidebar {
        transition-property: transform, width !important;
        transition-duration: 0.25s !important;
        transition-timing-function: ease !important;
        will-change: transform, width;
    }

    /* Target the collapsed state in desktop mode */
    /* .fi-sidebar:not(.fi-sidebar-open) {
        width: 5rem !important;
        min-width: 5rem !important;
        overflow: hidden;
    } */

    /* Target the open state in desktop mode */
    .fi-sidebar.fi-sidebar-open {
        width: var(--sidebar-width) !important;
    }
}

.fi-sidebar-header {
    @apply bg-transparent ring-0 shadow-none !important;
}

/* .fi-icon-btn {
    display: none;
} */

.fi-sidebar-header .fi-icon-btn {
    @apply absolute -right-2;
}

.fi-sidebar.-translate-x-full .fi-sidebar-header .fi-icon-btn {
    @apply -right-4;
}

.fi-sidebar-toggle-btn {
    @apply relative ml-auto;
    z-index: 50;
    transition: transform 0.25s ease;
}

.fi-sidebar-toggle-btn svg {
    transition: transform 0.25s ease;
}

/* Desktop specific toggle button animations */
@media (min-width: 1024px) {
    .fi-sidebar-toggle-btn {
        transform-origin: center;
        transition: transform 0.25s ease;
        position: relative;
        z-index: 60;
        width: 2.5rem;
        height: 2.5rem;
    }

    .fi-sidebar-toggle-btn:hover {
        transform: scale(1.05);
    }

    /* Fix button appearance */
    .fi-sidebar-toggle-btn svg {
        width: 1.5rem;
        height: 1.5rem;
    }

    /* We rely on the blade template for icon changes instead of using CSS content */
}

.fi-sidebar-item-active a {
    @apply text-white bg-primary-600 dark:bg-primary-950/70 hover:bg-primary-600 dark:hover:bg-primary-950/70 dark:text-white;
}

.fi-sidebar-item-active .fi-sidebar-item-label {
    @apply text-white dark:text-white;
}

.fi-sidebar-item-active svg {
    @apply text-white dark:text-white;
}

.fi-sidebar-item:not(.fi-sidebar-item-active) a {
    @apply dark:text-gray-400 hover:bg-primary-100 dark:hover:bg-primary-800/50 dark:hover:text-white;
}

.fi-sidebar-item:not(.fi-sidebar-item-active):hover a svg {
    @apply dark:text-white;
}

.fi-main {
    @apply w-full h-full max-w-full px-4 mx-auto md:px-6 lg:px-8;
}

.fi-section {
    @apply ring-1 ring-gray-200/25;
}

.fi-section.fi-aside {
    @apply ring-0;
}

.fi-fo-tabs {
    @apply ring-1 ring-gray-200/25;
}

.fi-ta-ctn {
    @apply ring-1 ring-gray-200/25;
}

.fi-ta-ctn {
    @apply divide-gray-50;
}

.fi-ta-ctn thead tr {
    @apply bg-transparent;
}

.fi-ta-ctn tbody {
    @apply divide-gray-100;
}

.fi-modal-close-overlay,
.spotlight {
    @apply bg-gray-600/30 backdrop-blur-sm;
}

.fi-simple-layout .fi-simple-main .fi-simple-header .fi-logo {
    @apply h-full !important;
}

.fi-dropdown.fi-user-menu {
    @apply ml-2;
}

.fi-sidebar-item a {
    @apply border border-transparent;
    transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* Desktop specific sidebar item animations */
@media (min-width: 1024px) {

    /* Improved sidebar item animations */
    .fi-sidebar-item {
        transition: transform 0.5s cubic-bezier(0.34, 1.56, 0.64, 1), opacity 0.5s ease;
    }

    /* Fix for sidebar icons to stay centered when collapsed */
    .fi-sidebar:not(.fi-sidebar-open) .fi-sidebar-item svg {
        margin: 0 auto;
    }

    /* Ensure sidebar items are properly aligned in collapsed state */
    .fi-sidebar:not(.fi-sidebar-open) .fi-sidebar-item a {
        justify-content: center;
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    /* Ensure sidebar items are properly aligned in expanded state */
    .fi-sidebar.fi-sidebar-open .fi-sidebar-item a {
        justify-content: flex-start;
    }
}

.fi-btn {
    @apply px-8 rounded-xl;
}

.fi-header .fi-btn,
.fi-sidebar-item-active a,
.fi-form-actions .fi-btn,
.fi-ta-actions .fi-btn,
.fi-modal-footer-actions .fi-btn,
.fi-custom-btn {
    @apply bg-gradient-to-b from-primary-300 via-[#f8a34d] to-[#f78232] border border-primary-300 text-black ring-0 dark:from-gray-300 dark:via-gray-300 dark:to-gray-300 dark:border-transparent;
    filter: drop-shadow(0 10px 10px rgba(250, 209, 148, 0.65));

    .dark & {
        filter: none;
    }
}

.fi-btn-group .fi-btn {
    @apply rounded-none;
}

.fi-header button.fi-btn-color-danger {
    @apply text-white bg-none bg-danger-400 border-danger-500 dark:bg-danger-600 dark:border-danger-600;
}

.fi-sidebar-item-active .fi-sidebar-item-label {
    @apply text-black dark:text-black;
}

.fi-sidebar-item-active svg {
    @apply text-black/70 dark:text-black/70;
}

.panel-switch-btn {
    @apply flex items-center justify-center w-full py-1 pl-1 pr-3 text-xs font-medium border rounded-lg gap-x-2 bg-primary-500/10 border-primary-500 dark:border-gray-500;
}

.fi-tenant-menu-trigger {
    @apply flex items-center justify-center w-full p-2 text-sm font-medium text-gray-700 transition duration-75 rounded-lg outline-none gap-x-3 hover:bg-gray-100 focus-visible:bg-gray-100 hover:text-gray-700 dark:hover:bg-white/5 dark:focus-visible:bg-white/5;
}

.pricing-rules-equipment {
    @apply max-w-screen-lg mx-auto;
}

/* Fix Image Editor Buttons */
div.fixed.inset-0.isolate.z-50 div.flex.items-center.gap-3.px-4.py-3 button.fi-btn,
div.fixed.inset-0.isolate.z-50 div.fi-btn-group.grid.grid-flow-col.rounded-lg.shadow-sm.ring-1.w-full button.fi-btn {
    @apply px-3;
}

.fi-theme-switcher-btn,
.fi-tabs-item.group {
    @apply hover:bg-gray-100 dark:hover:bg-gray-800;
}

.fi-theme-switcher-btn.fi-active,
.fi-tabs-item-active {
    @apply bg-gray-100 dark:bg-gray-800;
}

.custom-auth-form-panel .fi-logo {
    @apply w-full justify-center;
}

.fi-sidebar-header .fi-logo {
    @apply w-[140px];
    transition: opacity 0.5s ease, transform 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* Fix logo transitions in desktop mode */
@media (min-width: 1024px) {
    /* .fi-sidebar-header {
        overflow: hidden;
    } */

    .fi-sidebar-header .fi-logo {
        transition: opacity 0.4s ease, transform 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
    }

    .fi-sidebar.fi-sidebar-open .fi-sidebar-header .fi-logo {
        opacity: 1;
        transform: translateX(0);
        transition: opacity 0.4s ease 0.1s, transform 0.5s cubic-bezier(0.34, 1.56, 0.64, 1) 0.1s;
    }

    .fi-sidebar:not(.fi-sidebar-open) .fi-sidebar-header .fi-logo {
        opacity: 0;
        transform: translateX(-20px);
        transition: opacity 0.3s ease, transform 0.4s ease;
    }
}

/* .custom-auth-form-panel .fi-fo-field-wrp-hint.flex.items-center.gap-x-3.text-sm {
    @apply hidden;
} */

.fi-main {
    @apply !max-w-full !overflow-y-auto flex-grow;
}

.fi-topbar {
    @apply static z-auto;
}

.fi-topbar nav {
    @apply bg-[#fffdf4] dark:bg-gray-800 ring-0 shadow-none;
}

.fi-wi-stats-overview-stat {
    @apply ring-1 ring-gray-200/25;
}

.warning-alert {
    @apply p-4 mb-4 text-sm text-red-800 rounded-lg bg-red-50 dark:bg-gray-800 dark:text-red-400;
}

.fi-pagination-overview {
    display: inline !important;
}

.fi-sidebar-nav {
    transition: padding 0.5s cubic-bezier(0.34, 1.56, 0.64, 1), width 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.fi-sidebar-nav-groups {
    transition: opacity 0.5s ease, transform 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* Add animation to sidebar items */
.fi-sidebar-item {
    transition: transform 0.5s cubic-bezier(0.34, 1.56, 0.64, 1), opacity 0.5s ease;
}

/* Add slight bounce effect to sidebar collapse/expand */
.fi-sidebar.fi-sidebar-open {
    animation: sidebarOpen 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes sidebarOpen {
    0% {
        transform: translateX(-5%);
    }

    100% {
        transform: translateX(0);
    }
}

/* Simple direct sidebar animations for desktop */
@media (min-width: 1024px) {

    /* Direct style for sidebar labels */
    .fi-sidebar-item-label {
        transition: opacity 0.7s ease, transform 0.7s cubic-bezier(0.34, 1.56, 0.64, 1);
    }

    .fi-sidebar.fi-sidebar-open .fi-sidebar-item-label {
        opacity: 1;
        transform: translateX(0);
    }

    .fi-sidebar:not(.fi-sidebar-open) .fi-sidebar-item-label {
        opacity: 0;
        transform: translateX(10px);
    }

    /* Ensure sidebar content transitions smoothly */
    .fi-sidebar-nav {
        transition: all 0.7s cubic-bezier(0.34, 1.56, 0.64, 1);
    }

    /* Ensure sidebar items are properly aligned */
    .fi-sidebar:not(.fi-sidebar-open) .fi-sidebar-item a {
        justify-content: center;
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    .fi-sidebar.fi-sidebar-open .fi-sidebar-item a {
        justify-content: flex-start;
    }

    /* Fix for sidebar icons to stay centered when collapsed */
    .fi-sidebar:not(.fi-sidebar-open) .fi-sidebar-item svg {
        margin: 0 auto;
    }
}

/* Add this to adjust the small logo size and position */
.fi-sidebar:not(.fi-sidebar-open) .fi-sidebar-header svg {
    margin-left: -7px;
    /* Move slightly to the left */
}