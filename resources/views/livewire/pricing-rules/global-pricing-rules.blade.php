<div>
    <form wire:submit.prevent="save" class="fi-form grid gap-y-6">
        {{ $this->form }}
        <div class="fi-form-actions">
            <div class="fi-ac gap-3 flex flex-wrap items-center justify-start">
                <button type="submit"
                    style="--c-400:var(--primary-400);--c-500:var(--primary-500);--c-600:var(--primary-600);"
                    class="fi-btn fi-custom-btn relative grid-flow-col items-center justify-center font-semibold outline-none transition duration-75 focus-visible:ring-2 rounded-lg fi-color-custom fi-btn-color-primary fi-color-primary fi-size-md fi-btn-size-md gap-1.5 px-3 py-2 text-sm inline-grid shadow-sm bg-custom-600 text-white hover:bg-custom-500 focus-visible:ring-custom-500/50 dark:bg-custom-500 dark:hover:bg-custom-400 dark:focus-visible:ring-custom-400/50 fi-ac-action fi-ac-btn-action"
                    wire:loading.attr="disabled" wire:target="save">
                    <div class="flex items-center gap-2">
                        <div wire:loading wire:target="save">
                            <x-filament::loading-indicator class="h-5 w-5" />
                        </div>
                        <span>Save changes</span>
                    </div>
                </button>
            </div>
        </div>
    </form>
    <x-filament-actions::modals />

    <!-- Error Details Modal -->
    @if($showErrorModal)
        <div x-data="{}" x-on:keydown.escape.window="$wire.closeErrorModal()"
            class="fixed inset-0 z-50 flex items-center justify-center p-4" style="background-color: rgba(0, 0, 0, 0.5);">
            <div class="relative w-full max-w-4xl bg-white dark:bg-gray-800 rounded-xl shadow-xl flex flex-col"
                style="height: 80vh; max-height: 600px; min-height: 400px;">
                <!-- Modal Header -->
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                            {{ $modalTitle }}
                        </h3>
                        <button type="button" wire:click="closeErrorModal"
                            class="text-gray-400 hover:text-gray-500 focus:outline-none">
                            <span class="sr-only">Close</span>
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Modal Content -->
                <div class="flex-1 overflow-y-auto modal-scrollbar px-6 py-4" style="min-height: 0;">
                    <div class="text-sm text-gray-600 dark:text-gray-400 space-y-4 prose dark:prose-invert max-w-none">
                        @if($modalContent === 'tabbed')
                            @php
                                $tabbedData = $this->getTabbedContentData();
                            @endphp

                            @if($tabbedData['hasBaseError'] || $tabbedData['hasDistanceError'])
                                <div x-data="{ activeTab: '{{ $tabbedData['defaultTab'] }}' }" class="space-y-4">
                                    @if($tabbedData['showTabs'])
                                        <!-- Custom Tabs with Filament Styling -->
                                        <div
                                            class="fi-tabs flex max-w-full gap-x-1 overflow-x-auto mx-auto rounded-xl bg-gray-50 p-1 dark:bg-white/5">
                                            @if($tabbedData['hasBaseError'])
                                                <button type="button" x-on:click="activeTab = 'base'" :class="{
                                                                                                'bg-white shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10': activeTab === 'base',
                                                                                                'hover:bg-gray-50 focus:bg-gray-50 dark:hover:bg-white/5 dark:focus:bg-white/5': activeTab !== 'base'
                                                                                            }"
                                                    class="fi-tabs-item group flex items-center gap-x-2 rounded-lg px-3 py-2 text-sm font-medium outline-none transition duration-75 focus-visible:ring-2 focus-visible:ring-primary-600 dark:focus-visible:ring-primary-500">
                                                    <svg class="fi-tabs-item-icon h-5 w-5" :class="{
                                                                                                    'text-primary-600 dark:text-primary-400': activeTab === 'base',
                                                                                                    'text-gray-500 group-hover:text-gray-700 group-focus:text-gray-700 dark:text-gray-400 dark:group-hover:text-gray-200 dark:group-focus:text-gray-200': activeTab !== 'base'
                                                                                                }" fill="none" viewBox="0 0 24 24"
                                                        stroke-width="1.5" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            d="M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z" />
                                                    </svg>
                                                    <span class="fi-tabs-item-label transition duration-75" :class="{
                                                                                                    'text-primary-600 dark:text-primary-400': activeTab === 'base',
                                                                                                    'text-gray-500 group-hover:text-gray-700 group-focus:text-gray-700 dark:text-gray-400 dark:group-hover:text-gray-200 dark:group-focus:text-gray-200': activeTab !== 'base'
                                                                                                }">
                                                        Base Fare Conflicts
                                                    </span>
                                                </button>
                                            @endif

                                            @if($tabbedData['hasDistanceError'])
                                                <button type="button" x-on:click="activeTab = 'distance'" :class="{
                                                                                                'bg-white shadow-sm ring-1 ring-gray-950/5 dark:bg-gray-900 dark:ring-white/10': activeTab === 'distance',
                                                                                                'hover:bg-gray-50 focus:bg-gray-50 dark:hover:bg-white/5 dark:focus:bg-white/5': activeTab !== 'distance'
                                                                                            }"
                                                    class="fi-tabs-item group flex items-center gap-x-2 rounded-lg px-3 py-2 text-sm font-medium outline-none transition duration-75 focus-visible:ring-2 focus-visible:ring-primary-600 dark:focus-visible:ring-primary-500">
                                                    <svg class="fi-tabs-item-icon h-5 w-5" :class="{
                                                                                                    'text-primary-600 dark:text-primary-400': activeTab === 'distance',
                                                                                                    'text-gray-500 group-hover:text-gray-700 group-focus:text-gray-700 dark:text-gray-400 dark:group-hover:text-gray-200 dark:group-focus:text-gray-200': activeTab !== 'distance'
                                                                                                }" fill="none" viewBox="0 0 24 24"
                                                        stroke-width="1.5" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            d="M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z" />
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z" />
                                                    </svg>
                                                    <span class="fi-tabs-item-label transition duration-75" :class="{
                                                                                                    'text-primary-600 dark:text-primary-400': activeTab === 'distance',
                                                                                                    'text-gray-500 group-hover:text-gray-700 group-focus:text-gray-700 dark:text-gray-400 dark:group-hover:text-gray-200 dark:group-focus:text-gray-200': activeTab !== 'distance'
                                                                                                }">
                                                        Distance Fare Conflicts
                                                    </span>
                                                </button>
                                            @endif
                                        </div>
                                    @endif

                                    <!-- Tab Content -->
                                    @if($tabbedData['hasBaseError'])
                                        <div x-show="activeTab === 'base'" class="py-4">
                                            {!! $tabbedData['baseFareError'] !!}
                                        </div>
                                    @endif

                                    @if($tabbedData['hasDistanceError'])
                                        <div x-show="activeTab === 'distance'" class="py-4">
                                            {!! $tabbedData['distanceFareError'] !!}
                                        </div>
                                    @endif
                                </div>
                            @else
                                <p>No conflicts found.</p>
                            @endif
                        @else
                            {!! $modalContent !!}
                        @endif
                    </div>
                </div>

                <!-- Modal Footer -->
                <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex justify-end flex-shrink-0">
                    <button type="button"
                        class="fi-btn relative inline-flex items-center justify-center font-medium outline-none transition duration-75 focus-visible:ring-2 rounded-lg fi-btn-size-md gap-1.5 px-3 py-2 text-sm fi-btn-color-gray bg-white text-gray-950 hover:bg-gray-50 dark:bg-white/5 dark:text-white dark:hover:bg-white/10 ring-1 ring-gray-950/10 dark:ring-white/20"
                        wire:click="closeErrorModal">
                        Close
                    </button>
                </div>
            </div>
        </div>
    @endif


</div>