<x-filament-panels::page>
    <div class="space-y-6">
        <!-- Header Section -->
        <div class="bg-gradient-to-r from-green-600 to-green-800 overflow-hidden shadow-lg rounded-lg">
            <div class="p-6 text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold">Riders Report</h1>
                        <p class="text-green-100 mt-1">Comprehensive analysis of rider registrations and activity</p>
                    </div>
                    <div class="text-right">
                        <x-heroicon-o-user-group class="h-12 w-12 text-green-200" />
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters Form -->
        <div
            class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm rounded-lg border border-gray-200 dark:border-gray-700">
            <div class="p-6">
                <div class="flex items-center mb-4">
                    <x-heroicon-o-funnel class="h-5 w-5 text-gray-600 dark:text-gray-400 mr-2" />
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Report Filters</h3>
                </div>

                {{ $this->form }}

                <div class="mt-6">
                    <div class="text-sm text-gray-600 dark:text-gray-400">
                        @php $ridersData = $this->getRidersData(); @endphp
                        <span class="font-medium">{{ $ridersData['period_info'] ?? 'All time data' }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Key Metrics -->
        @if($this->reportGenerated && $ridersData['total_riders'] > 0)
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Total Riders -->
                <div
                    class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm rounded-lg border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 p-3 bg-green-100 dark:bg-green-900/30 rounded-lg">
                                <x-heroicon-o-user-group class="h-8 w-8 text-green-600 dark:text-green-400" />
                            </div>
                            <div class="ml-4 flex-1">
                                <h3 class="text-sm font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">
                                    Total Riders
                                </h3>
                                <p class="text-2xl font-bold text-gray-900 dark:text-white mt-1">
                                    {{ number_format($ridersData['total_riders']) }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Active Riders -->
                <div
                    class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm rounded-lg border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 p-3 bg-green-100 dark:bg-green-900/30 rounded-lg">
                                <x-heroicon-o-check-circle class="h-8 w-8 text-green-600 dark:text-green-400" />
                            </div>
                            <div class="ml-4 flex-1">
                                <h3 class="text-sm font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">
                                    Active Riders
                                </h3>
                                <p class="text-2xl font-bold text-green-600 dark:text-green-400 mt-1">
                                    {{ number_format($ridersData['status_distribution']['active'] ?? 0) }}
                                </p>
                                @php
                                    $activePercentage = $ridersData['total_riders'] > 0
                                        ? round((($ridersData['status_distribution']['active'] ?? 0) / $ridersData['total_riders']) * 100, 1)
                                        : 0;
                                @endphp
                                <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">
                                    {{ $activePercentage }}% of total
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Growth Rate -->
                <div
                    class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm rounded-lg border border-gray-200 dark:border-gray-700 hover:shadow-md transition-shadow">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div
                                class="flex-shrink-0 p-3 {{ $ridersData['growth_percentage'] >= 0 ? 'bg-green-100 dark:bg-green-900/30' : 'bg-red-100 dark:bg-red-900/30' }} rounded-lg">
                                @if($ridersData['growth_percentage'] >= 0)
                                    <x-heroicon-o-arrow-trending-up class="h-8 w-8 text-green-600 dark:text-green-400" />
                                @else
                                    <x-heroicon-o-arrow-trending-down class="h-8 w-8 text-red-600 dark:text-red-400" />
                                @endif
                            </div>
                            <div class="ml-4 flex-1">
                                <h3 class="text-sm font-medium text-gray-600 dark:text-gray-400 uppercase tracking-wide">
                                    Growth Rate
                                </h3>
                                <p
                                    class="text-2xl font-bold {{ $ridersData['growth_percentage'] >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' }} mt-1">
                                    {{ $ridersData['growth_percentage'] >= 0 ? '+' : '' }}{{ $ridersData['growth_percentage'] }}%
                                </p>
                                <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">
                                    vs previous period
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @else
            <!-- No Report Generated Message -->
            <div
                class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm rounded-lg border border-gray-200 dark:border-gray-700">
                <div class="p-12 text-center">
                    <div
                        class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-blue-100 dark:bg-blue-900/30 mb-4">
                        <x-heroicon-o-chart-bar class="h-8 w-8 text-blue-600 dark:text-blue-400" />
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
                        No Report Generated
                    </h3>
                    <p class="text-gray-600 dark:text-gray-400 mb-6">
                        Please select your desired filters and click "Generate Report" to view the riders data.
                    </p>
                </div>
            </div>
        @endif

        <!-- Status Distribution -->
        @if($this->reportGenerated && $ridersData['total_riders'] > 0)
            <div
                class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm rounded-lg border border-gray-200 dark:border-gray-700">
                <div class="p-6">
                    <div class="flex items-center mb-6">
                        <x-heroicon-o-chart-pie class="h-6 w-6 text-gray-600 dark:text-gray-400 mr-3" />
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                            Rider Status Distribution
                        </h3>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        @foreach(\App\Enums\RiderGlobalStatus::cases() as $status)
                            @php
                                $count = $ridersData['status_distribution'][$status->value] ?? 0;
                                $percentage = $ridersData['total_riders'] > 0 ? round(($count / $ridersData['total_riders']) * 100, 1) : 0;

                                $bgColor = match ($status->getColor()) {
                                    'success' => 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800',
                                    'danger' => 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800',
                                    'warning' => 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800',
                                    'info' => 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800',
                                    'gray' => 'bg-gray-50 dark:bg-gray-900/20 border-gray-200 dark:border-gray-700',
                                    default => 'bg-gray-50 dark:bg-gray-900/20 border-gray-200 dark:border-gray-700'
                                };

                                $iconColor = match ($status->getColor()) {
                                    'success' => 'text-green-600 dark:text-green-400',
                                    'danger' => 'text-red-600 dark:text-red-400',
                                    'warning' => 'text-yellow-600 dark:text-yellow-400',
                                    'info' => 'text-blue-600 dark:text-blue-400',
                                    'gray' => 'text-gray-600 dark:text-gray-400',
                                    default => 'text-gray-600 dark:text-gray-400'
                                };

                                $progressColor = match ($status->getColor()) {
                                    'success' => '#10b981',
                                    'danger' => '#ef4444',
                                    'warning' => '#f59e0b',
                                    'info' => '#3b82f6',
                                    'gray' => '#6b7280',
                                    default => '#6b7280'
                                };
                            @endphp

                            <div class="border rounded-lg p-4 hover:shadow-md transition-shadow {{ $bgColor }}">
                                <div class="flex items-center justify-between mb-3">
                                    <div class="flex items-center">
                                        <x-dynamic-component :component="$status->getIcon()"
                                            class="h-5 w-5 mr-3 {{ $iconColor }}" />
                                        <span class="text-sm font-medium text-gray-900 dark:text-white">
                                            {{ $status->getLabel() }}
                                        </span>
                                    </div>
                                    <div class="text-right">
                                        <div class="text-xl font-bold text-gray-900 dark:text-white">
                                            {{ number_format($count) }}
                                        </div>
                                    </div>
                                </div>

                                <!-- Progress Bar -->
                                <div class="mb-2">
                                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                        <div class="h-2 rounded-full transition-all duration-300"
                                            style="width: {{ $percentage }}%; background-color: {{ $progressColor }}"></div>
                                    </div>
                                </div>

                                <div class="flex justify-between items-center text-xs">
                                    <span class="text-gray-600 dark:text-gray-400">
                                        {{ $percentage }}% of total
                                    </span>
                                    @if($count > 0)
                                        <span class="font-medium {{ $iconColor }}">
                                            {{ $count }} riders
                                        </span>
                                    @endif
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>

            <!-- Additional Metrics -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Trip Activity -->
                <div
                    class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm rounded-lg border border-gray-200 dark:border-gray-700">
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <x-heroicon-o-map class="h-5 w-5 text-gray-600 dark:text-gray-400 mr-2" />
                            <h4 class="text-lg font-medium text-gray-900 dark:text-white">
                                Trip Activity
                            </h4>
                        </div>
                        @php
                            $ridersWithTrips = $this->getRidersWithTrips();
                            $averageTripsPerRider = $this->getAverageTripsPerRider();
                        @endphp
                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Riders with trips:</span>
                                <span
                                    class="font-semibold text-gray-900 dark:text-white">{{ number_format($ridersWithTrips) }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">Average trips per rider:</span>
                                <span
                                    class="font-semibold text-gray-900 dark:text-white">{{ number_format($averageTripsPerRider, 1) }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Gender Distribution -->
                <div
                    class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm rounded-lg border border-gray-200 dark:border-gray-700">
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <x-heroicon-o-users class="h-5 w-5 text-gray-600 dark:text-gray-400 mr-2" />
                            <h4 class="text-lg font-medium text-gray-900 dark:text-white">
                                Gender Distribution
                            </h4>
                        </div>
                        @php
                            $genderStats = \App\Models\User::whereHas('rider')
                                ->selectRaw('gender, COUNT(*) as count')
                                ->groupBy('gender')
                                ->pluck('count', 'gender')
                                ->toArray();
                            $totalWithGender = array_sum($genderStats);
                        @endphp
                        <div class="space-y-3">
                            @foreach(['male', 'female'] as $gender)
                                @php
                                    $count = $genderStats[$gender] ?? 0;
                                    $percentage = $totalWithGender > 0 ? round(($count / $totalWithGender) * 100, 1) : 0;
                                @endphp
                                <div class="flex justify-between">
                                    <span class="text-gray-600 dark:text-gray-400 capitalize">{{ $gender }}:</span>
                                    <span class="font-semibold text-gray-900 dark:text-white">{{ number_format($count) }}
                                        ({{ $percentage }}%)</span>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </div>
</x-filament-panels::page>