<script>
    // Suppress Google Maps alerts immediately when script loads
    (function () {
        'use strict';

        // Store the original alert function
        const originalAlert = window.alert;

        // Override the alert function with more targeted suppression
        window.alert = function (message) {
            // Only suppress Google Maps specific error messages
            if (typeof message === 'string') {
                const googleMapsErrors = [
                    'No details available for input',
                    'Geocoder failed',
                    'ZERO_RESULTS'
                ];

                const isGoogleMapsError = googleMapsErrors.some(error =>
                    message.includes(error)
                );

                if (isGoogleMapsError) {
                    console.log('Suppressed Google Maps alert:', message);
                    return;
                }
            }

            // Call the original alert for all other messages
            originalAlert.call(window, message);
        };
    })();

    // Google Maps autocomplete styling enhancement
    document.addEventListener('DOMContentLoaded', function () {
        // Wait for Alpine.js to be ready before enhancing
        function enhanceGoogleMapsAutocomplete() {
            // Find all address input fields that have autocomplete
            const addressInputs = document.querySelectorAll('input[name="address"]');

            addressInputs.forEach(function (input) {
                if (input.dataset.autocompleteEnhanced) {
                    return; // Already enhanced
                }

                // Mark as enhanced to prevent duplicate processing
                input.dataset.autocompleteEnhanced = 'true';
                input.dataset.autocomplete = 'true';

                // Add custom styling classes
                input.classList.add('google-autocomplete-input');

                // Listen for focus events to ensure proper styling
                input.addEventListener('focus', function () {
                    setTimeout(function () {
                        const pacContainer = document.querySelector('.pac-container');
                        if (pacContainer) {
                            pacContainer.style.marginTop = '4px';
                            pacContainer.classList.add('filament-autocomplete');
                            pacContainer.style.zIndex = '9999';
                        }
                    }, 50);
                });
            });
        }

        // Run enhancement after a delay to avoid interfering with Alpine.js
        setTimeout(enhanceGoogleMapsAutocomplete, 500);

        // Re-enhance when Livewire updates the DOM
        document.addEventListener('livewire:navigated', function () {
            setTimeout(enhanceGoogleMapsAutocomplete, 500);
        });

        if (window.Livewire) {
            Livewire.hook('morph.updated', () => {
                setTimeout(enhanceGoogleMapsAutocomplete, 500);
            });
        }
    });
</script>