<x-filament-widgets::widget>
    <x-filament::section>
        <div class="filament-widget">
            <!-- Vehicle Real-Time Map with Native Livewire Polling -->
            <div wire:poll.5s="loadVehicles" x-data="{
                map: null,
                vehicles: @js($vehicles),
                markers: [],
                markerClusterer: null,
                lastUpdateTime: Date.now(),

                get lastUpdateFormatted() {
                    return new Date(this.lastUpdateTime).toLocaleTimeString();
                },

                get vehicleCount() {
                    return this.vehicles ? this.vehicles.length : 0;
                },



                initialize() {
                    // Check if Google Maps is loaded
                    if (typeof google === 'undefined' || typeof google.maps === 'undefined') {
                        return;
                    }

                    // Default center (Libya coordinates)
                    const defaultCenter = { lat: 32.8872, lng: 13.1913 };
                    const center = this.vehicles.length > 0
                        ? { lat: parseFloat(this.vehicles[0].lat), lng: parseFloat(this.vehicles[0].lng) }
                        : defaultCenter;

                    try {
                        this.map = new google.maps.Map(this.$refs.map, {
                            center: center,
                            zoom: 12,
                            styles: [
                                {
                                    featureType: 'poi',
                                    elementType: 'labels',
                                    stylers: [{ visibility: 'off' }]
                                }
                            ],
                            mapTypeControl: false,
                            streetViewControl: false,
                            fullscreenControl: false
                        });

                        this.addVehicleMarkers();

                        // Listen for vehicles update events from Livewire
                        this.$wire.on('vehicles-updated', (eventData) => {
                            const data = Array.isArray(eventData) ? eventData[0] : eventData;
                            const newVehicles = data.vehicles;

                            if (newVehicles && newVehicles.length > 0) {
                                this.vehicles = newVehicles;
                                this.addVehicleMarkers();
                                this.lastUpdateTime = Date.now();
                            }
                        });

                        // Watch for changes in vehicles data (from wire:poll)
                        this.$watch('$wire.vehiclesJson', (newVehiclesJson) => {
                            if (newVehiclesJson) {
                                try {
                                    const newVehicles = JSON.parse(newVehiclesJson);
                                    if (newVehicles && newVehicles.length > 0) {
                                        this.vehicles = newVehicles;
                                        this.addVehicleMarkers();
                                        this.lastUpdateTime = Date.now();
                                    }
                                } catch (error) {
                                    console.error('Error parsing vehicles JSON:', error);
                                }
                            }
                        });



                    } catch (error) {
                        console.error('Error initializing vehicle tracking:', error);
                    }
                },

                // Cleanup method
                destroy() {
                    // Clear all markers
                    this.markers.forEach(marker => marker.setMap(null));
                    this.markers = [];

                    // Clear clusterer
                    if (this.markerClusterer) {
                        this.markerClusterer.clearMarkers();
                    }
                },

                addVehicleMarkers() {
                    if (!this.map || !this.vehicles || this.vehicles.length === 0) {
                        return;
                    }

                    // Create a map of existing markers by vehicle ID for efficient lookup
                    const existingMarkers = new Map();
                    this.markers.forEach(marker => {
                        if (marker.vehicleData && marker.vehicleData.id) {
                            existingMarkers.set(marker.vehicleData.id, marker);
                        }
                    });

                    // Track which vehicles are still active
                    const activeVehicleIds = new Set();

                    // Process each vehicle
                    this.vehicles.forEach(vehicle => {
                        if (!vehicle.lat || !vehicle.lng || !vehicle.id) {
                            return;
                        }

                        activeVehicleIds.add(vehicle.id);
                        const newPosition = { lat: parseFloat(vehicle.lat), lng: parseFloat(vehicle.lng) };
                        const existingMarker = existingMarkers.get(vehicle.id);

                        if (existingMarker) {
                            this.updateExistingMarker(existingMarker, vehicle, newPosition);
                        } else {
                            this.createNewMarker(vehicle, newPosition);
                        }
                    });

                    // Remove markers for vehicles that are no longer active
                    this.removeInactiveMarkers(activeVehicleIds);

                    // Update clusterer
                    this.updateClusterer();
                },

                updateExistingMarker(marker, vehicle, newPosition) {
                    // Animate to new position
                    this.animateMarker(marker, newPosition);

                    // Update marker data
                    marker.vehicleData = vehicle;
                    marker.setTitle(vehicle.driver?.name || 'Unknown Driver');

                    // Update icon if status changed
                    const newIconUrl = this.getIconUrl(vehicle.status);
                    const currentIconUrl = marker.getIcon()?.url;
                    if (currentIconUrl !== newIconUrl) {
                        marker.setIcon({
                            url: newIconUrl,
                            scaledSize: new google.maps.Size(20, 40),
                            anchor: new google.maps.Point(10, 20)
                        });
                    }
                },

                createNewMarker(vehicle, position) {
                    const marker = new google.maps.Marker({
                        position: position,
                        map: this.map,
                        title: vehicle.driver?.name || 'Unknown Driver',
                        icon: {
                            url: this.getIconUrl(vehicle.status),
                            scaledSize: new google.maps.Size(20, 40),
                            anchor: new google.maps.Point(10, 20)
                        },
                        animation: google.maps.Animation.DROP
                    });

                    // Store vehicle data
                    marker.vehicleData = vehicle;

                    // Add click listener - call Livewire method
                    marker.addListener('click', () => {
                        this.$wire.selectVehicle(vehicle.id);
                    });

                    this.markers.push(marker);
                },

                removeInactiveMarkers(activeVehicleIds) {
                    this.markers = this.markers.filter(marker => {
                        if (!marker.vehicleData || !activeVehicleIds.has(marker.vehicleData.id)) {
                            // Remove marker from map
                            marker.setMap(null);
                            return false;
                        }
                        return true;
                    });
                },

                getIconUrl(status) {
                    switch(status) {
                        case 'available':
                            return '{{ asset("icons/green.png") }}';
                        case 'on trip':
                            return '{{ asset("icons/red.png") }}';
                        case 'blocked':
                            return '{{ asset("icons/gray.png") }}';
                        default:
                            return '{{ asset("icons/gray.png") }}';
                    }
                },

                animateMarker(marker, newPosition) {
                    const currentPosition = marker.getPosition();
                    if (!currentPosition) {
                        marker.setPosition(newPosition);
                        return;
                    }

                    // Calculate if the distance is significant enough to animate
                    const distance = google.maps.geometry.spherical.computeDistanceBetween(
                        currentPosition,
                        new google.maps.LatLng(newPosition.lat, newPosition.lng)
                    );

                    // Only animate if distance is more than 10 meters
                    if (distance > 10) {
                        // Smooth transition animation
                        const steps = 20;
                        const deltaLat = (newPosition.lat - currentPosition.lat()) / steps;
                        const deltaLng = (newPosition.lng - currentPosition.lng()) / steps;
                        let step = 0;

                        const animate = () => {
                            if (step <= steps) {
                                const lat = currentPosition.lat() + (deltaLat * step);
                                const lng = currentPosition.lng() + (deltaLng * step);
                                marker.setPosition({ lat, lng });
                                step++;
                                setTimeout(animate, 50); // 50ms per step = 1 second total animation
                            }
                        };
                        animate();
                    } else {
                        // Small movement, just update position directly
                        marker.setPosition(newPosition);
                    }
                },

                updateClusterer() {
                    // Clear existing clusterer
                    if (this.markerClusterer) {
                        this.markerClusterer.clearMarkers();
                    }

                    // Recreate clusterer with current markers
                    if (typeof markerClusterer !== 'undefined' && markerClusterer.MarkerClusterer) {
                        this.markerClusterer = new markerClusterer.MarkerClusterer({
                            map: this.map,
                            markers: this.markers,
                            algorithm: new markerClusterer.GridAlgorithm({ gridSize: 60 }),
                            renderer: new markerClusterer.DefaultRenderer({
                                color: '#3b82f6',
                                textColor: 'white'
                            })
                        });
                    }
                },


            }" x-init="initialize()" x-on:beforeunload.window="destroy()" x-on:pagehide.window="destroy()"
                class="space-y-4">
                <!-- Map Container with wire:ignore to prevent Livewire from re-rendering -->
                <div class="relative overflow-hidden" wire:ignore>
                    <div x-ref="map"
                        class="w-full h-96 md:h-[600px] rounded-xl border-2 border-gray-200 dark:border-gray-600 shadow-2xl transition-all duration-300 hover:shadow-3xl bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-900">
                    </div>

                    <!-- Real-time Status Panel -->
                    <div
                        class="absolute top-6 right-6 bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm rounded-xl shadow-2xl border border-gray-200/50 dark:border-gray-700/50 p-4 max-w-sm transition-all duration-300 hover:shadow-3xl hover:scale-105">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="text-base font-bold text-gray-900 dark:text-gray-100 flex items-center gap-2">
                                <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z">
                                    </path>
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                </svg>
                                Real-time Tracking
                            </h4>
                            <div class="flex items-center">
                                <!-- Live Status Indicator -->
                                <div class="flex items-center bg-green-100 dark:bg-green-900/30 px-2 py-1 rounded-full">
                                    <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse mr-2"></div>
                                    <span class="text-xs font-medium text-green-700 dark:text-green-300">LIVE</span>
                                </div>
                            </div>
                        </div>

                        <!-- Performance Info -->
                        <div class="text-sm text-gray-700 dark:text-gray-300 space-y-2">
                            <div
                                class="flex items-center justify-between bg-gray-50 dark:bg-gray-700/50 px-3 py-2 rounded-lg">
                                <span class="font-medium">Vehicles:</span>
                                <span class="font-bold text-blue-600 dark:text-blue-400" x-text="vehicleCount"></span>
                            </div>
                            <div
                                class="flex items-center justify-between bg-gray-50 dark:bg-gray-700/50 px-3 py-2 rounded-lg">
                                <span class="font-medium">Last Update:</span>
                                <span class="font-mono text-xs text-gray-600 dark:text-gray-400"
                                    x-text="lastUpdateFormatted"></span>
                            </div>

                            <!-- Map Controls -->
                            <div class="mt-4 pt-3 border-t border-gray-200 dark:border-gray-600">

                                <!-- Status Legend -->
                                <div class="space-y-2">
                                    <h5
                                        class="text-xs font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wide">
                                        Vehicle Status</h5>
                                    <div class="space-y-2">
                                        <div
                                            class="flex items-center gap-3 p-2 rounded-lg bg-green-50 dark:bg-green-900/20 transition-colors hover:bg-green-100 dark:hover:bg-green-900/30">
                                            <span class="w-3 h-3 bg-green-500 rounded-full shadow-sm"></span>
                                            <span
                                                class="text-sm font-medium text-green-700 dark:text-green-300">Available</span>
                                        </div>
                                        <div
                                            class="flex items-center gap-3 p-2 rounded-lg bg-red-50 dark:bg-red-900/20 transition-colors hover:bg-red-100 dark:hover:bg-red-900/30">
                                            <span class="w-3 h-3 bg-red-500 rounded-full shadow-sm"></span>
                                            <span class="text-sm font-medium text-red-700 dark:text-red-300">On
                                                Trip</span>
                                        </div>
                                        <div
                                            class="flex items-center gap-3 p-2 rounded-lg bg-gray-50 dark:bg-gray-700/50 transition-colors hover:bg-gray-100 dark:hover:bg-gray-700/70">
                                            <span class="w-3 h-3 bg-gray-500 rounded-full shadow-sm"></span>
                                            <span
                                                class="text-sm font-medium text-gray-700 dark:text-gray-300">Offline</span>
                                        </div>
                                    </div>
                                </div>


                            </div>
                        </div>
                    </div>
                </div>

                <!-- Vehicle Details Modal using Filament Modal Component -->
                <x-filament::modal id="vehicle-details-modal" width="5xl" :close-by-clicking-away="true"
                    :close-by-escaping="true" x-data="{
                        showModal: $wire.entangle('showModal'),
                        init() {
                            this.$watch('showModal', (value) => {
                                if (value) {
                                    this.$dispatch('open-modal', { id: 'vehicle-details-modal' });
                                } else {
                                    this.$dispatch('close-modal', { id: 'vehicle-details-modal' });
                                }
                            });
                        }
                    }" x-on:close-modal.window="if ($event.detail.id === 'vehicle-details-modal') $wire.closeModal()">
                    <x-slot name="heading">
                        <div class="flex items-center gap-3">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            Vehicle Details
                        </div>
                    </x-slot>

                    @if($selectedVehicle)
                        <!-- Modal Content with Tabs -->
                        <div x-data="{ activeTab: 'vehicle' }" class="space-y-4">
                            <!-- Tab Navigation -->
                            <x-filament::tabs label="Vehicle Details Tabs">
                                <x-filament::tabs.item icon="heroicon-o-truck" alpine-active="activeTab === 'vehicle'"
                                    x-on:click="activeTab = 'vehicle'">
                                    Vehicle
                                </x-filament::tabs.item>

                                <x-filament::tabs.item icon="heroicon-o-user" alpine-active="activeTab === 'driver'"
                                    x-on:click="activeTab = 'driver'">
                                    Driver
                                </x-filament::tabs.item>

                                @if(isset($selectedVehicle['current_trip']) && $selectedVehicle['current_trip'])
                                    <x-filament::tabs.item icon="heroicon-o-map-pin" alpine-active="activeTab === 'trip'"
                                        x-on:click="activeTab = 'trip'">
                                        Current Trip
                                    </x-filament::tabs.item>
                                @endif
                            </x-filament::tabs>

                            <!-- Tab Content -->
                            <!-- Vehicle Tab -->
                            <div x-show="activeTab === 'vehicle'">
                                <x-filament::section>
                                    <x-slot name="heading">
                                        <div class="flex items-center gap-2">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            Vehicle Information
                                        </div>
                                    </x-slot>

                                    <div class="flex gap-4">
                                        <!-- Vehicle Image -->
                                        <div class="flex-shrink-0">
                                            <img src="{{ $selectedVehicle['vehicle']['image'] ?? asset('images/vehicle.jpg') }}"
                                                alt="Vehicle {{ $selectedVehicle['vehicle']['model'] ?? 'Unknown' }}"
                                                class="w-24 h-24 rounded-xl object-cover border-2 border-gray-200 dark:border-gray-600 shadow-lg"
                                                onerror="this.src='{{ asset('images/vehicle.jpg') }}'">
                                        </div>
                                        <!-- Vehicle Details -->
                                        <div class="flex-1 grid grid-cols-2 gap-4 text-sm">
                                            <div>
                                                <span class="font-medium text-gray-700 dark:text-gray-300">Model:</span>
                                                <span
                                                    class="ml-2 text-gray-900 dark:text-gray-100">{{ $selectedVehicle['vehicle']['model'] ?? 'Unknown' }}</span>
                                            </div>
                                            <div>
                                                <span class="font-medium text-gray-700 dark:text-gray-300">Brand:</span>
                                                <span
                                                    class="ml-2 text-gray-900 dark:text-gray-100">{{ $selectedVehicle['vehicle']['brand'] ?? 'Unknown' }}</span>
                                            </div>
                                            <div>
                                                <span class="font-medium text-gray-700 dark:text-gray-300">Type:</span>
                                                <span
                                                    class="ml-2 text-gray-900 dark:text-gray-100">{{ $selectedVehicle['vehicle']['type'] ?? 'Unknown' }}</span>
                                            </div>
                                            <div>
                                                <span class="font-medium text-gray-700 dark:text-gray-300">Rating:</span>
                                                <span class="ml-2 text-gray-900 dark:text-gray-100">
                                                    @php
                                                        $vehicleRating = $selectedVehicle['vehicle']['rating'] ?? 0;
                                                        $formattedVehicleRating = $vehicleRating == floor($vehicleRating) ? number_format($vehicleRating, 0) : number_format($vehicleRating, 1);
                                                    @endphp
                                                    {{ $formattedVehicleRating }}
                                                    <span class="text-yellow-500">⭐</span>
                                                </span>
                                            </div>
                                            <div>
                                                <span class="font-medium text-gray-700 dark:text-gray-300">Status:</span>
                                                <span class="ml-2">
                                                    @php
                                                        $statusColors = [
                                                            'available' => 'text-green-600 dark:text-green-400',
                                                            'on trip' => 'text-red-600 dark:text-red-400',
                                                            'blocked' => 'text-red-600 dark:text-red-400',
                                                            'offline' => 'text-gray-600 dark:text-gray-400'
                                                        ];
                                                        $statusColor = $statusColors[$selectedVehicle['status']] ?? 'text-gray-600 dark:text-gray-400';
                                                    @endphp
                                                    <span
                                                        class="font-medium {{ $statusColor }}">{{ ucfirst($selectedVehicle['status'] ?? 'Unknown') }}</span>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </x-filament::section>
                            </div>

                            <!-- Driver Tab -->
                            <div x-show="activeTab === 'driver'">
                                <x-filament::section>
                                    <x-slot name="heading">
                                        <div class="flex items-center gap-2">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z">
                                                </path>
                                            </svg>
                                            Driver Information
                                        </div>
                                    </x-slot>

                                    <div class="flex gap-4">
                                        <!-- Driver Cover Picture -->
                                        <div class="flex-shrink-0">
                                            <img src="{{ $selectedVehicle['driver']['cover_picture'] ?? asset('images/avatar.png') }}"
                                                alt="Driver {{ $selectedVehicle['driver']['name'] ?? 'Unknown' }}"
                                                class="w-24 h-24 rounded-full object-cover border-2 border-gray-200 dark:border-gray-600 shadow-lg"
                                                onerror="this.src='{{ asset('images/avatar.png') }}'">
                                        </div>
                                        <!-- Driver Details -->
                                        <div class="flex-1 grid grid-cols-2 gap-4 text-sm">
                                            <div>
                                                <span class="font-medium text-gray-700 dark:text-gray-300">Name:</span>
                                                <span
                                                    class="ml-2 text-gray-900 dark:text-gray-100">{{ $selectedVehicle['driver']['name'] ?? 'Unknown' }}</span>
                                            </div>
                                            <div>
                                                <span class="font-medium text-gray-700 dark:text-gray-300">Phone:</span>
                                                <span
                                                    class="ml-2 text-gray-900 dark:text-gray-100">{{ $selectedVehicle['driver']['phone'] ?? 'N/A' }}</span>
                                            </div>
                                            <div>
                                                <span class="font-medium text-gray-700 dark:text-gray-300">Gender:</span>
                                                <span
                                                    class="ml-2 text-gray-900 dark:text-gray-100">{{ ucfirst($selectedVehicle['driver']['gender'] ?? 'unknown') }}</span>
                                            </div>
                                            <div>
                                                <span class="font-medium text-gray-700 dark:text-gray-300">Rating:</span>
                                                <span class="ml-2 text-gray-900 dark:text-gray-100">
                                                    @php
                                                        $driverRating = $selectedVehicle['driver']['rating'] ?? 0;
                                                        $formattedDriverRating = $driverRating == floor($driverRating) ? number_format($driverRating, 0) : number_format($driverRating, 1);
                                                    @endphp
                                                    {{ $formattedDriverRating }}
                                                    <span class="text-yellow-500">⭐</span>
                                                </span>
                                            </div>
                                            <div>
                                                <span
                                                    class="font-medium text-gray-700 dark:text-gray-300">Status:</span>
                                                <span class="ml-2">
                                                    @php
                                                        // Show global_status if driver is blocked, otherwise show availability
                                                        $isBlocked = isset($selectedVehicle['driver']['global_status']) && $selectedVehicle['driver']['global_status'] === 'blocked';
                                                        $displayStatus = $isBlocked ? 'blocked' : ($selectedVehicle['availability'] ?? 'Unknown');

                                                        $statusColors = [
                                                            'online' => 'text-green-600 dark:text-green-400',
                                                            'busy' => 'text-red-600 dark:text-red-400',
                                                            'blocked' => 'text-red-600 dark:text-red-400',
                                                            'offline' => 'text-gray-600 dark:text-gray-400'
                                                        ];
                                                        $statusColor = $statusColors[$displayStatus] ?? 'text-gray-600 dark:text-gray-400';
                                                    @endphp
                                                    <span
                                                        class="font-medium {{ $statusColor }}">{{ ucfirst($displayStatus) }}</span>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </x-filament::section>
                            </div>

                            <!-- Trip Tab -->
                            @if(isset($selectedVehicle['current_trip']) && $selectedVehicle['current_trip'])
                                <div x-show="activeTab === 'trip'">
                                    <x-filament::section>
                                        <x-slot name="heading">
                                            <div class="flex items-center gap-2">
                                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                        d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                                </svg>
                                                Current Trip Details
                                            </div>
                                        </x-slot>

                                        <div class="space-y-6">
                                            <!-- Trip Status & Basic Info -->
                                            <div class="grid grid-cols-2 gap-4 text-sm">
                                                <div>
                                                    <span class="font-medium text-gray-700 dark:text-gray-300">Trip ID:</span>
                                                    <span
                                                        class="ml-2 text-gray-900 dark:text-gray-100">{{ $selectedVehicle['current_trip']['id'] }}</span>
                                                </div>
                                                <div>
                                                    <span class="font-medium text-gray-700 dark:text-gray-300">Status:</span>
                                                    <span class="ml-2">
                                                        <x-filament::badge color="primary">
                                                            {{ $selectedVehicle['current_trip']['status_label'] }}
                                                        </x-filament::badge>
                                                    </span>
                                                </div>
                                                <div>
                                                    <span class="font-medium text-gray-700 dark:text-gray-300">Distance:</span>
                                                    <span
                                                        class="ml-2 text-gray-900 dark:text-gray-100">{{ ($selectedVehicle['current_trip']['distance'] ?? 0) . ' km' }}</span>
                                                </div>
                                                @if(isset($selectedVehicle['current_trip']['eta_minutes']) && $selectedVehicle['current_trip']['eta_minutes'] !== null)
                                                    <div>
                                                        <span class="font-medium text-gray-700 dark:text-gray-300">ETA:</span>
                                                        <span
                                                            class="ml-2 text-gray-900 dark:text-gray-100">{{ $selectedVehicle['current_trip']['eta_minutes'] . ' min' }}</span>
                                                    </div>
                                                @endif
                                            </div>

                                            <!-- Pickup & Drop-off -->
                                            <div class="space-y-3">
                                                <div class="flex items-start gap-3">
                                                    <div
                                                        class="flex-shrink-0 w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                                                        <svg class="w-4 h-4 text-green-600 dark:text-green-400" fill="none"
                                                            stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                stroke-width="2"
                                                                d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z">
                                                            </path>
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                        </svg>
                                                    </div>
                                                    <div class="flex-1">
                                                        <span class="font-medium text-gray-700 dark:text-gray-300">Pickup
                                                            Location:</span>
                                                        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                                            {{ $selectedVehicle['current_trip']['departure_address'] }}
                                                        </p>
                                                    </div>
                                                </div>
                                                <div class="flex items-start gap-3">
                                                    <div
                                                        class="flex-shrink-0 w-8 h-8 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center">
                                                        <svg class="w-4 h-4 text-red-600 dark:text-red-400" fill="none"
                                                            stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                stroke-width="2"
                                                                d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z">
                                                            </path>
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                        </svg>
                                                    </div>
                                                    <div class="flex-1">
                                                        <span class="font-medium text-gray-700 dark:text-gray-300">Drop-off
                                                            Location:</span>
                                                        <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                                                            {{ $selectedVehicle['current_trip']['arrival_address'] }}
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Rider Information -->
                                            <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                                                <h6
                                                    class="text-base font-semibold text-gray-900 dark:text-gray-100 mb-3 flex items-center gap-2">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                            d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z">
                                                        </path>
                                                    </svg>
                                                    Rider Details
                                                </h6>
                                                <div class="flex gap-4">
                                                    <!-- Rider Cover Picture -->
                                                    <div class="flex-shrink-0">
                                                        <img src="{{ $selectedVehicle['current_trip']['rider']['cover_picture'] ?? asset('images/avatar.png') }}"
                                                            alt="Rider {{ $selectedVehicle['current_trip']['rider']['name'] ?? 'Unknown' }}"
                                                            class="w-16 h-16 rounded-full object-cover border-2 border-gray-200 dark:border-gray-600 shadow-lg"
                                                            onerror="this.src='{{ asset('images/avatar.png') }}'">
                                                    </div>
                                                    <!-- Rider Details -->
                                                    <div class="flex-1 grid grid-cols-2 gap-3 text-sm">
                                                        <div>
                                                            <span
                                                                class="font-medium text-gray-700 dark:text-gray-300">Name:</span>
                                                            <span
                                                                class="ml-2 text-gray-900 dark:text-gray-100">{{ $selectedVehicle['current_trip']['rider']['name'] ?? 'Unknown' }}</span>
                                                        </div>
                                                        <div>
                                                            <span
                                                                class="font-medium text-gray-700 dark:text-gray-300">Phone:</span>
                                                            <span
                                                                class="ml-2 text-gray-900 dark:text-gray-100">{{ $selectedVehicle['current_trip']['rider']['phone'] ?? 'N/A' }}</span>
                                                        </div>
                                                        <div>
                                                            <span
                                                                class="font-medium text-gray-700 dark:text-gray-300">Gender:</span>
                                                            <span
                                                                class="ml-2 text-gray-900 dark:text-gray-100">{{ ucfirst($selectedVehicle['current_trip']['rider']['gender'] ?? 'unknown') }}</span>
                                                        </div>
                                                        <div>
                                                            <span
                                                                class="font-medium text-gray-700 dark:text-gray-300">Rating:</span>
                                                            <span class="ml-2 text-gray-900 dark:text-gray-100">
                                                                @php
                                                                    $riderRating = $selectedVehicle['current_trip']['rider']['rating'] ?? 0;
                                                                    $formattedRiderRating = $riderRating == floor($riderRating) ? number_format($riderRating, 0) : number_format($riderRating, 1);
                                                                @endphp
                                                                {{ $formattedRiderRating }}
                                                                <span class="text-yellow-500">⭐</span>
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </x-filament::section>
                                </div>
                            @endif
                        </div>
                    @endif
                </x-filament::modal>
            </div>
        </div>
    </x-filament::section>
</x-filament-widgets::widget>

@assets
<script src="https://maps.googleapis.com/maps/api/js?key={{ env('GOOGLE_MAPS_API_KEY') }}&libraries=geometry"></script>
<script src="https://unpkg.com/@googlemaps/markerclusterer/dist/index.min.js"></script>
@endassets