# Icons API Documentation

## Overview

The ZTS application now includes a comprehensive icons system that provides a curated list of SVG icons for use in mobile applications. This system is integrated into the existing configuration API and follows the same architectural patterns as other configuration data.

## API Endpoint

### Get Configuration with Icons

**Endpoint:** `GET /api/config`

**Authentication:** None required (public endpoint)

**Response Structure:**
```json
{
  "status": "success",
  "message": "General configuration data",
  "data": {
    "driver_gender": ["male", "female", "both"],
    "car_brands": [...],
    "seats_number": [2, 4, 6],
    "car_equipments": [...],
    "car_types": [...],
    "available_icons": [
      {
        "icon_name": "heroicon-o-star",
        "display_name": "Star",
        "category": "general",
        "icon_svg": "<svg xmlns=\"http://www.w3.org/2000/svg\"...></svg>"
      },
      {
        "icon_name": "mdi-home",
        "display_name": "Home",
        "category": "location",
        "icon_svg": "<svg fill=\"currentColor\"...></svg>"
      }
    ]
  }
}
```

## Available Icons

### Categories

- **general**: General purpose icons (star, heart, camera)
- **location**: Location-related icons (home, map-pin, park)
- **business**: Business-related icons (office, bank, briefcase)
- **food**: Food and dining icons (coffee, restaurant)
- **transport**: Transportation icons (car, bus, train, airplane)
- **health**: Health and medical icons (hospital, gym)
- **entertainment**: Entertainment icons (stadium)
- **shopping**: Shopping icons (cart, store)

### Icon List

#### Heroicons
- `heroicon-o-star` - Star (general)
- `heroicon-o-map-pin` - Map Pin (location)
- `heroicon-o-heart` - Heart (general)
- `heroicon-o-camera` - Camera (general)
- `heroicon-o-building-storefront` - Store (business)

#### Material Design Icons
- `mdi-home` - Home (location)
- `mdi-briefcase` - Work (business)
- `mdi-coffee` - Coffee (food)
- `mdi-tree` - Park (location)
- `mdi-train` - Train (transport)
- `mdi-office-building` - Office (business)
- `mdi-car-back` - Car (transport)
- `mdi-bus` - Bus (transport)
- `mdi-hospital-building` - Hospital (health)
- `mdi-stadium` - Stadium (entertainment)
- `mdi-bank` - Bank (business)
- `mdi-airplane` - Airport (transport)
- `mdi-cart` - Shopping (shopping)
- `mdi-silverware-fork-knife` - Restaurant (food)
- `mdi-ferry` - Ferry (transport)
- `mdi-dumbbell` - Gym (health)

## Usage in Address Labels

When creating or updating favorite addresses, you can now use any of the available icons:

### Create Favorite Address
```json
POST /api/rider/favorite-addresses
{
  "address": "123 Main Street",
  "latitude": 32.8872,
  "longitude": 13.1913,
  "icon": "mdi-home",
  "label": "Home"
}
```

### Update Address Label
```json
POST /api/rider/addresses/label
{
  "address_id": 123,
  "label": "Work Office",
  "icon": "mdi-briefcase"
}
```

## Validation

The system includes validation to ensure only valid icons are used:

- Icons must be from the predefined list in `config/icons.php`
- Invalid icon names will return a validation error
- The validation rule `ValidIcon` is automatically applied

## Configuration Management

### Adding New Icons

To add new icons, update the `config/icons.php` file:

```php
'available_icons' => [
    // Add new icon
    [
        'icon_name' => 'mdi-new-icon',
        'display_name' => 'New Icon',
        'category' => 'general',
    ],
    // ... existing icons
],
```

### Icon Categories

Categories can be managed in the same configuration file:

```php
'categories' => [
    'general' => 'General',
    'location' => 'Location',
    'business' => 'Business',
    // ... add new categories
],
```

## Technical Implementation

- **Configuration File**: `config/icons.php`
- **Validation Rule**: `app/Rules/ValidIcon.php`
- **Controller**: `app/Http/Controllers/Api/ConfigController.php`
- **Resource**: `app/Http/Resources/RiderPreferencesResource.php`

The system leverages the existing `blade-ui-kit/blade-icons` package for SVG rendering and follows Laravel best practices for configuration management and validation.
