<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Create new available_icons table
        Schema::create('available_icons', function (Blueprint $table) {
            $table->id();
            $table->string('icon_name')->unique(); // e.g., 'mdi-home', 'heroicon-o-star'
            $table->string('display_name'); // e.g., 'Home', 'Star'
            $table->string('category')->default('general'); // e.g., 'location', 'business'
            $table->boolean('status')->default(true); // active/inactive
            $table->timestamps();
        });

        // Migrate existing data from address_label to available_icons
        // Get unique icons from address_label table
        $existingIcons = DB::table('address_label')
            ->select('icon')
            ->whereNotNull('icon')
            ->distinct()
            ->get();

        foreach ($existingIcons as $iconData) {
            if (! empty($iconData->icon)) {
                DB::table('available_icons')->insertOrIgnore([
                    'icon_name' => $iconData->icon,
                    'display_name' => ucfirst(str_replace(['mdi-', 'heroicon-o-', '-'], ['', '', ' '], $iconData->icon)),
                    'category' => 'general',
                    'status' => true,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        }

        // Update address_label table structure
        Schema::table('address_label', function (Blueprint $table) {
            // Add foreign key to available_icons
            $table->unsignedBigInteger('available_icon_id')->nullable();
            $table->foreign('available_icon_id')
                ->references('id')
                ->on('available_icons')
                ->onDelete('set null');
        });

        // Update existing address_label records to reference available_icons
        $addressLabels = DB::table('address_label')->whereNotNull('icon')->get();

        foreach ($addressLabels as $label) {
            $availableIcon = DB::table('available_icons')
                ->where('icon_name', $label->icon)
                ->first();

            if ($availableIcon) {
                DB::table('address_label')
                    ->where('id', $label->id)
                    ->update(['available_icon_id' => $availableIcon->id]);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove foreign key and column from address_label
        Schema::table('address_label', function (Blueprint $table) {
            $table->dropForeign(['available_icon_id']);
            $table->dropColumn('available_icon_id');
        });

        // Drop available_icons table
        Schema::dropIfExists('available_icons');
    }
};
