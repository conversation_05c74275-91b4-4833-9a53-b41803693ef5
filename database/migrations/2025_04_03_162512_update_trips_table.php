<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('trips', function (Blueprint $table) {
            $table->dropIndex('trips_status_index');
            $table->dropColumn('status');

            $table->enum('status', [
                'pending', 'timeout', 'dispatched', 'canceled', 'rejected',
                'assigned', 'driver_arriving', 'driver_arrived', 'no_show',
                'on_trip', 'waiting_for_driver_confirmation', 'completed',
            ])->default('pending');
        });
    }

    public function down(): void
    {
        Schema::table('trips', function (Blueprint $table) {
            $table->dropIndex('trips_status_index');
            $table->dropColumn('status');

            // Reverting back to the original column type (assuming it was string)
            $table->string('status')->default('pending');
        });
    }
};
