<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Use raw SQL to modify the enum constraint without losing data
        DB::statement('ALTER TABLE trip_cancellations DROP CONSTRAINT trip_cancellations_cancelled_by_check');
        DB::statement("ALTER TABLE trip_cancellations ADD CONSTRAINT trip_cancellations_cancelled_by_check CHECK (cancelled_by IN ('rider', 'driver', 'admin'))");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert to original constraint without admin option
        DB::statement('ALTER TABLE trip_cancellations DROP CONSTRAINT trip_cancellations_cancelled_by_check');
        DB::statement("ALTER TABLE trip_cancellations ADD CONSTRAINT trip_cancellations_cancelled_by_check CHECK (cancelled_by IN ('rider', 'driver'))");
    }
};
