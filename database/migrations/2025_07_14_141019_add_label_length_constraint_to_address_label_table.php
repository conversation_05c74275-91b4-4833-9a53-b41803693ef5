<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('address_label', function (Blueprint $table) {
            // Change the label column to have a maximum length of 20 characters
            $table->string('label', 20)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('address_label', function (Blueprint $table) {
            // Revert back to default string length (255)
            $table->string('label')->change();
        });
    }
};
