<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('trip_ratings', function (Blueprint $table) {
            $table->tinyInteger('rider_to_driver_rating')->nullable()->change();
            $table->tinyInteger('rider_to_car_rating')->nullable()->change();
            $table->tinyInteger('driver_to_rider_rating')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('trip_ratings', function (Blueprint $table) {
            $table->tinyInteger('rider_to_driver_rating')->nullable(false)->change();
            $table->tinyInteger('rider_to_car_rating')->nullable(false)->change();
            $table->tinyInteger('driver_to_rider_rating')->nullable(false)->change();
        });
    }
};
