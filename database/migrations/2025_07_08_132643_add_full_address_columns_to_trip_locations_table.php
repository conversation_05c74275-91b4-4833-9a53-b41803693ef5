<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('trip_locations', function (Blueprint $table) {
            $table->text('departure_full_address')->nullable();
            $table->text('arrival_full_address')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('trip_locations', function (Blueprint $table) {
            $table->dropColumn(['departure_full_address', 'arrival_full_address']);
        });
    }
};
