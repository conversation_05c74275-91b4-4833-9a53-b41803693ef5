<?php

namespace Database\Seeders;

use App\Models\AvailableIcon;
use Illuminate\Database\Seeder;

class AvailableIconSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $icons = [
            // Heroicons
            [
                'icon_name' => 'heroicon-o-star',
                'display_name' => 'Star',
                'category' => 'general',
            ],
            [
                'icon_name' => 'heroicon-o-map-pin',
                'display_name' => 'Map Pin',
                'category' => 'location',
            ],
            [
                'icon_name' => 'heroicon-o-heart',
                'display_name' => 'Heart',
                'category' => 'general',
            ],
            [
                'icon_name' => 'heroicon-o-camera',
                'display_name' => 'Camera',
                'category' => 'general',
            ],
            [
                'icon_name' => 'heroicon-o-building-storefront',
                'display_name' => 'Store',
                'category' => 'business',
            ],

            // Material Design Icons
            [
                'icon_name' => 'mdi-home',
                'display_name' => 'Home',
                'category' => 'location',
            ],
            [
                'icon_name' => 'mdi-briefcase',
                'display_name' => 'Work',
                'category' => 'business',
            ],
            [
                'icon_name' => 'mdi-coffee',
                'display_name' => 'Coffee',
                'category' => 'food',
            ],
            [
                'icon_name' => 'mdi-tree',
                'display_name' => 'Park',
                'category' => 'location',
            ],
            [
                'icon_name' => 'mdi-train',
                'display_name' => 'Train',
                'category' => 'transport',
            ],
            [
                'icon_name' => 'mdi-office-building',
                'display_name' => 'Office',
                'category' => 'business',
            ],
            [
                'icon_name' => 'mdi-car-back',
                'display_name' => 'Car',
                'category' => 'transport',
            ],
            [
                'icon_name' => 'mdi-bus',
                'display_name' => 'Bus',
                'category' => 'transport',
            ],
            [
                'icon_name' => 'mdi-hospital-building',
                'display_name' => 'Hospital',
                'category' => 'health',
            ],
            [
                'icon_name' => 'mdi-stadium',
                'display_name' => 'Stadium',
                'category' => 'entertainment',
            ],
            [
                'icon_name' => 'mdi-bank',
                'display_name' => 'Bank',
                'category' => 'business',
            ],
            [
                'icon_name' => 'mdi-airplane',
                'display_name' => 'Airport',
                'category' => 'transport',
            ],
            [
                'icon_name' => 'mdi-cart',
                'display_name' => 'Shopping',
                'category' => 'shopping',
            ],
            [
                'icon_name' => 'mdi-silverware-fork-knife',
                'display_name' => 'Restaurant',
                'category' => 'food',
            ],
            [
                'icon_name' => 'mdi-ferry',
                'display_name' => 'Ferry',
                'category' => 'transport',
            ],
            [
                'icon_name' => 'mdi-dumbbell',
                'display_name' => 'Gym',
                'category' => 'health',
            ],
        ];

        foreach ($icons as $iconData) {
            AvailableIcon::updateOrCreate(
                ['icon_name' => $iconData['icon_name']],
                $iconData
            );
        }
    }
}
