<?php

namespace Database\Factories;

use App\Models\AvailableIcon;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\AvailableIcon>
 */
class AvailableIconFactory extends Factory
{
    protected $model = AvailableIcon::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $icons = [
            'mdi-home' => ['display_name' => 'Home', 'category' => 'location'],
            'mdi-office-building' => ['display_name' => 'Office', 'category' => 'business'],
            'mdi-school' => ['display_name' => 'School', 'category' => 'location'],
            'mdi-hospital' => ['display_name' => 'Hospital', 'category' => 'health'],
            'mdi-shopping' => ['display_name' => 'Shopping', 'category' => 'shopping'],
            'mdi-restaurant' => ['display_name' => 'Restaurant', 'category' => 'food'],
            'mdi-gas-station' => ['display_name' => 'Gas Station', 'category' => 'transport'],
            'mdi-bank' => ['display_name' => 'Bank', 'category' => 'business'],
            'mdi-gym' => ['display_name' => 'Gym', 'category' => 'health'],
            'mdi-star' => ['display_name' => 'Star', 'category' => 'general'],
            'heroicon-o-map-pin' => ['display_name' => 'Map Pin', 'category' => 'location'],
            'heroicon-o-heart' => ['display_name' => 'Heart', 'category' => 'general'],
            'heroicon-o-camera' => ['display_name' => 'Camera', 'category' => 'general'],
            'heroicon-o-building-storefront' => ['display_name' => 'Store', 'category' => 'business'],
        ];

        $iconName = $this->faker->randomElement(array_keys($icons));
        $iconData = $icons[$iconName];

        return [
            'icon_name' => $iconName,
            'display_name' => $iconData['display_name'],
            'category' => $iconData['category'],
            'status' => $this->faker->boolean(90), // 90% chance of being active
        ];
    }

    /**
     * Indicate that the icon should be active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => true,
        ]);
    }

    /**
     * Indicate that the icon should be inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => false,
        ]);
    }

    /**
     * Create an icon with specific category.
     */
    public function category(string $category): static
    {
        return $this->state(fn (array $attributes) => [
            'category' => $category,
        ]);
    }
}
