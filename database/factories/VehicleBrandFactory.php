<?php

namespace Database\Factories;

use App\Models\VehicleBrand;
use Illuminate\Database\Eloquent\Factories\Factory;

class VehicleBrandFactory extends Factory
{
    protected $model = VehicleBrand::class;

    public function definition(): array
    {
        $brands = [
            'Toyota' => 'تويوتا',
            'Ford' => 'فورد',
            'Mercedes-Benz' => 'مرسيدس',
            'BMW' => 'بي إم دبليو',
            'Hyundai' => 'هيونداي',
            'Nissan' => 'نيسان',
            'Chevrolet' => 'شيفروليه',
            'Kia' => 'كيا',
            'Audi' => 'أودي',
            'Lexus' => 'لكزس',
        ];

        $randomBrand = $this->faker->randomElement(array_keys($brands));

        return [
            'name_en' => $randomBrand,
            'name_ar' => $brands[$randomBrand],
        ];
    }
}
