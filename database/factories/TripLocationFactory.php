<?php

namespace Database\Factories;

use App\Models\TripLocation;
use Illuminate\Database\Eloquent\Factories\Factory;

class TripLocationFactory extends Factory
{
    protected $model = TripLocation::class;

    public function definition(): array
    {
        // Coordinates for Libyan cities with more precise and expanded locations
        $libyaCities = [
            'Tripoli' => ['lat' => [32.85, 32.90], 'lng' => [13.16, 13.24]],
            '<PERSON><PERSON><PERSON>' => ['lat' => [32.07, 32.12], 'lng' => [20.03, 20.10]],
            'Misrata' => ['lat' => [32.37, 32.39], 'lng' => [15.08, 15.10]],
            'Zawiya' => ['lat' => [32.75, 32.77], 'lng' => [12.72, 12.74]],
            'Sirte' => ['lat' => [31.19, 31.21], 'lng' => [16.58, 16.60]],
            'Sabha' => ['lat' => [27.03, 27.05], 'lng' => [14.42, 14.44]],
            'Ajdabiya' => ['lat' => [30.75, 30.77], 'lng' => [20.22, 20.24]],
            'Tobruk' => ['lat' => [32.07, 32.09], 'lng' => [23.95, 23.97]],
        ];

        // Select a random city
        $city = fake()->randomElement(array_keys($libyaCities));
        $latRange = $libyaCities[$city]['lat'];
        $lngRange = $libyaCities[$city]['lng'];

        // Generate departure coordinates
        $departureLat = fake()->latitude($latRange[0], $latRange[1]);
        $departureLng = fake()->longitude($lngRange[0], $lngRange[1]);

        // Generate arrival coordinates (within the same city but different location)
        $arrivalLat = fake()->latitude($latRange[0], $latRange[1]);
        $arrivalLng = fake()->longitude($lngRange[0], $lngRange[1]);

        // Expanded sample addresses for Libyan cities with more specific locations
        $addressesByCity = [
            'Tripoli' => [
                'Omar Al-Mukhtar Street, Tripoli',
                'Martyrs Square, Tripoli',
                'Gargaresh Road, Tripoli',
                'Sidi Masri, Tripoli',
                'Souq Al-Jumaa, Tripoli',
                'Hay Al-Andalus, Tripoli',
                'Ben Ashour, Tripoli',
                'Janzour, Tripoli',
                'Ain Zara, Tripoli',
                'Tajoura, Tripoli',
                'Abu Salim, Tripoli',
                'Al-Hadba, Tripoli',
            ],
            'Benghazi' => [
                'Al-Berka, Benghazi',
                'Al-Sabri, Benghazi',
                'Al-Leithi, Benghazi',
                'Al-Hadaiq, Benghazi',
                'Al-Fuwayhat, Benghazi',
                'Al-Keesh, Benghazi',
                'Benina, Benghazi',
                'Al-Salmani, Benghazi',
                'Bouatni, Benghazi',
                'Al-Majouri, Benghazi',
            ],
            'Misrata' => [
                'Tripoli Street, Misrata',
                'Qasr Ahmed, Misrata',
                'Al-Zarrouk, Misrata',
                'Al-Ghiran, Misrata',
                'Al-Dafniya, Misrata',
                'Zawiyat Al-Mahjoub, Misrata',
                'Tomena, Misrata',
                'Al-Jazeera, Misrata',
                'Karzaz, Misrata',
            ],
            'Zawiya' => [
                'Al-Harsha, Zawiya',
                'City Center, Zawiya',
                'Al-Mutared, Zawiya',
                'Al-Jadida, Zawiya',
                'Al-Ajelat, Zawiya',
                'Abu Issa, Zawiya',
                'Al-Zawiya University, Zawiya',
            ],
            'Sirte' => [
                'City Center, Sirte',
                'Al-Zafaran, Sirte',
                'Al-Gharbia, Sirte',
                'Ibn Sina Hospital Area, Sirte',
                'Sirte University, Sirte',
                'Al-Dollar, Sirte',
            ],
            'Sabha' => [
                'City Center, Sabha',
                'Al-Jadid, Sabha',
                'Al-Mahdia, Sabha',
                'Al-Gurda, Sabha',
                'Sabha University, Sabha',
            ],
            'Ajdabiya' => [
                'City Center, Ajdabiya',
                'Al-Fath, Ajdabiya',
                'Al-Intisar, Ajdabiya',
                'Al-Sharqiya, Ajdabiya',
            ],
            'Tobruk' => [
                'City Center, Tobruk',
                'Al-Andalus, Tobruk',
                'Al-Nasr, Tobruk',
                'Al-Inshirah, Tobruk',
                'Tobruk Port, Tobruk',
            ],
        ];

        // Select random addresses for departure and arrival
        $departureAddress = fake()->randomElement($addressesByCity[$city]);

        // For arrival address, pick a different address in the same city
        $arrivalAddresses = array_diff($addressesByCity[$city], [$departureAddress]);
        $arrivalAddress = ! empty($arrivalAddresses)
            ? fake()->randomElement($arrivalAddresses)
            : 'Near '.$departureAddress; // Fallback if only one address in city

        return [
            'departure_address' => $departureAddress,
            'arrival_address' => $arrivalAddress,
            'departure_full_address' => fake()->optional(0.7)->streetAddress().', '.$city.', Libya',
            'arrival_full_address' => fake()->optional(0.7)->streetAddress().', '.$city.', Libya',
            'departure_lat' => $departureLat,
            'departure_lng' => $departureLng,
            'arrival_lat' => $arrivalLat,
            'arrival_lng' => $arrivalLng,
        ];
    }
}
