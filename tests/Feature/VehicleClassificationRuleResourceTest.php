<?php

namespace Tests\Feature;

use App\Enums\VehicleTypesCategories;
use App\Enums\WeightCategoryEnum;
use App\Filament\Resources\Panel\VehicleClassificationRuleResource;
use App\Models\User;
use App\Models\VehicleBrand;
use App\Models\VehicleClassificationRule;
use App\Models\VehicleModel;
use App\Models\VehicleType;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Livewire\Livewire;
use Tests\TestCase;

class VehicleClassificationRuleResourceTest extends TestCase
{
    use RefreshDatabase;

    private User $admin;

    private VehicleType $economyType;

    private VehicleType $comfortType;

    private VehicleType $luxuryType;

    private VehicleType $lightCoveredType;

    private VehicleBrand $brand;

    private VehicleModel $model;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin user
        $this->admin = User::factory()->create([
            'email' => '<EMAIL>',
            'type' => 'admin',
        ]);

        // Create test data
        $this->brand = VehicleBrand::factory()->create(['name_en' => 'Toyota']);
        $this->model = VehicleModel::factory()->create([
            'vehicle_brand_id' => $this->brand->id,
            'name_en' => 'Camry',
        ]);

        $this->economyType = VehicleType::factory()->create([
            'name_en' => 'Economy',
            'category' => VehicleTypesCategories::Passenger,
            'status' => true,
        ]);

        $this->comfortType = VehicleType::factory()->create([
            'name_en' => 'Comfort',
            'category' => VehicleTypesCategories::Passenger,
            'status' => true,
        ]);

        $this->luxuryType = VehicleType::factory()->create([
            'name_en' => 'Luxury',
            'category' => VehicleTypesCategories::Passenger,
            'status' => true,
        ]);

        $this->lightCoveredType = VehicleType::factory()->create([
            'name_en' => 'Light Covered Truck',
            'category' => VehicleTypesCategories::Freight,
            'is_covered' => true,
            'weight_category' => WeightCategoryEnum::LessThan1000kg,
            'status' => true,
        ]);
    }

    /** @test */
    public function admin_can_view_vehicle_classification_rules_index()
    {
        $this->actingAs($this->admin);

        // Create some test rules
        VehicleClassificationRule::factory()->count(3)->create();

        Livewire::test(VehicleClassificationRuleResource\Pages\ListVehicleClassificationRules::class)
            ->assertSuccessful()
            ->assertCanSeeTableRecords(VehicleClassificationRule::all());
    }

    /** @test */
    public function admin_can_create_passenger_vehicle_classification_rule()
    {
        $this->actingAs($this->admin);

        Livewire::test(VehicleClassificationRuleResource\Pages\CreateVehicleClassificationRule::class)
            ->fillForm([
                'category' => VehicleTypesCategories::Passenger->value,
                'vehicle_type_id' => $this->comfortType->id,
                'qualifications' => [
                    [
                        'brands' => [$this->brand->id],
                        'models' => [$this->model->id],
                        'min_year' => 2018,
                        'max_year' => 2024,
                        'seat_numbers' => [4, 5],
                    ],
                ],
            ])
            ->call('create')
            ->assertHasNoFormErrors();

        $this->assertDatabaseHas('vehicle_classification_rules', [
            'vehicle_type_id' => $this->comfortType->id,
            'category' => VehicleTypesCategories::Passenger->value,
        ]);

        $this->assertDatabaseHas('vehicle_classification_qualifications', [
            'min_year' => 2018,
            'max_year' => 2024,
        ]);
    }

    /** @test */
    public function admin_can_create_freight_vehicle_classification_rule()
    {
        $this->actingAs($this->admin);

        Livewire::test(VehicleClassificationRuleResource\Pages\CreateVehicleClassificationRule::class)
            ->fillForm([
                'category' => VehicleTypesCategories::Freight->value,
                'vehicle_type_id' => $this->lightCoveredType->id,
                'qualifications' => [
                    [
                        'brands' => [$this->brand->id],
                        'models' => [$this->model->id],
                        'min_year' => 2015,
                        'max_year' => 2025,
                        'is_covered' => true,
                        'weight_category' => 'less_than_1000kg',
                    ],
                ],
            ])
            ->call('create')
            ->assertHasNoFormErrors();

        $this->assertDatabaseHas('vehicle_classification_rules', [
            'vehicle_type_id' => $this->lightCoveredType->id,
            'category' => VehicleTypesCategories::Freight->value,
        ]);

        $this->assertDatabaseHas('vehicle_classification_qualifications', [
            'is_covered' => true,
            'weight_category' => 'less_than_1000kg',
        ]);
    }

    /** @test */
    public function admin_can_edit_vehicle_classification_rule()
    {
        $this->actingAs($this->admin);

        $rule = VehicleClassificationRule::factory()->create([
            'vehicle_type_id' => $this->economyType->id,
            'category' => VehicleTypesCategories::Passenger,
        ]);

        Livewire::test(VehicleClassificationRuleResource\Pages\EditVehicleClassificationRule::class, [
            'record' => $rule->getRouteKey(),
        ])
            ->fillForm([
                'vehicle_type_id' => $this->comfortType->id,
            ])
            ->call('save')
            ->assertHasNoFormErrors();

        $this->assertDatabaseHas('vehicle_classification_rules', [
            'id' => $rule->id,
            'vehicle_type_id' => $this->comfortType->id,
        ]);
    }

    /** @test */
    public function admin_can_delete_vehicle_classification_rule()
    {
        $this->actingAs($this->admin);

        $rule = VehicleClassificationRule::factory()->create();

        Livewire::test(VehicleClassificationRuleResource\Pages\ListVehicleClassificationRules::class)
            ->callTableAction('delete', $rule)
            ->assertSuccessful();

        $this->assertSoftDeleted('vehicle_classification_rules', [
            'id' => $rule->id,
        ]);
    }

    /** @test */
    public function vehicle_type_options_are_filtered_by_category()
    {
        $this->actingAs($this->admin);

        Livewire::test(VehicleClassificationRuleResource\Pages\CreateVehicleClassificationRule::class)
            ->fillForm([
                'category' => VehicleTypesCategories::Passenger->value,
            ])
            ->assertFormFieldExists('vehicle_type_id')
            ->assertFormFieldIsVisible('vehicle_type_id');
    }

    /** @test */
    public function qualification_fields_are_shown_based_on_category()
    {
        $this->actingAs($this->admin);

        // Test passenger category shows seat numbers
        Livewire::test(VehicleClassificationRuleResource\Pages\CreateVehicleClassificationRule::class)
            ->fillForm([
                'category' => VehicleTypesCategories::Passenger->value,
                'vehicle_type_id' => $this->economyType->id,
            ])
            ->assertFormFieldExists('qualifications.0.seat_numbers')
            ->assertFormFieldIsVisible('qualifications.0.seat_numbers');

        // Test freight category shows covered and weight fields
        Livewire::test(VehicleClassificationRuleResource\Pages\CreateVehicleClassificationRule::class)
            ->fillForm([
                'category' => VehicleTypesCategories::Freight->value,
                'vehicle_type_id' => $this->lightCoveredType->id,
            ])
            ->assertFormFieldExists('qualifications.0.is_covered')
            ->assertFormFieldExists('qualifications.0.weight_category')
            ->assertFormFieldIsVisible('qualifications.0.is_covered')
            ->assertFormFieldIsVisible('qualifications.0.weight_category');
    }

    /** @test */
    public function validation_prevents_invalid_year_ranges()
    {
        $this->actingAs($this->admin);

        Livewire::test(VehicleClassificationRuleResource\Pages\CreateVehicleClassificationRule::class)
            ->fillForm([
                'category' => VehicleTypesCategories::Passenger->value,
                'vehicle_type_id' => $this->economyType->id,
                'qualifications' => [
                    [
                        'min_year' => 2025,
                        'max_year' => 2020, // Invalid: max < min
                        'seat_numbers' => [4],
                    ],
                ],
            ])
            ->call('create')
            ->assertHasFormErrors(['qualifications.0.max_year']);
    }

    /** @test */
    public function multiple_qualifications_can_be_added_to_single_rule()
    {
        $this->actingAs($this->admin);

        $secondBrand = VehicleBrand::factory()->create(['name_en' => 'Honda']);
        $secondModel = VehicleModel::factory()->create([
            'vehicle_brand_id' => $secondBrand->id,
            'name_en' => 'Accord',
        ]);

        Livewire::test(VehicleClassificationRuleResource\Pages\CreateVehicleClassificationRule::class)
            ->fillForm([
                'category' => VehicleTypesCategories::Passenger->value,
                'vehicle_type_id' => $this->comfortType->id,
                'qualifications' => [
                    [
                        'brands' => [$this->brand->id],
                        'models' => [$this->model->id],
                        'min_year' => 2018,
                        'max_year' => 2024,
                        'seat_numbers' => [4, 5],
                    ],
                    [
                        'brands' => [$secondBrand->id],
                        'models' => [$secondModel->id],
                        'min_year' => 2020,
                        'max_year' => 2024,
                        'seat_numbers' => [4],
                    ],
                ],
            ])
            ->call('create')
            ->assertHasNoFormErrors();

        $rule = VehicleClassificationRule::latest()->first();
        $this->assertCount(2, $rule->qualifications()->get());
    }

    /** @test */
    public function validation_service_prevents_duplicate_brand_model_combinations()
    {
        // Create an existing rule with Toyota Camry
        $existingRule = VehicleClassificationRule::create([
            'vehicle_type_id' => $this->comfortType->id,
            'category' => VehicleTypesCategories::Passenger,
        ]);

        $existingRule->qualifications()->create([
            'brands' => [$this->brand->id],
            'models' => [$this->model->id],
            'min_year' => 2018,
            'max_year' => 2024,
            'seat_numbers' => [4, 5],
        ]);

        // Test the validation service directly
        $service = new \App\Services\VehicleClassificationService;

        $qualifications = [
            [
                'brands' => [$this->brand->id], // Same brand
                'models' => [$this->model->id], // Same model
                'min_year' => 2020,
                'max_year' => 2024,
                'seat_numbers' => [4],
            ],
        ];

        $conflicts = $service->validateBrandModelCombinations($qualifications);

        // Should detect conflicts
        $this->assertNotEmpty($conflicts);
        $this->assertCount(1, $conflicts);

        $formattedConflicts = $service->formatValidationConflicts($conflicts);
        $this->assertNotEmpty($formattedConflicts);
        $this->assertStringContainsString('Toyota Camry', $formattedConflicts[0]['message']);
        $this->assertStringContainsString('Comfort', $formattedConflicts[0]['message']);
    }

    /** @test */
    public function validation_service_excludes_current_rule_when_editing()
    {
        // Create two different rules
        $firstRule = VehicleClassificationRule::create([
            'vehicle_type_id' => $this->comfortType->id,
            'category' => VehicleTypesCategories::Passenger,
        ]);

        $firstRule->qualifications()->create([
            'brands' => [$this->brand->id],
            'models' => [$this->model->id],
            'min_year' => 2018,
            'max_year' => 2024,
            'seat_numbers' => [4, 5],
        ]);

        $secondBrand = VehicleBrand::factory()->create(['name_en' => 'BMW']);
        $secondModel = VehicleModel::factory()->create([
            'vehicle_brand_id' => $secondBrand->id,
            'name_en' => 'X5',
        ]);

        $secondRule = VehicleClassificationRule::create([
            'vehicle_type_id' => $this->luxuryType->id,
            'category' => VehicleTypesCategories::Passenger,
        ]);

        $secondRule->qualifications()->create([
            'brands' => [$secondBrand->id],
            'models' => [$secondModel->id],
            'min_year' => 2020,
            'max_year' => 2024,
            'seat_numbers' => [4],
        ]);

        // Test the validation service directly
        $service = new \App\Services\VehicleClassificationService;

        // Test editing second rule to use same brand-model as first (should conflict)
        $qualifications = [
            [
                'brands' => [$this->brand->id], // Same as first rule
                'models' => [$this->model->id], // Same as first rule
                'min_year' => 2020,
                'max_year' => 2024,
                'seat_numbers' => [4],
            ],
        ];

        $conflicts = $service->validateBrandModelCombinations($qualifications, $secondRule->id);
        $this->assertNotEmpty($conflicts); // Should detect conflicts

        // Test editing first rule with its own brand-model (should not conflict)
        $qualifications = [
            [
                'brands' => [$this->brand->id], // Same brand
                'models' => [$this->model->id], // Same model
                'min_year' => 2019, // Different year
                'max_year' => 2025,
                'seat_numbers' => [4, 5],
            ],
        ];

        $conflicts = $service->validateBrandModelCombinations($qualifications, $firstRule->id);
        $this->assertEmpty($conflicts); // Should not detect conflicts when excluding current rule
    }

    /** @test */
    public function validation_method_throws_exception_for_duplicate_brand_model_combination()
    {
        // Create an existing rule with Toyota Camry
        $existingRule = VehicleClassificationRule::create([
            'vehicle_type_id' => $this->comfortType->id,
            'category' => VehicleTypesCategories::Passenger,
        ]);

        $existingRule->qualifications()->create([
            'brands' => [$this->brand->id],
            'models' => [$this->model->id],
            'min_year' => 2018,
            'max_year' => 2024,
            'seat_numbers' => [4, 5],
        ]);

        // Create a mock CreateVehicleClassificationRule page to test validation
        $createPage = new \App\Filament\Resources\Panel\VehicleClassificationRuleResource\Pages\CreateVehicleClassificationRule;

        // Set up the data that would cause a conflict
        $createPage->data = [
            'category' => VehicleTypesCategories::Passenger->value,
            'vehicle_type_id' => $this->luxuryType->id,
            'qualifications' => [
                [
                    'brands' => [$this->brand->id], // Same brand
                    'models' => [$this->model->id], // Same model
                    'min_year' => 2020,
                    'max_year' => 2024,
                    'seat_numbers' => [4],
                ],
            ],
        ];

        // Test that validation throws an exception
        $this->expectException(\Illuminate\Validation\ValidationException::class);

        // Call the validation method directly
        $reflection = new \ReflectionClass($createPage);
        $method = $reflection->getMethod('validateBrandModelCombinations');
        $method->setAccessible(true);
        $method->invoke($createPage);
    }

    /** @test */
    public function validation_logic_detects_duplicate_models_within_same_rule_qualifications()
    {
        // Create a second model for the same brand
        $secondModel = VehicleModel::factory()->create([
            'vehicle_brand_id' => $this->brand->id,
            'name_en' => 'Corolla',
        ]);

        // Test the validation logic directly (simulating what happens in the form)
        $qualifications = [
            [
                'brands' => $this->brand->id,
                'models' => [$this->model->id, $secondModel->id], // Q1: Camry, Corolla
                'min_year' => 2018,
                'max_year' => 2024,
                'seat_numbers' => [4, 5],
            ],
            [
                'brands' => $this->brand->id,
                'models' => [$this->model->id], // Q2: Camry (duplicate from Q1)
                'min_year' => 2020,
                'max_year' => 2024,
                'seat_numbers' => [4],
            ],
        ];

        // Test the validation logic for the second qualification
        $currentIndex = 1;
        $value = [$this->model->id]; // Models for Q2
        $currentBrand = $qualifications[$currentIndex]['brands'];
        $conflicts = [];

        // Check for conflicts with other qualifications
        foreach ($qualifications as $index => $qualification) {
            if ($index === $currentIndex) {
                continue; // Skip current qualification
            }

            $otherBrand = $qualification['brands'] ?? null;
            $otherModels = $qualification['models'] ?? [];

            // If same brand, check for model conflicts
            if ($otherBrand === $currentBrand && ! empty($otherModels)) {
                $conflictingModels = array_intersect($value, $otherModels);
                if (! empty($conflictingModels)) {
                    $modelNames = VehicleModel::whereIn('id', $conflictingModels)
                        ->pluck('name_en')
                        ->toArray();
                    $conflicts = array_merge($conflicts, $modelNames);
                }
            }
        }

        // Should detect conflicts
        $this->assertNotEmpty($conflicts, 'Should detect duplicate models within the same rule');
        $this->assertContains('Camry', $conflicts, 'Should detect Camry as a duplicate model');
    }

    /** @test */
    public function validation_method_throws_exception_for_within_rule_duplicate_models()
    {
        // Create a second model for the same brand
        $secondModel = VehicleModel::factory()->create([
            'vehicle_brand_id' => $this->brand->id,
            'name_en' => 'Corolla',
        ]);

        // Create a mock CreateVehicleClassificationRule page to test validation
        $createPage = new \App\Filament\Resources\Panel\VehicleClassificationRuleResource\Pages\CreateVehicleClassificationRule;

        // Set up the data that would cause within-rule conflicts
        $createPage->data = [
            'category' => VehicleTypesCategories::Passenger->value,
            'vehicle_type_id' => $this->comfortType->id,
            'qualifications' => [
                [
                    'brands' => $this->brand->id,
                    'models' => [$this->model->id, $secondModel->id], // Q1: Camry, Corolla
                    'min_year' => 2018,
                    'max_year' => 2024,
                    'seat_numbers' => [4, 5],
                ],
                [
                    'brands' => $this->brand->id,
                    'models' => [$this->model->id], // Q2: Camry (duplicate from Q1)
                    'min_year' => 2020,
                    'max_year' => 2024,
                    'seat_numbers' => [4],
                ],
            ],
        ];

        // Test that validation throws an exception
        $this->expectException(\Illuminate\Validation\ValidationException::class);

        // Call the validation method directly
        $reflection = new \ReflectionClass($createPage);
        $method = $reflection->getMethod('validateBrandModelCombinations');
        $method->setAccessible(true);
        $method->invoke($createPage);
    }

    /** @test */
    public function admin_can_edit_rule_with_same_brand_model_combination_as_itself()
    {
        $this->actingAs($this->admin);

        // Create a rule
        $rule = VehicleClassificationRule::create([
            'vehicle_type_id' => $this->comfortType->id,
            'category' => VehicleTypesCategories::Passenger,
        ]);

        $rule->qualifications()->create([
            'brands' => [$this->brand->id],
            'models' => [$this->model->id],
            'min_year' => 2018,
            'max_year' => 2024,
            'seat_numbers' => [4, 5],
        ]);

        // Edit the same rule with the same brand-model combination (should be allowed)
        Livewire::test(VehicleClassificationRuleResource\Pages\EditVehicleClassificationRule::class, ['record' => $rule->id])
            ->fillForm([
                'category' => VehicleTypesCategories::Passenger->value,
                'vehicle_type_id' => $this->comfortType->id,
                'qualifications' => [
                    [
                        'brands' => [$this->brand->id], // Same brand
                        'models' => [$this->model->id], // Same model
                        'min_year' => 2019, // Different year range
                        'max_year' => 2025,
                        'seat_numbers' => [4, 5],
                    ],
                ],
            ])
            ->call('save')
            ->assertHasNoFormErrors();

        // Verify the rule was updated successfully
        $rule->refresh();
        $qualification = $rule->qualifications()->first();
        $this->assertEquals(2019, $qualification->min_year);
        $this->assertEquals(2025, $qualification->max_year);
    }
}
