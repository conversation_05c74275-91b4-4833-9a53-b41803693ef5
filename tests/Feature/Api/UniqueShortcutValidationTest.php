<?php

namespace Tests\Feature\Api;

use App\Models\Address;
use App\Models\AddressLabel;
use App\Models\AvailableIcon;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class UniqueShortcutValidationTest extends TestCase
{
    use RefreshDatabase;

    protected $user;

    protected $availableIcon;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a passenger user
        $this->user = User::factory()->create(['type' => 'passenger']);
        Sanctum::actingAs($this->user, ['*']);

        // Create an available icon
        $this->availableIcon = AvailableIcon::factory()->create([
            'icon_name' => 'mdi-home',
            'display_name' => 'Home',
            'category' => 'location',
            'status' => true,
        ]);
    }

    /** @test */
    public function it_prevents_duplicate_label_when_adding_favorite_address()
    {
        // Create an existing shortcut with a label
        $existingAddress = Address::factory()->create([
            'addressable_type' => 'App\Models\User',
            'addressable_id' => $this->user->id,
            'address' => 'Existing Address',
            'latitude' => '35.838177',
            'longitude' => '10.624184',
            'is_favorite' => true,
            'shortcut' => true,
        ]);

        $existingLabel = AddressLabel::factory()->create([
            'label' => 'Home',
            'icon' => 'mdi-home',
            'address_id' => $existingAddress->id,
            'available_icon_id' => $this->availableIcon->id,
        ]);

        $existingAddress->update(['address_label_id' => $existingLabel->id]);

        // Try to create a new favorite address with the same label
        $response = $this->postJson(route('api.rider.favorite-addresses.add'), [
            'address' => 'New Address',
            'full_address' => 'New Full Address',
            'latitude' => 35.840000,
            'longitude' => 10.620000,
            'icon' => 'mdi-home',
            'label' => 'Home', // Same label as existing
        ]);

        // Should fail with validation error
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['label']);

        $this->assertStringContainsString(
            'A shortcut with this label already exists',
            $response->json('errors.label.0')
        );
    }

    /** @test */
    public function it_prevents_duplicate_address_when_adding_favorite_address()
    {
        // Create an existing shortcut
        Address::factory()->create([
            'addressable_type' => 'App\Models\User',
            'addressable_id' => $this->user->id,
            'address' => 'Duplicate Address',
            'latitude' => '35.838177',
            'longitude' => '10.624184',
            'is_favorite' => true,
            'shortcut' => true,
        ]);

        // Try to create a new favorite address with the same address
        $response = $this->postJson(route('api.rider.favorite-addresses.add'), [
            'address' => 'Duplicate Address', // Same address
            'full_address' => 'Different Full Address',
            'latitude' => 35.838177,
            'longitude' => 10.624184,
            'icon' => 'mdi-work',
            'label' => 'Work',
        ]);

        // Should fail with validation error
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['address']);

        $this->assertStringContainsString(
            'A shortcut for this address already exists',
            $response->json('errors.address.0')
        );
    }

    /** @test */
    public function it_prevents_duplicate_coordinates_when_adding_favorite_address()
    {
        // Create an existing shortcut with specific coordinates
        Address::factory()->create([
            'addressable_type' => 'App\Models\User',
            'addressable_id' => $this->user->id,
            'address' => 'Original Address',
            'latitude' => '35.838177',
            'longitude' => '10.624184',
            'is_favorite' => true,
            'shortcut' => true,
        ]);

        // Try to create a new favorite address with very similar coordinates (within tolerance)
        $response = $this->postJson(route('api.rider.favorite-addresses.add'), [
            'address' => 'Different Address Name',
            'full_address' => 'Different Full Address',
            'latitude' => 35.838178, // Very close coordinates
            'longitude' => 10.624185,
            'icon' => 'mdi-work',
            'label' => 'Work',
        ]);

        // Should fail with validation error
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['address']);

        $this->assertStringContainsString(
            'A shortcut for this address already exists',
            $response->json('errors.address.0')
        );
    }

    /** @test */
    public function it_allows_unique_label_and_address_when_adding_favorite_address()
    {
        // Create an existing shortcut
        $existingAddress = Address::factory()->create([
            'addressable_type' => 'App\Models\User',
            'addressable_id' => $this->user->id,
            'address' => 'Existing Address',
            'latitude' => '35.838177',
            'longitude' => '10.624184',
            'is_favorite' => true,
            'shortcut' => true,
        ]);

        $existingLabel = AddressLabel::factory()->create([
            'label' => 'Home',
            'icon' => 'mdi-home',
            'address_id' => $existingAddress->id,
            'available_icon_id' => $this->availableIcon->id,
        ]);

        $existingAddress->update(['address_label_id' => $existingLabel->id]);

        // Create a new favorite address with unique label and address
        $response = $this->postJson(route('api.rider.favorite-addresses.add'), [
            'address' => 'Unique Address',
            'full_address' => 'Unique Full Address',
            'latitude' => 35.840000,
            'longitude' => 10.620000,
            'icon' => 'mdi-work',
            'label' => 'Work', // Different label
        ]);

        // Should succeed
        $response->assertStatus(201)
            ->assertJson([
                'status' => 'success',
                'message' => 'address added to favorite successfully',
            ]);
    }

    /** @test */
    public function it_prevents_duplicate_label_when_updating_address()
    {
        // Create two addresses with different labels
        $address1 = Address::factory()->create([
            'addressable_type' => 'App\Models\User',
            'addressable_id' => $this->user->id,
            'address' => 'Address 1',
            'latitude' => '35.838177',
            'longitude' => '10.624184',
            'is_favorite' => true,
            'shortcut' => true,
        ]);

        $label1 = AddressLabel::factory()->create([
            'label' => 'Home',
            'icon' => 'mdi-home',
            'address_id' => $address1->id,
            'available_icon_id' => $this->availableIcon->id,
        ]);

        $address1->update(['address_label_id' => $label1->id]);

        $address2 = Address::factory()->create([
            'addressable_type' => 'App\Models\User',
            'addressable_id' => $this->user->id,
            'address' => 'Address 2',
            'latitude' => '35.840000',
            'longitude' => '10.620000',
            'is_favorite' => true,
            'shortcut' => true,
        ]);

        $label2 = AddressLabel::factory()->create([
            'label' => 'Work',
            'icon' => 'mdi-work',
            'address_id' => $address2->id,
            'available_icon_id' => $this->availableIcon->id,
        ]);

        $address2->update(['address_label_id' => $label2->id]);

        // Try to update address2's label to be the same as address1's label
        $response = $this->postJson(route('api.rider.favorite-addresses.update'), [
            'address_id' => $address2->id,
            'label' => 'Home', // Same as address1's label
        ]);

        // Should fail with validation error
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['label']);

        $this->assertStringContainsString(
            'A shortcut with this label already exists',
            $response->json('errors.label.0')
        );
    }

    /** @test */
    public function it_allows_updating_address_with_same_label_unchanged()
    {
        // Create an address with a label
        $address = Address::factory()->create([
            'addressable_type' => 'App\Models\User',
            'addressable_id' => $this->user->id,
            'address' => 'Test Address',
            'latitude' => '35.838177',
            'longitude' => '10.624184',
            'is_favorite' => true,
            'shortcut' => true,
        ]);

        $label = AddressLabel::factory()->create([
            'label' => 'Home',
            'icon' => 'mdi-home',
            'address_id' => $address->id,
            'available_icon_id' => $this->availableIcon->id,
        ]);

        $address->update(['address_label_id' => $label->id]);

        // Update the address but keep the same label
        $response = $this->postJson(route('api.rider.favorite-addresses.update'), [
            'address_id' => $address->id,
            'label' => 'Home', // Same label
            'icon' => 'mdi-home-variant',
        ]);

        // Should succeed
        $response->assertStatus(200)
            ->assertJson([
                'status' => 'success',
                'message' => 'Label updated successfully',
            ]);
    }

    /** @test */
    public function it_prevents_setting_shortcut_for_duplicate_address()
    {
        // Create an existing shortcut
        Address::factory()->create([
            'addressable_type' => 'App\Models\User',
            'addressable_id' => $this->user->id,
            'address' => 'Duplicate Address',
            'latitude' => '35.838177',
            'longitude' => '10.624184',
            'is_favorite' => true,
            'shortcut' => true,
        ]);

        // Create another address with the same address text but not a shortcut yet
        $newAddress = Address::factory()->create([
            'addressable_type' => 'App\Models\User',
            'addressable_id' => $this->user->id,
            'address' => 'Duplicate Address', // Same address
            'latitude' => '35.838177',
            'longitude' => '10.624184',
            'is_favorite' => true,
            'shortcut' => false, // Not a shortcut yet
        ]);

        // Try to set this address as a shortcut
        $response = $this->postJson(route('api.address.set-shortcut', $newAddress->id));

        // Should fail with conflict error
        $response->assertStatus(409)
            ->assertJson([
                'status' => 'error',
                'message' => 'A shortcut for this address already exists',
            ]);
    }

    /** @test */
    public function it_prevents_setting_shortcut_for_duplicate_label()
    {
        // Create an existing shortcut with a label
        $existingAddress = Address::factory()->create([
            'addressable_type' => 'App\Models\User',
            'addressable_id' => $this->user->id,
            'address' => 'Existing Address',
            'latitude' => '35.838177',
            'longitude' => '10.624184',
            'is_favorite' => true,
            'shortcut' => true,
        ]);

        $existingLabel = AddressLabel::factory()->create([
            'label' => 'Home',
            'icon' => 'mdi-home',
            'address_id' => $existingAddress->id,
            'available_icon_id' => $this->availableIcon->id,
        ]);

        $existingAddress->update(['address_label_id' => $existingLabel->id]);

        // Create another address with the same label but not a shortcut yet
        $newAddress = Address::factory()->create([
            'addressable_type' => 'App\Models\User',
            'addressable_id' => $this->user->id,
            'address' => 'Different Address',
            'latitude' => '35.840000',
            'longitude' => '10.620000',
            'is_favorite' => true,
            'shortcut' => false, // Not a shortcut yet
        ]);

        $newLabel = AddressLabel::factory()->create([
            'label' => 'Home', // Same label as existing
            'icon' => 'mdi-home',
            'address_id' => $newAddress->id,
            'available_icon_id' => $this->availableIcon->id,
        ]);

        $newAddress->update(['address_label_id' => $newLabel->id]);

        // Try to set this address as a shortcut
        $response = $this->postJson(route('api.address.set-shortcut', $newAddress->id));

        // Should fail with conflict error
        $response->assertStatus(409)
            ->assertJson([
                'status' => 'error',
                'message' => 'A shortcut with this label already exists',
            ]);
    }

    /** @test */
    public function it_allows_setting_shortcut_for_unique_address_and_label()
    {
        // Create an existing shortcut
        $existingAddress = Address::factory()->create([
            'addressable_type' => 'App\Models\User',
            'addressable_id' => $this->user->id,
            'address' => 'Existing Address',
            'latitude' => '35.838177',
            'longitude' => '10.624184',
            'is_favorite' => true,
            'shortcut' => true,
        ]);

        $existingLabel = AddressLabel::factory()->create([
            'label' => 'Home',
            'icon' => 'mdi-home',
            'address_id' => $existingAddress->id,
            'available_icon_id' => $this->availableIcon->id,
        ]);

        $existingAddress->update(['address_label_id' => $existingLabel->id]);

        // Create another address with unique address and label
        $newAddress = Address::factory()->create([
            'addressable_type' => 'App\Models\User',
            'addressable_id' => $this->user->id,
            'address' => 'Unique Address',
            'latitude' => '35.840000',
            'longitude' => '10.620000',
            'is_favorite' => true,
            'shortcut' => false, // Not a shortcut yet
        ]);

        $newLabel = AddressLabel::factory()->create([
            'label' => 'Work', // Different label
            'icon' => 'mdi-work',
            'address_id' => $newAddress->id,
            'available_icon_id' => $this->availableIcon->id,
        ]);

        $newAddress->update(['address_label_id' => $newLabel->id]);

        // Try to set this address as a shortcut
        $response = $this->postJson(route('api.address.set-shortcut', $newAddress->id));

        // Should succeed
        $response->assertStatus(200)
            ->assertJson([
                'status' => 'success',
                'message' => 'Address shortcut set successfully',
            ]);

        // Verify the address is now a shortcut
        $this->assertTrue($newAddress->fresh()->shortcut);
    }

    /** @test */
    public function it_allows_different_users_to_have_same_labels_and_addresses()
    {
        // Create another user
        $otherUser = User::factory()->create(['type' => 'passenger']);

        // Create a shortcut for the first user
        $address1 = Address::factory()->create([
            'addressable_type' => 'App\Models\User',
            'addressable_id' => $this->user->id,
            'address' => 'Same Address',
            'latitude' => '35.838177',
            'longitude' => '10.624184',
            'is_favorite' => true,
            'shortcut' => true,
        ]);

        $label1 = AddressLabel::factory()->create([
            'label' => 'Home',
            'icon' => 'mdi-home',
            'address_id' => $address1->id,
            'available_icon_id' => $this->availableIcon->id,
        ]);

        $address1->update(['address_label_id' => $label1->id]);

        // Switch to the other user
        Sanctum::actingAs($otherUser, ['*']);

        // Create a favorite address for the second user with the same label and address
        $response = $this->postJson(route('api.rider.favorite-addresses.add'), [
            'address' => 'Same Address', // Same address as first user
            'full_address' => 'Same Full Address',
            'latitude' => 35.838177,
            'longitude' => 10.624184,
            'icon' => 'mdi-home',
            'label' => 'Home', // Same label as first user
        ]);

        // Should succeed because it's a different user
        $response->assertStatus(201)
            ->assertJson([
                'status' => 'success',
                'message' => 'address added to favorite successfully',
            ]);
    }

    /** @test */
    public function it_prevents_labels_exceeding_20_characters_when_adding_favorite_address()
    {
        $longLabel = 'This is a very long label that exceeds twenty characters';

        $response = $this->postJson(route('api.rider.favorite-addresses.add'), [
            'address' => 'Test Address',
            'full_address' => 'Test Full Address',
            'latitude' => 35.838177,
            'longitude' => 10.624184,
            'icon' => 'mdi-home',
            'label' => $longLabel, // 56 characters
        ]);

        // Should fail with validation error
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['label']);

        $this->assertStringContainsString(
            'must not be greater than 20 characters',
            $response->json('errors.label.0')
        );
    }

    /** @test */
    public function it_allows_labels_with_exactly_20_characters_when_adding_favorite_address()
    {
        $exactLabel = '12345678901234567890'; // Exactly 20 characters

        $response = $this->postJson(route('api.rider.favorite-addresses.add'), [
            'address' => 'Test Address',
            'full_address' => 'Test Full Address',
            'latitude' => 35.838177,
            'longitude' => 10.624184,
            'icon' => 'mdi-home',
            'label' => $exactLabel,
        ]);

        // Should succeed
        $response->assertStatus(201)
            ->assertJson([
                'status' => 'success',
                'message' => 'address added to favorite successfully',
            ]);
    }

    /** @test */
    public function it_prevents_labels_exceeding_20_characters_when_updating_address()
    {
        // Create an address first
        $address = Address::factory()->create([
            'addressable_type' => 'App\Models\User',
            'addressable_id' => $this->user->id,
            'address' => 'Test Address',
            'latitude' => '35.838177',
            'longitude' => '10.624184',
            'is_favorite' => true,
            'shortcut' => true,
        ]);

        $label = AddressLabel::factory()->create([
            'label' => 'Home',
            'icon' => 'mdi-home',
            'address_id' => $address->id,
            'available_icon_id' => $this->availableIcon->id,
        ]);

        $address->update(['address_label_id' => $label->id]);

        $longLabel = 'This is another very long label that exceeds the limit';

        // Try to update with a long label
        $response = $this->postJson(route('api.rider.favorite-addresses.update'), [
            'address_id' => $address->id,
            'label' => $longLabel, // 54 characters
        ]);

        // Should fail with validation error
        $response->assertStatus(422)
            ->assertJsonValidationErrors(['label']);

        $this->assertStringContainsString(
            'must not be greater than 20 characters',
            $response->json('errors.label.0')
        );
    }

    /** @test */
    public function it_allows_labels_with_20_characters_or_less_when_updating_address()
    {
        // Create an address first
        $address = Address::factory()->create([
            'addressable_type' => 'App\Models\User',
            'addressable_id' => $this->user->id,
            'address' => 'Test Address',
            'latitude' => '35.838177',
            'longitude' => '10.624184',
            'is_favorite' => true,
            'shortcut' => true,
        ]);

        $label = AddressLabel::factory()->create([
            'label' => 'Home',
            'icon' => 'mdi-home',
            'address_id' => $address->id,
            'available_icon_id' => $this->availableIcon->id,
        ]);

        $address->update(['address_label_id' => $label->id]);

        $validLabel = 'Valid Label'; // 11 characters

        // Try to update with a valid label
        $response = $this->postJson(route('api.rider.favorite-addresses.update'), [
            'address_id' => $address->id,
            'label' => $validLabel,
        ]);

        // Should succeed
        $response->assertStatus(200)
            ->assertJson([
                'status' => 'success',
                'message' => 'Label updated successfully',
            ]);
    }
}
