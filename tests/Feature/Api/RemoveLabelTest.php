<?php

namespace Tests\Feature\Api;

use App\Models\Address;
use App\Models\AddressLabel;
use App\Models\AvailableIcon;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class RemoveLabelTest extends TestCase
{
    use RefreshDatabase;

    protected $user;

    protected $address;

    protected $addressLabel;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a passenger user
        $this->user = User::factory()->create(['type' => 'passenger']);
        Sanctum::actingAs($this->user, ['*']);

        // Create an available icon
        $availableIcon = AvailableIcon::factory()->create([
            'icon_name' => 'mdi-home',
            'display_name' => 'Home',
            'category' => 'location',
            'status' => true,
        ]);

        // Create an address with shortcut enabled
        $this->address = Address::factory()->create([
            'addressable_type' => 'App\Models\User',
            'addressable_id' => $this->user->id,
            'address' => 'Test Address',
            'latitude' => '35.838177',
            'longitude' => '10.624184',
            'shortcut' => true,
        ]);

        // Create address label
        $this->addressLabel = AddressLabel::factory()->create([
            'label' => 'Home',
            'icon' => 'mdi-home',
            'address_id' => $this->address->id,
            'available_icon_id' => $availableIcon->id,
        ]);

        // Update address with label ID
        $this->address->update(['address_label_id' => $this->addressLabel->id]);
    }

    /** @test */
    public function it_removes_label_and_sets_shortcut_to_false()
    {
        // Verify initial state
        $this->assertTrue($this->address->fresh()->shortcut);
        $this->assertDatabaseHas('address_label', ['id' => $this->addressLabel->id]);

        // Make the API call
        $response = $this->deleteJson(route('api.rider.favorite-addresses.label.remove', $this->address->id));

        // Assert response
        $response->assertOk()
            ->assertJson([
                'status' => 'success',
                'message' => 'Label removed successfully',
                'data' => null,
            ]);

        // Verify the address shortcut is set to false
        $this->assertFalse($this->address->fresh()->shortcut);

        // Verify the address label is deleted
        $this->assertDatabaseMissing('address_label', ['id' => $this->addressLabel->id]);

        // Verify the address still exists
        $this->assertDatabaseHas('addresses', ['id' => $this->address->id]);
    }

    /** @test */
    public function it_returns_unauthorized_for_address_not_owned_by_user()
    {
        // Create another user and their address
        $otherUser = User::factory()->create(['type' => 'passenger']);
        $otherAddress = Address::factory()->create([
            'addressable_type' => 'App\Models\User',
            'addressable_id' => $otherUser->id,
            'shortcut' => true,
        ]);

        // Try to remove label from other user's address
        $response = $this->deleteJson(route('api.rider.favorite-addresses.label.remove', $otherAddress->id));

        // Assert unauthorized response
        $response->assertStatus(403)
            ->assertJson([
                'status' => 'error',
                'message' => 'Unauthorized access',
            ]);

        // Verify the other user's address is unchanged
        $this->assertTrue($otherAddress->fresh()->shortcut);
    }

    /** @test */
    public function it_returns_unauthorized_for_non_existent_address()
    {
        $nonExistentAddressId = 99999;

        // Try to remove label from non-existent address
        $response = $this->deleteJson(route('api.rider.favorite-addresses.label.remove', $nonExistentAddressId));

        // Assert unauthorized response
        $response->assertStatus(403)
            ->assertJson([
                'status' => 'error',
                'message' => 'Unauthorized access',
            ]);
    }

    /** @test */
    public function it_handles_address_without_label_gracefully()
    {
        // Create an address without a label but with shortcut enabled
        $addressWithoutLabel = Address::factory()->create([
            'addressable_type' => 'App\Models\User',
            'addressable_id' => $this->user->id,
            'address' => 'Address Without Label',
            'shortcut' => true,
            'address_label_id' => null,
        ]);

        // Make the API call
        $response = $this->deleteJson(route('api.rider.favorite-addresses.label.remove', $addressWithoutLabel->id));

        // Assert response is successful
        $response->assertOk()
            ->assertJson([
                'status' => 'success',
                'message' => 'Label removed successfully',
            ]);

        // Verify the address shortcut is set to false
        $this->assertFalse($addressWithoutLabel->fresh()->shortcut);

        // Verify the address still exists
        $this->assertDatabaseHas('addresses', ['id' => $addressWithoutLabel->id]);
    }

    /** @test */
    public function it_handles_address_with_shortcut_already_false()
    {
        // Set shortcut to false initially
        $this->address->update(['shortcut' => false]);

        // Make the API call
        $response = $this->deleteJson(route('api.rider.favorite-addresses.label.remove', $this->address->id));

        // Assert response is successful
        $response->assertOk()
            ->assertJson([
                'status' => 'success',
                'message' => 'Label removed successfully',
            ]);

        // Verify the address shortcut remains false
        $this->assertFalse($this->address->fresh()->shortcut);

        // Verify the address label is deleted
        $this->assertDatabaseMissing('address_label', ['id' => $this->addressLabel->id]);
    }

    /** @test */
    public function it_only_affects_the_specified_address()
    {
        // Create another address for the same user
        $anotherAddress = Address::factory()->create([
            'addressable_type' => 'App\Models\User',
            'addressable_id' => $this->user->id,
            'address' => 'Another Address',
            'shortcut' => true,
        ]);

        $anotherLabel = AddressLabel::factory()->create([
            'label' => 'Work',
            'icon' => 'mdi-office-building',
            'address_id' => $anotherAddress->id,
        ]);

        // Remove label from the first address
        $response = $this->deleteJson(route('api.rider.favorite-addresses.label.remove', $this->address->id));

        // Assert response is successful
        $response->assertOk();

        // Verify only the first address is affected
        $this->assertFalse($this->address->fresh()->shortcut);
        $this->assertDatabaseMissing('address_label', ['id' => $this->addressLabel->id]);

        // Verify the second address is unchanged
        $this->assertTrue($anotherAddress->fresh()->shortcut);
        $this->assertDatabaseHas('address_label', ['id' => $anotherLabel->id]);
    }
}
