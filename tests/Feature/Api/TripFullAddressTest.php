<?php

namespace Tests\Feature\Api;

use App\Models\Area;
use App\Models\PricingRules;
use App\Models\Rider;
use App\Models\Trip;
use App\Models\User;
use App\Models\VehicleType;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Lara<PERSON>\Sanctum\Sanctum;
use Tests\TestCase;

class TripFullAddressTest extends TestCase
{
    use RefreshDatabase;

    protected $user;

    protected $rider;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->createTestData();

        // Create user and rider
        $this->user = User::factory()->create();
        $this->rider = Rider::factory()->create(['user_id' => $this->user->id]);
    }

    private function createTestData()
    {
        // Create global pricing rules
        PricingRules::create([
            'global_base_price' => 10.00,
            'global_price_per_km' => 30.00,
            'time_threshold_percentage' => 20.00,
        ]);

        // Create vehicle type
        VehicleType::create([
            'name_en' => 'Economy',
            'name_ar' => 'اقتصادي',
            'base_fare_adjustment_type' => 'percentage',
            'distance_fare_adjustment_type' => 'percentage',
            'base_fare_adjustment' => 0,
            'distance_fare_adjustment' => 0,
            'additional_base_fare' => 0,
            'additional_price_per_km' => 0,
        ]);

        // Create area
        Area::create([
            'name_en' => 'Test Area',
            'name_ar' => 'منطقة اختبار',
            'is_active' => true,
            'polygon' => [
                ['lat' => 32.0, 'lng' => 13.0],
                ['lat' => 33.0, 'lng' => 13.0],
                ['lat' => 33.0, 'lng' => 14.0],
                ['lat' => 32.0, 'lng' => 14.0],
            ],
        ]);
    }

    /** @test */
    public function test_trip_creation_with_full_address_fields()
    {
        // Authenticate as the user
        Sanctum::actingAs($this->user, [], 'web');

        $tripData = [
            'departure_location' => [
                'latitude' => 32.888736,
                'longitude' => 13.359754,
                'address' => 'Vicomte',
                'full_address' => 'Vicomte Street, Tripoli, Libya',
            ],
            'arrival_location' => [
                'latitude' => 32.868404,
                'longitude' => 13.389752,
                'address' => 'ksar hlel',
                'full_address' => 'Ksar Hlel District, Tripoli, Libya',
            ],
            'rider_notes' => 'AZERTYUI',
            'vehicle_category' => 'freight',
            'is_covered' => false,
            'weight_category' => 'less_than_1000kg',
        ];

        $response = $this->postJson('/api/trips', $tripData);

        $response->assertStatus(201);
        $response->assertJsonStructure([
            'data' => [
                'id',
                'departureAddress' => [
                    'departure_address',
                    'departure_full_address',
                    'latitude',
                    'longitude',
                ],
                'arrivalAddress' => [
                    'arrival_address',
                    'arrival_full_address',
                    'latitude',
                    'longitude',
                ],
            ],
        ]);

        // Verify the full addresses are stored correctly
        $responseData = $response->json('data');
        $this->assertEquals('Vicomte Street, Tripoli, Libya', $responseData['departureAddress']['departure_full_address']);
        $this->assertEquals('Ksar Hlel District, Tripoli, Libya', $responseData['arrivalAddress']['arrival_full_address']);

        // Verify in database
        $trip = Trip::find($responseData['id']);
        $this->assertNotNull($trip->tripLocation);
        $this->assertEquals('Vicomte Street, Tripoli, Libya', $trip->tripLocation->departure_full_address);
        $this->assertEquals('Ksar Hlel District, Tripoli, Libya', $trip->tripLocation->arrival_full_address);
    }

    /** @test */
    public function test_trip_creation_without_full_address_fields()
    {
        // Authenticate as the user
        Sanctum::actingAs($this->user, [], 'web');

        $tripData = [
            'departure_location' => [
                'latitude' => 32.888736,
                'longitude' => 13.359754,
                'address' => 'Vicomte',
                // No full_address provided
            ],
            'arrival_location' => [
                'latitude' => 32.868404,
                'longitude' => 13.389752,
                'address' => 'ksar hlel',
                // No full_address provided
            ],
            'rider_notes' => 'AZERTYUI',
            'vehicle_category' => 'freight',
            'is_covered' => false,
            'weight_category' => 'less_than_1000kg',
        ];

        $response = $this->postJson('/api/trips', $tripData);

        $response->assertStatus(201);

        // Verify the full addresses are null when not provided
        $responseData = $response->json('data');
        $this->assertNull($responseData['departureAddress']['departure_full_address']);
        $this->assertNull($responseData['arrivalAddress']['arrival_full_address']);

        // Verify in database
        $trip = Trip::find($responseData['id']);
        $this->assertNull($trip->tripLocation->departure_full_address);
        $this->assertNull($trip->tripLocation->arrival_full_address);
    }

    /** @test */
    public function test_trip_show_includes_full_address_fields()
    {
        // Create a trip with full addresses
        $trip = Trip::factory()->create(['rider_id' => $this->rider->id]);

        // Authenticate as the user
        Sanctum::actingAs($this->user, [], 'web');

        $response = $this->getJson("/api/trips/{$trip->id}");

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data' => [
                'departureAddress' => [
                    'departure_address',
                    'departure_full_address',
                ],
                'arrivalAddress' => [
                    'arrival_address',
                    'arrival_full_address',
                ],
            ],
        ]);
    }
}
