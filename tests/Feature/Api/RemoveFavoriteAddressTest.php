<?php

namespace Tests\Feature\Api;

use App\Models\Address;
use App\Models\AddressLabel;
use App\Models\AvailableIcon;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class RemoveFavoriteAddressTest extends TestCase
{
    use RefreshDatabase;

    private User $user;

    private Address $address;

    private AddressLabel $addressLabel;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a passenger user
        $this->user = User::factory()->create(['type' => 'passenger']);
        Sanctum::actingAs($this->user, ['*']);

        // Create an available icon
        $availableIcon = AvailableIcon::factory()->create([
            'icon_name' => 'mdi-home',
            'display_name' => 'Home',
            'category' => 'location',
            'status' => true,
        ]);

        // Create an address that is BOTH favorite AND shortcut
        $this->address = Address::factory()->create([
            'addressable_type' => 'App\Models\User',
            'addressable_id' => $this->user->id,
            'address' => 'Test Favorite Address',
            'latitude' => '35.838177',
            'longitude' => '10.624184',
            'is_favorite' => true,  // This is a favorite
            'shortcut' => true,     // This is also a shortcut
        ]);

        // Create an address label
        $this->addressLabel = AddressLabel::factory()->create([
            'label' => 'Home',
            'icon' => 'mdi-home',
            'address_id' => $this->address->id,
            'available_icon_id' => $availableIcon->id,
        ]);

        // Update address with label ID
        $this->address->update(['address_label_id' => $this->addressLabel->id]);
    }

    /** @test */
    public function it_removes_favorite_address_and_linked_shortcut_automatically()
    {
        // Verify initial state - address is both favorite AND shortcut
        $this->assertTrue($this->address->fresh()->is_favorite);
        $this->assertTrue($this->address->fresh()->shortcut);

        // Make the API call to remove favorite address
        $response = $this->deleteJson(route('api.address.remove-shortcut', $this->address->id));

        // Assert response is successful
        $response->assertOk()
            ->assertJson([
                'status' => 'success',
                'message' => 'Favorite address removed successfully',
            ]);

        // Verify BOTH flags are set to false
        $updatedAddress = $this->address->fresh();
        $this->assertFalse($updatedAddress->is_favorite, 'is_favorite should be false');
        $this->assertFalse($updatedAddress->shortcut, 'shortcut should be false');

        // Verify the address still exists (not deleted, just unfavorited)
        $this->assertDatabaseHas('addresses', ['id' => $this->address->id]);
    }

    /** @test */
    public function it_removes_favorite_address_when_shortcut_is_already_false()
    {
        // Set shortcut to false initially, but keep as favorite
        $this->address->update(['shortcut' => false]);

        // Verify initial state
        $this->assertTrue($this->address->fresh()->is_favorite);
        $this->assertFalse($this->address->fresh()->shortcut);

        // Make the API call
        $response = $this->deleteJson(route('api.address.remove-shortcut', $this->address->id));

        // Assert response is successful
        $response->assertOk()
            ->assertJson([
                'status' => 'success',
                'message' => 'Favorite address removed successfully',
            ]);

        // Verify both flags are false
        $updatedAddress = $this->address->fresh();
        $this->assertFalse($updatedAddress->is_favorite);
        $this->assertFalse($updatedAddress->shortcut);
    }

    /** @test */
    public function it_removes_shortcut_when_is_favorite_is_already_false()
    {
        // Set is_favorite to false initially, but keep as shortcut
        $this->address->update(['is_favorite' => false]);

        // Verify initial state
        $this->assertFalse($this->address->fresh()->is_favorite);
        $this->assertTrue($this->address->fresh()->shortcut);

        // Make the API call
        $response = $this->deleteJson(route('api.address.remove-shortcut', $this->address->id));

        // Assert response is successful
        $response->assertOk();

        // Verify both flags are false
        $updatedAddress = $this->address->fresh();
        $this->assertFalse($updatedAddress->is_favorite);
        $this->assertFalse($updatedAddress->shortcut);
    }

    /** @test */
    public function it_returns_unauthorized_for_address_not_owned_by_user()
    {
        // Create another user and their address
        $otherUser = User::factory()->create(['type' => 'passenger']);
        $otherAddress = Address::factory()->create([
            'addressable_type' => 'App\Models\User',
            'addressable_id' => $otherUser->id,
            'is_favorite' => true,
            'shortcut' => true,
        ]);

        // Try to remove favorite from other user's address
        $response = $this->deleteJson(route('api.address.remove-shortcut', $otherAddress->id));

        // Assert unauthorized response
        $response->assertStatus(404)
            ->assertJson([
                'status' => 'error',
                'message' => 'Address not found or does not belong to you',
            ]);

        // Verify the other user's address is unchanged
        $this->assertTrue($otherAddress->fresh()->is_favorite);
        $this->assertTrue($otherAddress->fresh()->shortcut);
    }

    /** @test */
    public function it_returns_not_found_for_non_existent_address()
    {
        $nonExistentAddressId = 99999;

        // Try to remove favorite from non-existent address
        $response = $this->deleteJson(route('api.address.remove-shortcut', $nonExistentAddressId));

        // Assert not found response
        $response->assertStatus(404)
            ->assertJson([
                'status' => 'error',
                'message' => 'Address not found or does not belong to you',
            ]);
    }

    /** @test */
    public function it_only_affects_the_specified_address()
    {
        // Create another favorite+shortcut address for the same user
        $anotherAddress = Address::factory()->create([
            'addressable_type' => 'App\Models\User',
            'addressable_id' => $this->user->id,
            'address' => 'Another Favorite Address',
            'is_favorite' => true,
            'shortcut' => true,
        ]);

        // Remove favorite from the first address only
        $response = $this->deleteJson(route('api.address.remove-shortcut', $this->address->id));

        // Assert response is successful
        $response->assertOk();

        // Verify only the first address is affected
        $this->assertFalse($this->address->fresh()->is_favorite);
        $this->assertFalse($this->address->fresh()->shortcut);

        // Verify the second address is unchanged
        $this->assertTrue($anotherAddress->fresh()->is_favorite);
        $this->assertTrue($anotherAddress->fresh()->shortcut);
    }

    /** @test */
    public function it_returns_unauthorized_for_non_passenger_user()
    {
        // Create a driver user
        $driverUser = User::factory()->create(['type' => 'driver']);
        Sanctum::actingAs($driverUser, ['*']);

        // Try to remove favorite address as driver
        $response = $this->deleteJson(route('api.address.remove-shortcut', $this->address->id));

        // Assert unauthorized response
        $response->assertStatus(403)
            ->assertJson([
                'status' => 'error',
                'message' => 'Unauthorized access',
            ]);

        // Verify the address is unchanged
        $this->assertTrue($this->address->fresh()->is_favorite);
        $this->assertTrue($this->address->fresh()->shortcut);
    }

    /** @test */
    public function integration_test_exact_user_scenario()
    {
        // This test replicates the exact scenario described in the issue:
        // 1. Rider is logged in
        // 2. A favorite address is saved
        // 3. A shortcut is created and linked to that favorite address
        // 4. Rider deletes the favorite address
        // 5. The linked shortcut should also be deleted automatically

        // Step 1: Rider is logged in (already done in setUp)

        // Step 2 & 3: Create a favorite address that is also a shortcut (already done in setUp)
        // Verify initial state
        $this->assertTrue($this->address->fresh()->is_favorite, 'Address should be favorite initially');
        $this->assertTrue($this->address->fresh()->shortcut, 'Address should be shortcut initially');

        // Step 4: Rider deletes the favorite address
        $response = $this->deleteJson(route('api.address.remove-shortcut', $this->address->id));
        $response->assertOk();

        // Step 5: Verify both favorite and shortcut are removed
        $updatedAddress = $this->address->fresh();
        $this->assertFalse($updatedAddress->is_favorite, 'Favorite address should be deleted');
        $this->assertFalse($updatedAddress->shortcut, 'Linked shortcut should also be deleted automatically');

        // Additional verification: Address record still exists but both flags are false
        $this->assertDatabaseHas('addresses', [
            'id' => $this->address->id,
            'is_favorite' => false,
            'shortcut' => false,
        ]);
    }
}
