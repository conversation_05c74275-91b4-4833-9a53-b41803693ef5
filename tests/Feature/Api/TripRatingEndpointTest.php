<?php

namespace Tests\Feature\Api;

use App\Models\Driver;
use App\Models\Rider;
use App\Models\Trip;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class TripRatingEndpointTest extends TestCase
{
    use RefreshDatabase;

    protected $user;

    protected $rider;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a user and authenticate
        $this->user = User::factory()->create([
            'email' => '<EMAIL>',
        ]);

        // Create a rider for this user
        $this->rider = Rider::factory()->create([
            'user_id' => $this->user->id,
        ]);

        Sanctum::actingAs($this->user, [], 'web');
    }

    /** @test */
    public function rider_can_submit_trip_rating_with_new_fields()
    {
        // Create a driver and trip
        $driver = Driver::factory()->create();

        $trip = Trip::factory()->create([
            'rider_id' => $this->rider->id,
            'driver_id' => $driver->id,
            'status' => 'completed',
        ]);

        // Prepare rating data with new fields
        $ratingData = [
            'trip_id' => $trip->id,
            'rider_id' => $this->rider->id,
            'driver_id' => $driver->id,
            'traffic_lights_exceeded' => true,
            'speed_exceeded' => false,
            'driver_behavior' => 'good',
            'rider_to_driver_rating' => 4,
            'rider_to_car_rating' => 5,
            'rider_review' => 'Great trip overall!',
        ];

        // Submit the rating
        $response = $this->postJson('/api/rider/trip-ratings', $ratingData);

        // Check if the response is successful
        $response->assertStatus(201);

        // Verify the data was stored in the database
        $this->assertDatabaseHas('trip_ratings', [
            'trip_id' => $trip->id,
            'rider_id' => $this->rider->id,
            'driver_id' => $driver->id,
            'traffic_lights_exceeded' => true,
            'speed_exceeded' => false,
            'driver_behavior' => 'good',
            'rider_to_driver_rating' => 4,
            'rider_to_car_rating' => 5,
            'rider_review' => 'Great trip overall!',
        ]);
    }

    /** @test */
    public function trip_rating_validates_required_fields()
    {
        // Create a driver and trip
        $driver = Driver::factory()->create();

        $trip = Trip::factory()->create([
            'rider_id' => $this->rider->id,
            'driver_id' => $driver->id,
            'status' => 'completed',
        ]);

        // Submit incomplete rating data
        $incompleteData = [
            'trip_id' => $trip->id,
            // Missing required fields
        ];

        $response = $this->postJson('/api/rider/trip-ratings', $incompleteData);

        // Should return validation errors
        $response->assertStatus(422);
    }

    /** @test */
    public function trip_rating_validates_driver_behavior_enum()
    {
        // Create a driver and trip
        $driver = Driver::factory()->create();

        $trip = Trip::factory()->create([
            'rider_id' => $this->rider->id,
            'driver_id' => $driver->id,
            'status' => 'completed',
        ]);

        // Submit rating data with invalid driver_behavior
        $invalidData = [
            'trip_id' => $trip->id,
            'rider_id' => $this->rider->id,
            'driver_id' => $driver->id,
            'traffic_lights_exceeded' => true,
            'speed_exceeded' => false,
            'driver_behavior' => 'invalid_behavior', // Invalid value
            'rider_to_driver_rating' => 4,
            'rider_to_car_rating' => 5,
            'rider_review' => 'Great trip overall!',
        ];

        $response = $this->postJson('/api/rider/trip-ratings', $invalidData);

        // Should return validation error
        $response->assertStatus(422);
        $response->assertJsonValidationErrors(['driver_behavior']);
    }

    /** @test */
    public function trip_rating_accepts_valid_driver_behavior_values()
    {
        $validBehaviors = ['excellent', 'good', 'poor'];

        foreach ($validBehaviors as $behavior) {
            // Create a driver and trip
            $driver = Driver::factory()->create();

            $trip = Trip::factory()->create([
                'rider_id' => $this->rider->id,
                'driver_id' => $driver->id,
                'status' => 'completed',
            ]);

            // Submit rating data with valid driver_behavior
            $validData = [
                'trip_id' => $trip->id,
                'rider_id' => $this->rider->id,
                'driver_id' => $driver->id,
                'traffic_lights_exceeded' => false,
                'speed_exceeded' => false,
                'driver_behavior' => $behavior,
                'rider_to_driver_rating' => 3,
                'rider_to_car_rating' => 3,
                'rider_review' => 'Test review',
            ];

            $response = $this->postJson('/api/rider/trip-ratings', $validData);

            // Should be successful
            $response->assertStatus(201);

            // Verify the behavior was stored correctly
            $this->assertDatabaseHas('trip_ratings', [
                'trip_id' => $trip->id,
                'driver_behavior' => $behavior,
            ]);
        }
    }
}
