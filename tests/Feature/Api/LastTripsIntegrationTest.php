<?php

namespace Tests\Feature\Api;

use App\Enums\Trips\TripStatus;
use App\Models\Address;
use App\Models\Rider;
use App\Models\Trip;
use App\Models\TripLocation;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class LastTripsIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected $user;

    protected $rider;

    protected function setUp(): void
    {
        parent::setUp();

        // Create a user with rider
        $this->user = User::factory()->create(['type' => 'passenger']);
        $this->rider = Rider::factory()->create(['user_id' => $this->user->id]);
    }

    /** @test */
    public function test_complete_flow_create_trip_mark_favorite_and_check_last_trips()
    {
        // Authenticate as the user
        Sanctum::actingAs($this->user, [], 'web');

        // Step 1: Create a completed trip with trip location
        $trip = Trip::factory()->create([
            'rider_id' => $this->rider->id,
            'status' => TripStatus::completed,
        ]);

        $tripLocation = TripLocation::create([
            'trip_id' => $trip->id,
            'departure_lat' => 35.843137,
            'departure_lng' => 10.603898,
            'arrival_lat' => 35.835738,
            'arrival_lng' => 10.586988,
            'departure_address' => 'Departure Test Address',
            'arrival_address' => 'Arrival Test Address',
            'polyline' => 'test_polyline_data',
        ]);

        // Update trip with trip_location_id
        $trip->update(['trip_location_id' => $tripLocation->id]);

        // Step 2: Verify that initially the address is NOT favorite
        $response = $this->getJson(route('api.rider.last.trips'));
        $response->assertOk();

        $data = $response->json();
        $this->assertCount(1, $data['data']['rides']);
        $this->assertFalse($data['data']['rides'][0]['arrivalAddress']['is_favorite']);
        $this->assertNull($data['data']['rides'][0]['arrivalAddress']['full_address']);

        // Step 3: Mark the arrival address as favorite using the API
        $favoriteResponse = $this->postJson(route('api.rider.favorite-addresses.save'), [
            'TripId' => $trip->id,
            'favorite_for' => 'arrival',
        ]);

        $favoriteResponse->assertStatus(201); // saveFavoriteAddress returns 201 when creating new address

        // Step 4: Verify that the address is now in the addresses table as favorite
        $favoriteAddress = Address::where('addressable_id', $this->user->id)
            ->where('addressable_type', 'App\Models\User')
            ->where('is_favorite', true)
            ->first();

        $this->assertNotNull($favoriteAddress);
        $this->assertEquals('Arrival Test Address', $favoriteAddress->address);
        $this->assertEquals(35.835738, $favoriteAddress->latitude);
        $this->assertEquals(10.586988, $favoriteAddress->longitude);

        // Step 5: Test our last trips endpoint again - should now show as favorite
        $response2 = $this->getJson(route('api.rider.last.trips'));
        $response2->assertOk();

        $data2 = $response2->json();
        $this->assertCount(1, $data2['data']['rides']);
        $this->assertTrue($data2['data']['rides'][0]['arrivalAddress']['is_favorite']);

        // The full_address should still be null because saveFavoriteAddress doesn't set it
        $this->assertNull($data2['data']['rides'][0]['arrivalAddress']['full_address']);
    }

    /** @test */
    public function test_full_address_is_populated_when_address_has_full_address()
    {
        // Authenticate as the user
        Sanctum::actingAs($this->user, [], 'web');

        // Create a trip
        $trip = Trip::factory()->create([
            'rider_id' => $this->rider->id,
            'status' => TripStatus::completed,
        ]);

        $tripLocation = TripLocation::create([
            'trip_id' => $trip->id,
            'departure_lat' => 35.843137,
            'departure_lng' => 10.603898,
            'arrival_lat' => 35.835738,
            'arrival_lng' => 10.586988,
            'departure_address' => 'Short Address',
            'arrival_address' => 'Short Arrival Address',
            'polyline' => 'test_polyline_data',
        ]);

        $trip->update(['trip_location_id' => $tripLocation->id]);

        // Manually create a favorite address with full_address (simulating addFavoriteAddress endpoint)
        Address::create([
            'addressable_type' => 'App\Models\User',
            'addressable_id' => $this->user->id,
            'address' => 'Short Arrival Address',
            'full_address' => 'Complete Full Address with Street, City, Country Details',
            'latitude' => 35.835738,
            'longitude' => 10.586988,
            'is_favorite' => true,
        ]);

        // Test our endpoint
        $response = $this->getJson(route('api.rider.last.trips'));
        $response->assertOk();

        $data = $response->json();
        $this->assertCount(1, $data['data']['rides']);
        $this->assertTrue($data['data']['rides'][0]['arrivalAddress']['is_favorite']);
        $this->assertEquals(
            'Complete Full Address with Street, City, Country Details',
            $data['data']['rides'][0]['arrivalAddress']['full_address']
        );
    }

    /** @test */
    public function test_multiple_trips_with_mixed_favorite_status()
    {
        // Authenticate as the user
        Sanctum::actingAs($this->user, [], 'web');

        // Create 3 trips with explicit timestamps to ensure proper ordering
        for ($i = 0; $i < 3; $i++) {
            $trip = Trip::factory()->create([
                'rider_id' => $this->rider->id,
                'status' => TripStatus::completed,
                'created_at' => now()->subMinutes(3 - $i), // Trip 0 = 3 min ago, Trip 1 = 2 min ago, Trip 2 = 1 min ago
            ]);

            $tripLocation = TripLocation::create([
                'trip_id' => $trip->id,
                'departure_lat' => 35.843137 + $i * 0.001,
                'departure_lng' => 10.603898 + $i * 0.001,
                'arrival_lat' => 35.835738 + $i * 0.001,
                'arrival_lng' => 10.586988 + $i * 0.001,
                'departure_address' => "Departure Address $i",
                'arrival_address' => "Arrival Address $i",
                'polyline' => "test_polyline_$i",
            ]);

            $trip->update(['trip_location_id' => $tripLocation->id]);
        }

        // Mark only the second trip (index 1) as favorite
        Address::create([
            'addressable_type' => 'App\Models\User',
            'addressable_id' => $this->user->id,
            'address' => 'Arrival Address 1',
            'full_address' => 'Full Address for Trip 1',
            'latitude' => 35.835738 + 1 * 0.001,
            'longitude' => 10.586988 + 1 * 0.001,
            'is_favorite' => true,
        ]);

        // Test our endpoint
        $response = $this->getJson(route('api.rider.last.trips'));
        $response->assertOk();

        $data = $response->json();
        $this->assertCount(3, $data['data']['rides']);

        // Check that trips are sorted by created_at desc
        $returnedTrips = $data['data']['rides'];

        // Trip 2 (newest) should be first and NOT favorite
        $this->assertEquals('Arrival Address 2', $returnedTrips[0]['arrivalAddress']['arrival_address']);
        $this->assertFalse($returnedTrips[0]['arrivalAddress']['is_favorite']);
        $this->assertNull($returnedTrips[0]['arrivalAddress']['full_address']);

        // Trip 1 (middle) should be second and IS favorite
        $this->assertEquals('Arrival Address 1', $returnedTrips[1]['arrivalAddress']['arrival_address']);
        $this->assertTrue($returnedTrips[1]['arrivalAddress']['is_favorite']);
        $this->assertEquals('Full Address for Trip 1', $returnedTrips[1]['arrivalAddress']['full_address']);

        // Trip 0 (oldest) should be third and NOT favorite
        $this->assertEquals('Arrival Address 0', $returnedTrips[2]['arrivalAddress']['arrival_address']);
        $this->assertFalse($returnedTrips[2]['arrivalAddress']['is_favorite']);
        $this->assertNull($returnedTrips[2]['arrivalAddress']['full_address']);
    }

    /** @test */
    public function test_address_matching_by_coordinates_vs_address_string()
    {
        // Authenticate as the user
        Sanctum::actingAs($this->user, [], 'web');

        // Create a trip
        $trip = Trip::factory()->create([
            'rider_id' => $this->rider->id,
            'status' => TripStatus::completed,
        ]);

        $tripLocation = TripLocation::create([
            'trip_id' => $trip->id,
            'departure_lat' => 35.843137,
            'departure_lng' => 10.603898,
            'arrival_lat' => 35.835738,
            'arrival_lng' => 10.586988,
            'departure_address' => 'Test Address',
            'arrival_address' => 'Test Address',
            'polyline' => 'test_polyline_data',
        ]);

        $trip->update(['trip_location_id' => $tripLocation->id]);

        // Create a favorite address that matches by address string but has different coordinates
        Address::create([
            'addressable_type' => 'App\Models\User',
            'addressable_id' => $this->user->id,
            'address' => 'Test Address',  // Same address string
            'full_address' => 'Full Test Address Details',
            'latitude' => 35.999999,     // Different coordinates
            'longitude' => 10.999999,    // Different coordinates
            'is_favorite' => true,
        ]);

        // Test our endpoint - should match by address string
        $response = $this->getJson(route('api.rider.last.trips'));
        $response->assertOk();

        $data = $response->json();
        $this->assertCount(1, $data['data']['rides']);
        $this->assertTrue($data['data']['rides'][0]['arrivalAddress']['is_favorite']);
        $this->assertEquals('Full Test Address Details', $data['data']['rides'][0]['arrivalAddress']['full_address']);
    }
}
