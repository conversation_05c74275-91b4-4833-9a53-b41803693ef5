<?php

namespace Tests\Feature;

use App\Models\Driver;
use App\Models\Rider;
use App\Models\Trip;
use App\Models\TripLocation;
use App\Models\User;
use App\Models\Vehicle;
use App\Models\VehicleBrand;
use App\Models\VehicleModel;
use App\Models\VehicleType;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TripStatusApiFeatureTest extends TestCase
{
    use RefreshDatabase;

    private $rider;

    private $driver;

    private $vehicle;

    private $trip;

    protected function setUp(): void
    {
        parent::setUp();
        $this->createTestData();
    }

    private function createTestData(): void
    {
        // Create rider user
        $riderUser = User::factory()->create([
            'type' => 'passenger',
            'name' => 'Test',
            'last_name' => 'Rider',
            'phone_number' => '+218911234567',
        ]);

        $this->rider = Rider::factory()->create([
            'user_id' => $riderUser->id,
        ]);

        // Create driver user
        $driverUser = User::factory()->create([
            'type' => 'driver',
            'name' => 'Test',
            'last_name' => 'Driver',
            'phone_number' => '+218911234568',
        ]);

        $this->driver = Driver::factory()->create([
            'user_id' => $driverUser->id,
            'location' => 'SRID=4326;POINT(13.1913 32.8872)', // Tripoli, Libya coordinates
        ]);

        // Create vehicle brand
        $vehicleBrand = VehicleBrand::factory()->create([
            'name_en' => 'Toyota',
            'name_ar' => 'تويوتا',
        ]);

        // Create vehicle model
        $vehicleModel = VehicleModel::factory()->create([
            'vehicle_brand_id' => $vehicleBrand->id,
            'name_en' => 'Corolla',
            'name_ar' => 'كورولا',
        ]);

        // Create vehicle type
        $vehicleType = VehicleType::factory()->create([
            'name_en' => 'Sedan',
            'name_ar' => 'سيدان',
        ]);

        // Create vehicle
        $this->vehicle = Vehicle::factory()->create([
            'vehicle_model_id' => $vehicleModel->id,
            'vehicle_type_id' => $vehicleType->id,
            'license_plate_number' => 'ABC-123',
            'average_vehicle_rating' => '4.5',
        ]);

        // Create trip
        $this->trip = Trip::factory()->create([
            'rider_id' => $this->rider->id,
            'driver_id' => $this->driver->id,
            'vehicle_id' => $this->vehicle->id,
            'status' => 'completed',
            'distance' => '10.5',
            'pricing_breakdown' => json_encode([
                'base_fare' => 15,
                'distance_fare' => 5,
                'total' => 20,
            ]),
        ]);

        // Create trip location
        TripLocation::factory()->create([
            'trip_id' => $this->trip->id,
            'departure_address' => 'Test Departure',
            'arrival_address' => 'Test Arrival',
            'departure_lat' => '32.8872',
            'departure_lng' => '13.1913',
            'arrival_lat' => '32.9000',
            'arrival_lng' => '13.2000',
        ]);
    }

    public function test_tripstatus_api_includes_driver_location_coordinates()
    {
        // Authenticate as rider
        $this->actingAs($this->rider->user);

        // Make API request
        $response = $this->getJson("/api/trip/{$this->trip->id}");

        // Assert response structure
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'status',
            'message',
            'data' => [
                'id',
                'driver' => [
                    'location_coordinates' => [
                        'latitude',
                        'longitude',
                    ],
                ],
            ],
        ]);

        // Check that driver location coordinates are correct
        $responseData = $response->json();
        $locationCoords = $responseData['data']['driver']['location_coordinates'];

        $this->assertEquals(32.8872, $locationCoords['latitude']);
        $this->assertEquals(13.1913, $locationCoords['longitude']);
    }

    public function test_tripstatus_api_includes_vehicle_multilingual_information()
    {
        // Authenticate as rider
        $this->actingAs($this->rider->user);

        // Make API request
        $response = $this->getJson("/api/trip/{$this->trip->id}");

        // Assert response structure
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'status',
            'message',
            'data' => [
                'id',
                'vehicle' => [
                    'vehicle_type_ar',
                    'vehicle_type_en',
                    'vehicle_model_ar',
                    'vehicle_model_en',
                    'vehicle_brand_ar',
                    'vehicle_brand_en',
                    'vehicle_average_rating',
                    'vehicle_rating',
                ],
            ],
        ]);

        // Check that vehicle multilingual information is correct
        $responseData = $response->json();
        $vehicle = $responseData['data']['vehicle'];

        $this->assertEquals('سيدان', $vehicle['vehicle_type_ar']);
        $this->assertEquals('Sedan', $vehicle['vehicle_type_en']);
        $this->assertEquals('كورولا', $vehicle['vehicle_model_ar']);
        $this->assertEquals('Corolla', $vehicle['vehicle_model_en']);
        $this->assertEquals('تويوتا', $vehicle['vehicle_brand_ar']);
        $this->assertEquals('Toyota', $vehicle['vehicle_brand_en']);

        // The vehicle average rating might be null if there are no ratings yet
        $this->assertArrayHasKey('vehicle_average_rating', $vehicle);
        $this->assertArrayHasKey('vehicle_rating', $vehicle);
    }

    public function test_tripstatus_api_unauthorized_access()
    {
        // Create another user who is not related to the trip
        $unauthorizedUser = User::factory()->create(['type' => 'passenger']);
        $this->actingAs($unauthorizedUser);

        // Make API request
        $response = $this->getJson("/api/trip/{$this->trip->id}");

        // Assert unauthorized response
        $response->assertStatus(401);
        $response->assertJson([
            'message' => 'Unauthorized access',
        ]);
    }

    public function test_tripstatus_api_trip_not_found()
    {
        // Authenticate as rider
        $this->actingAs($this->rider->user);

        // Make API request with non-existent trip ID
        $response = $this->getJson('/api/trip/99999');

        // Assert not found response
        $response->assertStatus(404);
        $response->assertJson([
            'message' => 'Trip not found',
        ]);
    }

    public function test_tripstatus_api_driver_can_access_trip()
    {
        // Authenticate as driver
        $this->actingAs($this->driver->user);

        // Make API request
        $response = $this->getJson("/api/trip/{$this->trip->id}");

        // Assert successful response
        $response->assertStatus(200);
        $response->assertJsonStructure([
            'status',
            'message',
            'data' => [
                'id',
                'driver' => [
                    'location_coordinates',
                ],
                'vehicle' => [
                    'vehicle_type_ar',
                    'vehicle_type_en',
                ],
            ],
        ]);
    }
}
