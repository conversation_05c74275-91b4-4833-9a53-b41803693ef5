<?php

namespace Tests\Feature;

use App\Models\User;
use Tests\TestCase;

class ExampleTest extends TestCase
{
    /**
     * Test that the application redirects unauthenticated users.
     */
    public function test_unauthenticated_user_redirected(): void
    {
        $response = $this->get('/');

        $response->assertStatus(302);
    }

    /**
     * Test that authenticated users can access a protected route.
     */
    public function test_authenticated_user_can_access_protected_route(): void
    {
        $user = User::factory()->create();

        // Test that the user can be authenticated
        $this->assertInstanceOf(User::class, $user);
        $this->assertNotNull($user->id);
    }
}
