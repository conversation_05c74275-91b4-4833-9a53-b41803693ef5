<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\UserInvitation;
use App\Services\UserInvitationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Notification;
use Tests\TestCase;

class UserInvitationTest extends TestCase
{
    use RefreshDatabase;

    protected UserInvitationService $invitationService;
    protected User $adminUser;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->invitationService = app(UserInvitationService::class);
        
        // Create a test admin user
        $this->adminUser = User::factory()->create([
            'email' => '<EMAIL>',
            'type' => 'admin',
        ]);
    }

    public function test_can_create_invitation()
    {
        Notification::fake();

        $invitation = $this->invitationService->createInvitation(
            '<EMAIL>',
            $this->adminUser->id
        );

        $this->assertInstanceOf(UserInvitation::class, $invitation);
        $this->assertEquals('<EMAIL>', $invitation->email);
        $this->assertEquals($this->adminUser->id, $invitation->invited_by);
        $this->assertNotNull($invitation->token);
        $this->assertNotNull($invitation->expires_at);
        $this->assertNull($invitation->accepted_at);
    }

    public function test_cannot_create_invitation_for_existing_user()
    {
        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('A user with this email already exists.');

        $this->invitationService->createInvitation(
            $this->adminUser->email,
            $this->adminUser->id
        );
    }

    public function test_cannot_create_duplicate_invitation()
    {
        $this->invitationService->createInvitation(
            '<EMAIL>',
            $this->adminUser->id
        );

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('A valid invitation for this email already exists.');

        $this->invitationService->createInvitation(
            '<EMAIL>',
            $this->adminUser->id
        );
    }

    public function test_can_accept_invitation()
    {
        $invitation = $this->invitationService->createInvitation(
            '<EMAIL>',
            $this->adminUser->id
        );

        $user = $this->invitationService->acceptInvitation(
            $invitation->token,
            '<EMAIL>',
            'password123',
            ['name' => 'New', 'last_name' => 'User']
        );

        $this->assertInstanceOf(User::class, $user);
        $this->assertEquals('<EMAIL>', $user->email);
        $this->assertEquals('New', $user->name);
        $this->assertEquals('User', $user->last_name);
        $this->assertEquals('admin', $user->type);
        $this->assertNotNull($user->email_verified_at);

        // Check that invitation is marked as accepted
        $invitation->refresh();
        $this->assertNotNull($invitation->accepted_at);
    }

    public function test_cannot_accept_expired_invitation()
    {
        $invitation = UserInvitation::create([
            'email' => '<EMAIL>',
            'token' => UserInvitation::generateToken(),
            'invited_by' => $this->adminUser->id,
            'expires_at' => now()->subDay(),
        ]);

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('This invitation has expired or has already been used.');

        $this->invitationService->acceptInvitation(
            $invitation->token,
            '<EMAIL>',
            'password123'
        );
    }

    public function test_invitation_acceptance_page_loads()
    {
        $invitation = $this->invitationService->createInvitation(
            '<EMAIL>',
            $this->adminUser->id
        );

        $response = $this->get(route('invitation.accept', [
            'token' => $invitation->token,
            'email' => '<EMAIL>',
        ]));

        $response->assertStatus(200);
        $response->assertSee('Accept Invitation');
        $response->assertSee('<EMAIL>');
    }

    public function test_invalid_invitation_shows_error_page()
    {
        $response = $this->get(route('invitation.accept', [
            'token' => 'invalid-token',
            'email' => '<EMAIL>',
        ]));

        $response->assertStatus(200);
        $response->assertSee('Invalid Invitation');
        $response->assertSee('Invalid invitation link');
    }

    public function test_invitation_post_creates_user_and_logs_in()
    {
        $invitation = $this->invitationService->createInvitation(
            '<EMAIL>',
            $this->adminUser->id
        );

        $response = $this->post(route('invitation.accept.post'), [
            'token' => $invitation->token,
            'email' => '<EMAIL>',
            'name' => 'New',
            'last_name' => 'User',
            'password' => 'password123',
            'password_confirmation' => 'password123',
        ]);

        $response->assertRedirect();
        
        // Check user was created
        $user = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($user);
        $this->assertEquals('New', $user->name);
        
        // Check user is logged in
        $this->assertAuthenticatedAs($user);
    }
}
