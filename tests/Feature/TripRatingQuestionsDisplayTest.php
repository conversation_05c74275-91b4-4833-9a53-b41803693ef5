<?php

namespace Tests\Feature;

use App\Enums\Trips\TripStatus;
use App\Models\Driver;
use App\Models\Rider;
use App\Models\Trip;
use App\Models\TripRating;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TripRatingQuestionsDisplayTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create an admin user for testing
        $this->admin = User::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);
    }

    /** @test */
    public function trip_rating_questions_are_displayed_correctly()
    {
        // Create a completed trip with rating
        $rider = Rider::factory()->create();
        $driver = Driver::factory()->create();
        
        $trip = Trip::factory()->create([
            'rider_id' => $rider->id,
            'driver_id' => $driver->id,
            'status' => TripStatus::completed,
        ]);

        // Create a trip rating with the new fields
        $tripRating = TripRating::factory()->create([
            'trip_id' => $trip->id,
            'rider_id' => $rider->id,
            'driver_id' => $driver->id,
            'traffic_lights_exceeded' => true,
            'speed_exceeded' => false,
            'driver_behavior' => 'good',
            'rider_to_driver_rating' => 4,
            'rider_to_car_rating' => 5,
            'rider_review' => 'Great trip overall!',
        ]);

        // Test that the trip rating has the expected values
        $this->assertTrue($tripRating->traffic_lights_exceeded);
        $this->assertFalse($tripRating->speed_exceeded);
        $this->assertEquals('good', $tripRating->driver_behavior);
        $this->assertEquals(4, $tripRating->rider_to_driver_rating);
        $this->assertEquals(5, $tripRating->rider_to_car_rating);
        $this->assertEquals('Great trip overall!', $tripRating->rider_review);

        // Verify the trip has the rating relationship
        $this->assertNotNull($trip->tripRatings()->first());
        $this->assertEquals($tripRating->id, $trip->tripRatings()->first()->id);
    }

    /** @test */
    public function trip_rating_questions_handle_null_values()
    {
        // Create a completed trip with rating that has null values
        $rider = Rider::factory()->create();
        $driver = Driver::factory()->create();
        
        $trip = Trip::factory()->create([
            'rider_id' => $rider->id,
            'driver_id' => $driver->id,
            'status' => TripStatus::completed,
        ]);

        // Create a trip rating with null/default values
        $tripRating = TripRating::factory()->create([
            'trip_id' => $trip->id,
            'rider_id' => $rider->id,
            'driver_id' => $driver->id,
            'traffic_lights_exceeded' => false,
            'speed_exceeded' => false,
            'driver_behavior' => null,
            'rider_to_driver_rating' => 3,
            'rider_to_car_rating' => 3,
            'rider_review' => null,
        ]);

        // Test that the trip rating handles null values correctly
        $this->assertFalse($tripRating->traffic_lights_exceeded);
        $this->assertFalse($tripRating->speed_exceeded);
        $this->assertNull($tripRating->driver_behavior);
        $this->assertNull($tripRating->rider_review);

        // Verify the trip has the rating relationship
        $this->assertNotNull($trip->tripRatings()->first());
    }

    /** @test */
    public function trip_rating_questions_display_different_behavior_values()
    {
        $behaviors = ['excellent', 'good', 'poor'];
        
        foreach ($behaviors as $behavior) {
            // Create a completed trip with rating
            $rider = Rider::factory()->create();
            $driver = Driver::factory()->create();
            
            $trip = Trip::factory()->create([
                'rider_id' => $rider->id,
                'driver_id' => $driver->id,
                'status' => TripStatus::completed,
            ]);

            // Create a trip rating with specific behavior
            $tripRating = TripRating::factory()->create([
                'trip_id' => $trip->id,
                'rider_id' => $rider->id,
                'driver_id' => $driver->id,
                'driver_behavior' => $behavior,
            ]);

            $this->assertEquals($behavior, $tripRating->driver_behavior);
        }
    }
}
