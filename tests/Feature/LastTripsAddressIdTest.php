<?php

namespace Tests\Feature;

use App\Enums\Trips\TripStatus;
use App\Http\Resources\LastTripsCollection;
use App\Models\Address;
use App\Models\Rider;
use App\Models\Trip;
use App\Models\TripLocation;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class LastTripsAddressIdTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function last_trips_collection_includes_address_id_field()
    {
        // Simple test to verify that the LastTripsCollection includes address_id field
        // Create a mock trip with the required fields
        $mockTrip = (object) [
            'id' => 1,
            'created_at' => now(),
            'tripLocation' => (object) [
                'arrival_address' => 'Test Address',
                'arrival_lat' => 36.8065,
                'arrival_lng' => 10.1815,
            ],
            'arrival_full_address' => 'Full Test Address',
            'arrival_label' => 'Home',
            'arrival_is_favorite' => true,
            'arrival_address_id' => 123, // This is what we're testing
        ];

        $trips = collect([$mockTrip]);
        $resource = new LastTripsCollection($trips);
        $resourceArray = $resource->toArray(request());

        // Verify the response structure
        $this->assertArrayHasKey('rides', $resourceArray);
        $this->assertCount(1, $resourceArray['rides']);

        $ride = $resourceArray['rides'][0];
        $this->assertArrayHasKey('arrivalAddress', $ride);

        $arrivalAddress = $ride['arrivalAddress'];

        // Verify that address_id is included and has the correct value
        $this->assertArrayHasKey('address_id', $arrivalAddress);
        $this->assertEquals(123, $arrivalAddress['address_id']);

        // Verify other fields are correct
        $this->assertEquals('Test Address', $arrivalAddress['arrival_address']);
        $this->assertEquals('Full Test Address', $arrivalAddress['full_address']);
        $this->assertEquals('Home', $arrivalAddress['address_label']);
        $this->assertEquals(36.8065, $arrivalAddress['latitude']);
        $this->assertEquals(10.1815, $arrivalAddress['longitude']);
        $this->assertTrue($arrivalAddress['is_favorite']);
    }

    /** @test */
    public function last_trips_has_null_address_id_when_arrival_address_is_not_favorite()
    {
        // Create a user and rider
        $user = User::factory()->create(['type' => 'passenger']);
        $rider = Rider::factory()->create(['user_id' => $user->id]);

        // Create a trip without matching favorite address
        $trip = Trip::factory()->create([
            'rider_id' => $rider->id,
            'status' => TripStatus::completed,
        ]);

        $tripLocation = TripLocation::factory()->create(['trip_id' => $trip->id]);

        // Update with our specific values after creation
        $tripLocation->update([
            'arrival_address' => 'Non-favorite Address',
            'arrival_lat' => 36.8065,
            'arrival_lng' => 10.1815,
        ]);

        // Load the trip with relationships like the controller does
        $trip->load(['tripLocation', 'rider.user']);

        // Simulate the controller logic
        $trips = collect([$trip]);
        foreach ($trips as $tripItem) {
            $tripItem['arrival_label'] = null;
            $tripItem['arrival_full_address'] = null;
            $tripItem['arrival_is_favorite'] = false;
            $tripItem['arrival_address_id'] = null;

            if ($tripItem->tripLocation) {
                $arrival_address = Address::where('addressable_id', $tripItem->rider->user->id)
                    ->where('addressable_type', 'App\Models\User')
                    ->where('is_favorite', true)
                    ->where(function ($query) use ($tripItem) {
                        $query->where(function ($subQuery) use ($tripItem) {
                            $subQuery->where('longitude', $tripItem->tripLocation->arrival_lng)
                                ->where('latitude', $tripItem->tripLocation->arrival_lat);
                        })->orWhere('address', $tripItem->tripLocation->arrival_address);
                    })
                    ->first();

                if ($arrival_address) {
                    $tripItem['arrival_is_favorite'] = true;
                    $tripItem['arrival_full_address'] = $arrival_address->full_address;
                    $tripItem['arrival_address_id'] = $arrival_address->id;
                    if ($arrival_address->label) {
                        $tripItem['arrival_label'] = $arrival_address->label->label ?? null;
                    }
                }
            }
        }

        // Create the resource collection
        $resource = new LastTripsCollection($trips);
        $resourceArray = $resource->toArray(request());

        // Verify the response structure
        $ride = $resourceArray['rides'][0];
        $arrivalAddress = $ride['arrivalAddress'];

        // Verify that address_id is null when not favorite
        $this->assertArrayHasKey('address_id', $arrivalAddress);
        $this->assertNull($arrivalAddress['address_id']);

        // Verify other fields (use the actual address from the trip location)
        $this->assertNotNull($arrivalAddress['arrival_address']); // Just verify it's not null
        $this->assertNull($arrivalAddress['full_address']);
        $this->assertNull($arrivalAddress['address_label']);
        $this->assertFalse($arrivalAddress['is_favorite']);
    }
}
