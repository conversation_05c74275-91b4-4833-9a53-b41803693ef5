<?php

namespace Tests;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\TestCase as BaseTestCase;

abstract class TestCase extends BaseTestCase
{
    use CreatesApplication, RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Seed essential data for tests
        $this->seedEssentialData();
    }

    /**
     * Seed essential data needed for most tests
     */
    protected function seedEssentialData(): void
    {
        // Don't seed any data by default - let individual tests create what they need
        // This prevents conflicts between tests
    }
}
