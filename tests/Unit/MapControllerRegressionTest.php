<?php

namespace Tests\Unit;

use App\Http\Controllers\MapController;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class MapControllerRegressionTest extends TestCase
{
    use RefreshDatabase;

    private MapController $controller;

    private array $mockGoogleResponse;

    protected function setUp(): void
    {
        parent::setUp();

        $this->controller = new MapController;

        $this->mockGoogleResponse = [
            'routes' => [
                [
                    'legs' => [
                        [
                            'distance' => [
                                'text' => '5.2 km',
                                'value' => 5200,
                            ],
                            'duration' => [
                                'text' => '12 mins',
                                'value' => 720,
                            ],
                            'duration_in_traffic' => [
                                'text' => '15 mins',
                                'value' => 900,
                            ],
                        ],
                    ],
                    'overview_polyline' => [
                        'points' => 'test_polyline_encoded_string',
                    ],
                ],
            ],
            'status' => 'OK',
        ];
    }

    /** @test */
    public function map_controller_now_uses_google_maps_service()
    {
        Http::fake([
            'maps.googleapis.com/*' => Http::response($this->mockGoogleResponse, 200),
        ]);

        // Create a request with origin and destination
        $request = Request::create('/map/distance', 'GET', [
            'origin' => '32.8872,13.1913',
            'destination' => '32.9042,13.1856',
        ]);

        // Call the controller method
        $response = $this->controller->getDistanceInfo($request);

        // Verify response structure
        $this->assertEquals(200, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);

        // Should maintain Distance Matrix API response format for backward compatibility
        $this->assertArrayHasKey('destination_addresses', $responseData);
        $this->assertArrayHasKey('origin_addresses', $responseData);
        $this->assertArrayHasKey('rows', $responseData);
        $this->assertArrayHasKey('status', $responseData);
        $this->assertEquals('OK', $responseData['status']);

        // Check the actual distance/duration data
        $element = $responseData['rows'][0]['elements'][0];
        $this->assertArrayHasKey('distance', $element);
        $this->assertArrayHasKey('duration', $element);
        $this->assertArrayHasKey('duration_in_traffic', $element);
        $this->assertEquals('OK', $element['status']);

        // Verify the data values
        $this->assertEquals('5.2 km', $element['distance']['text']);
        $this->assertEquals(5200, $element['distance']['value']);
        $this->assertEquals('12 mins', $element['duration']['text']);
        $this->assertEquals(720, $element['duration']['value']);
        $this->assertEquals('15 mins', $element['duration_in_traffic']['text']);
        $this->assertEquals(900, $element['duration_in_traffic']['value']);
    }

    /** @test */
    public function map_controller_handles_api_failures_gracefully()
    {
        // Clear any existing cache
        \Illuminate\Support\Facades\Cache::flush();

        Http::fake([
            'maps.googleapis.com/*' => Http::response([], 500), // Server error
        ]);

        $request = Request::create('/map/distance', 'GET', [
            'origin' => '99.9999,99.9999', // Use different coordinates to avoid cache
            'destination' => '88.8888,88.8888',
        ]);

        $response = $this->controller->getDistanceInfo($request);

        $this->assertEquals(200, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);

        // GoogleMapsService returns fallback data when API fails, so we should get OK status
        // but with fallback values (1 km, 5 min)
        $this->assertEquals('OK', $responseData['status']);
        $element = $responseData['rows'][0]['elements'][0];
        $this->assertEquals('1 km', $element['distance']['text']);
        $this->assertEquals(1000, $element['distance']['value']);
        $this->assertEquals('5 min', $element['duration']['text']);
        $this->assertEquals(300, $element['duration']['value']);
    }

    /** @test */
    public function map_controller_coordinate_parsing_works()
    {
        Http::fake([
            'maps.googleapis.com/*' => Http::response($this->mockGoogleResponse, 200),
        ]);

        // Test with different coordinate formats
        $request = Request::create('/map/distance', 'GET', [
            'origin' => '40.6655101,-73.8918897',
            'destination' => '40.6905615,-73.9976592',
        ]);

        $response = $this->controller->getDistanceInfo($request);

        $this->assertEquals(200, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);

        // Verify that coordinates are properly parsed and included in response
        $this->assertEquals(['40.6655101,-73.8918897'], $responseData['origin_addresses']);
        $this->assertEquals(['40.6905615,-73.9976592'], $responseData['destination_addresses']);
    }

    /** @test */
    public function map_controller_response_format_maintains_backward_compatibility()
    {
        Http::fake([
            'maps.googleapis.com/*' => Http::response($this->mockGoogleResponse, 200),
        ]);

        $request = Request::create('/map/distance', 'GET', [
            'origin' => '32.8872,13.1913',
            'destination' => '32.9042,13.1856',
        ]);

        $response = $this->controller->getDistanceInfo($request);
        $responseData = json_decode($response->getContent(), true);

        // Verify the response matches the expected Distance Matrix API format
        $expectedStructure = [
            'destination_addresses' => ['32.9042,13.1856'],
            'origin_addresses' => ['32.8872,13.1913'],
            'rows' => [
                [
                    'elements' => [
                        [
                            'distance' => [
                                'text' => '5.2 km',
                                'value' => 5200,
                            ],
                            'duration' => [
                                'text' => '12 mins',
                                'value' => 720,
                            ],
                            'duration_in_traffic' => [
                                'text' => '15 mins',
                                'value' => 900,
                            ],
                            'status' => 'OK',
                        ],
                    ],
                ],
            ],
            'status' => 'OK',
        ];

        $this->assertEquals($expectedStructure, $responseData);
    }

    /** @test */
    public function map_controller_now_benefits_from_caching()
    {
        // Clear cache first
        \Illuminate\Support\Facades\Cache::flush();

        Http::fake([
            'maps.googleapis.com/*' => Http::response($this->mockGoogleResponse, 200),
        ]);

        $request = Request::create('/map/distance', 'GET', [
            'origin' => '11.1111,11.1111', // Use unique coordinates
            'destination' => '22.2222,22.2222',
        ]);

        // Make the same request twice
        $response1 = $this->controller->getDistanceInfo($request);
        $response2 = $this->controller->getDistanceInfo($request);

        // Both should return the same data
        $this->assertEquals($response1->getContent(), $response2->getContent());

        // Should only make one API call due to caching
        Http::assertSentCount(1);
    }

    /** @test */
    public function map_controller_handles_response_without_traffic_data()
    {
        // Clear cache first
        \Illuminate\Support\Facades\Cache::flush();

        // Mock response without traffic data
        $responseWithoutTraffic = [
            'routes' => [
                [
                    'legs' => [
                        [
                            'distance' => [
                                'text' => '5.2 km',
                                'value' => 5200,
                            ],
                            'duration' => [
                                'text' => '12 mins',
                                'value' => 720,
                            ],
                            // No duration_in_traffic
                        ],
                    ],
                    'overview_polyline' => [
                        'points' => 'test_polyline_encoded_string',
                    ],
                ],
            ],
            'status' => 'OK',
        ];

        Http::fake([
            'maps.googleapis.com/*' => Http::response($responseWithoutTraffic, 200),
        ]);

        $request = Request::create('/map/distance', 'GET', [
            'origin' => '55.5555,55.5555', // Use unique coordinates
            'destination' => '66.6666,66.6666',
        ]);

        $response = $this->controller->getDistanceInfo($request);
        $responseData = json_decode($response->getContent(), true);

        $element = $responseData['rows'][0]['elements'][0];

        // Should have distance and duration
        $this->assertArrayHasKey('distance', $element);
        $this->assertArrayHasKey('duration', $element);

        // Should NOT have duration_in_traffic when not available in the API response
        $this->assertArrayNotHasKey('duration_in_traffic', $element);
    }
}
