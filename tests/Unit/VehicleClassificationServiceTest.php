<?php

namespace Tests\Unit;

use App\Enums\VehicleTypesCategories;
use App\Enums\WeightCategoryEnum;
use App\Models\Vehicle;
use App\Models\VehicleBrand;
use App\Models\VehicleClassificationRule;
use App\Models\VehicleModel;
use App\Models\VehicleType;
use App\Services\VehicleClassificationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class VehicleClassificationServiceTest extends TestCase
{
    use RefreshDatabase;

    private VehicleClassificationService $service;

    private VehicleBrand $brand;

    private VehicleModel $model;

    private VehicleModel $secondModel;

    private VehicleType $economyType;

    private VehicleType $comfortType;

    private VehicleType $luxuryType;

    private VehicleType $lightCoveredType;

    protected function setUp(): void
    {
        parent::setUp();

        $this->service = new VehicleClassificationService;

        // Create test data
        $this->brand = VehicleBrand::factory()->create(['name_en' => 'Toyota']);
        $this->model = VehicleModel::factory()->create([
            'vehicle_brand_id' => $this->brand->id,
            'name_en' => 'Camry',
        ]);
        $this->secondModel = VehicleModel::factory()->create([
            'vehicle_brand_id' => $this->brand->id,
            'name_en' => 'Corolla',
        ]);

        // Create vehicle types
        $this->economyType = VehicleType::factory()->create([
            'name_en' => 'Economy',
            'category' => VehicleTypesCategories::Passenger,
            'status' => true,
        ]);

        $this->comfortType = VehicleType::factory()->create([
            'name_en' => 'Comfort',
            'category' => VehicleTypesCategories::Passenger,
            'status' => true,
        ]);

        $this->luxuryType = VehicleType::factory()->create([
            'name_en' => 'Luxury',
            'category' => VehicleTypesCategories::Passenger,
            'status' => true,
        ]);

        $this->lightCoveredType = VehicleType::factory()->create([
            'name_en' => 'Light Covered Truck',
            'category' => VehicleTypesCategories::Freight,
            'is_covered' => true,
            'weight_category' => WeightCategoryEnum::LessThan1000kg,
            'status' => true,
        ]);
    }

    /** @test */
    public function it_classifies_passenger_vehicle_correctly()
    {
        // Create classification rule for Comfort type
        $rule = VehicleClassificationRule::create([
            'vehicle_type_id' => $this->comfortType->id,
            'category' => VehicleTypesCategories::Passenger,
        ]);

        $rule->qualifications()->create([
            'brands' => [$this->brand->id],
            'models' => [$this->model->id],
            'min_year' => 2018,
            'max_year' => 2024,
            'seat_numbers' => [4, 5],
        ]);

        // Create vehicle that matches the rule
        $vehicle = Vehicle::factory()->create([
            'vehicle_model_id' => $this->model->id,
            'vehicle_type_id' => $this->economyType->id, // Start with economy
            'year' => 2020,
            'seat_number' => 4,
        ]);

        $result = $this->service->classifyVehicle($vehicle);

        $this->assertEquals($this->comfortType->id, $result);
    }

    /** @test */
    public function it_returns_null_for_unmatched_passenger_vehicle()
    {
        // Create vehicle that doesn't match any rules
        $vehicle = Vehicle::factory()->create([
            'vehicle_model_id' => $this->model->id,
            'vehicle_type_id' => $this->economyType->id,
            'year' => 2015, // Too old
            'seat_number' => 4,
        ]);

        $result = $this->service->classifyVehicle($vehicle);

        // Service should return null when no classification rules match
        $this->assertNull($result);
    }

    /** @test */
    public function it_classifies_freight_vehicle_correctly()
    {
        // Create classification rule for Light Covered Truck
        $rule = VehicleClassificationRule::create([
            'vehicle_type_id' => $this->lightCoveredType->id,
            'category' => VehicleTypesCategories::Freight,
        ]);

        $rule->qualifications()->create([
            'brands' => [$this->brand->id],
            'models' => [$this->model->id],
            'min_year' => 2015,
            'max_year' => 2025,
            'is_covered' => true,
            'weight_category' => WeightCategoryEnum::LessThan1000kg->value,
        ]);

        // Create freight vehicle that matches the rule
        $vehicle = Vehicle::factory()->create([
            'vehicle_model_id' => $this->model->id,
            'vehicle_type_id' => $this->lightCoveredType->id,
            'year' => 2020,
        ]);

        $result = $this->service->classifyVehicle($vehicle);

        $this->assertEquals($this->lightCoveredType->id, $result);
    }

    /** @test */
    public function it_returns_null_for_vehicle_without_model_or_brand()
    {
        // Create a vehicle with a model but test the service with null model
        $vehicle = Vehicle::factory()->create([
            'vehicle_type_id' => $this->economyType->id,
        ]);

        // Manually set the vehicle_model_id to null to test the service
        $vehicle->vehicle_model_id = null;

        $result = $this->service->classifyVehicle($vehicle);

        $this->assertNull($result);
    }

    /** @test */
    public function it_auto_classifies_and_updates_vehicle()
    {
        // Create classification rule
        $rule = VehicleClassificationRule::create([
            'vehicle_type_id' => $this->luxuryType->id,
            'category' => VehicleTypesCategories::Passenger,
        ]);

        $rule->qualifications()->create([
            'brands' => [$this->brand->id],
            'models' => [$this->model->id],
            'min_year' => 2020,
            'max_year' => 2024,
            'seat_numbers' => [4],
        ]);

        // Create vehicle with different type
        $vehicle = Vehicle::factory()->create([
            'vehicle_model_id' => $this->model->id,
            'vehicle_type_id' => $this->economyType->id,
            'year' => 2022,
            'seat_number' => 4,
        ]);

        $result = $this->service->autoClassifyVehicle($vehicle);

        $this->assertTrue($result);
        $this->assertEquals($this->luxuryType->id, $vehicle->fresh()->vehicle_type_id);
    }

    /** @test */
    public function it_gets_suggested_vehicle_types_with_classification_first()
    {
        // Create classification rule for Comfort type
        $rule = VehicleClassificationRule::create([
            'vehicle_type_id' => $this->comfortType->id,
            'category' => VehicleTypesCategories::Passenger,
        ]);

        $rule->qualifications()->create([
            'brands' => [$this->brand->id],
            'models' => [$this->model->id],
            'min_year' => 2018,
            'max_year' => 2024,
            'seat_numbers' => [4],
        ]);

        $vehicle = Vehicle::factory()->create([
            'vehicle_model_id' => $this->model->id,
            'vehicle_type_id' => $this->economyType->id,
            'year' => 2020,
            'seat_number' => 4,
        ]);

        $options = $this->service->getSuggestedVehicleTypes($vehicle);

        // Check that suggested type is first and marked with star
        $firstKey = array_key_first($options);
        $this->assertEquals($this->comfortType->id, $firstKey);
        $this->assertStringContainsString('⭐', $options[$firstKey]);
        $this->assertStringContainsString('Suggested', $options[$firstKey]);
    }

    /** @test */
    public function it_handles_multiple_qualifications_per_rule()
    {
        // Create rule with multiple qualifications
        $rule = VehicleClassificationRule::create([
            'vehicle_type_id' => $this->luxuryType->id,
            'category' => VehicleTypesCategories::Passenger,
        ]);

        // First qualification: Toyota Camry 2020-2024
        $rule->qualifications()->create([
            'brands' => [$this->brand->id],
            'models' => [$this->model->id],
            'min_year' => 2020,
            'max_year' => 2024,
            'seat_numbers' => [4, 5],
        ]);

        // Second qualification: Any brand/model 2022-2024 with 6 seats
        $rule->qualifications()->create([
            'min_year' => 2022,
            'max_year' => 2024,
            'seat_numbers' => [6],
        ]);

        // Test vehicle that matches second qualification
        $vehicle = Vehicle::factory()->create([
            'vehicle_model_id' => $this->model->id,
            'vehicle_type_id' => $this->economyType->id,
            'year' => 2023,
            'seat_number' => 6,
        ]);

        $result = $this->service->classifyVehicle($vehicle);

        $this->assertEquals($this->luxuryType->id, $result);
    }

    /** @test */
    public function it_validates_brand_model_combinations_and_finds_conflicts()
    {
        // Create an existing rule with Toyota Camry
        $existingRule = VehicleClassificationRule::create([
            'vehicle_type_id' => $this->comfortType->id,
            'category' => VehicleTypesCategories::Passenger,
        ]);

        $existingRule->qualifications()->create([
            'brands' => [$this->brand->id],
            'models' => [$this->model->id],
            'min_year' => 2018,
            'max_year' => 2024,
            'seat_numbers' => [4, 5],
        ]);

        // Test validation with conflicting qualifications
        $qualifications = [
            [
                'brands' => [$this->brand->id], // Same brand
                'models' => [$this->model->id], // Same model
                'min_year' => 2020,
                'max_year' => 2024,
                'seat_numbers' => [4],
            ],
        ];

        $conflicts = $this->service->validateBrandModelCombinations($qualifications);

        $this->assertNotEmpty($conflicts);
        $this->assertCount(1, $conflicts);
        $this->assertEquals(0, $conflicts[0]['qualification_index']);
        $this->assertEquals($this->brand->id, $conflicts[0]['brand_id']);
        $this->assertEquals($this->model->id, $conflicts[0]['model_id']);
    }

    /** @test */
    public function it_validates_brand_model_combinations_and_excludes_current_rule()
    {
        // Create an existing rule with Toyota Camry
        $existingRule = VehicleClassificationRule::create([
            'vehicle_type_id' => $this->comfortType->id,
            'category' => VehicleTypesCategories::Passenger,
        ]);

        $existingRule->qualifications()->create([
            'brands' => [$this->brand->id],
            'models' => [$this->model->id],
            'min_year' => 2018,
            'max_year' => 2024,
            'seat_numbers' => [4, 5],
        ]);

        // Test validation with same brand-model but excluding the current rule
        $qualifications = [
            [
                'brands' => [$this->brand->id], // Same brand
                'models' => [$this->model->id], // Same model
                'min_year' => 2020,
                'max_year' => 2024,
                'seat_numbers' => [4],
            ],
        ];

        $conflicts = $this->service->validateBrandModelCombinations($qualifications, $existingRule->id);

        $this->assertEmpty($conflicts); // Should be empty because we excluded the current rule
    }

    /** @test */
    public function it_validates_brand_model_combinations_with_no_conflicts()
    {
        // Create an existing rule with Toyota Camry
        $existingRule = VehicleClassificationRule::create([
            'vehicle_type_id' => $this->comfortType->id,
            'category' => VehicleTypesCategories::Passenger,
        ]);

        $existingRule->qualifications()->create([
            'brands' => [$this->brand->id],
            'models' => [$this->model->id],
            'min_year' => 2018,
            'max_year' => 2024,
            'seat_numbers' => [4, 5],
        ]);

        // Create a different brand and model
        $differentBrand = VehicleBrand::factory()->create(['name_en' => 'Honda']);
        $differentModel = VehicleModel::factory()->create([
            'vehicle_brand_id' => $differentBrand->id,
            'name_en' => 'Accord',
        ]);

        // Test validation with different brand-model combination
        $qualifications = [
            [
                'brands' => [$differentBrand->id], // Different brand
                'models' => [$differentModel->id], // Different model
                'min_year' => 2020,
                'max_year' => 2024,
                'seat_numbers' => [4],
            ],
        ];

        $conflicts = $this->service->validateBrandModelCombinations($qualifications);

        $this->assertEmpty($conflicts); // Should be empty because no conflicts
    }

    /** @test */
    public function it_formats_validation_conflicts_correctly()
    {
        // Create an existing rule with Toyota Camry
        $existingRule = VehicleClassificationRule::create([
            'vehicle_type_id' => $this->comfortType->id,
            'category' => VehicleTypesCategories::Passenger,
        ]);

        $existingRule->qualifications()->create([
            'brands' => [$this->brand->id],
            'models' => [$this->model->id],
            'min_year' => 2018,
            'max_year' => 2024,
            'seat_numbers' => [4, 5],
        ]);

        // Test validation with conflicting qualifications
        $qualifications = [
            [
                'brands' => [$this->brand->id], // Same brand
                'models' => [$this->model->id], // Same model
                'min_year' => 2020,
                'max_year' => 2024,
                'seat_numbers' => [4],
            ],
        ];

        $conflicts = $this->service->validateBrandModelCombinations($qualifications);
        $formattedConflicts = $this->service->formatValidationConflicts($conflicts);

        $this->assertNotEmpty($formattedConflicts);
        $this->assertCount(1, $formattedConflicts);

        $conflict = $formattedConflicts[0];
        $this->assertStringContainsString('Toyota Camry', $conflict['message']);
        $this->assertStringContainsString('Comfort', $conflict['message']);
        $this->assertStringContainsString('Passenger', $conflict['message']);
        $this->assertEquals(0, $conflict['qualification_index']);
        $this->assertEquals('Toyota Camry', $conflict['brand_model']);
        $this->assertEquals('Comfort', $conflict['existing_vehicle_type']);
        $this->assertEquals('Passenger', $conflict['existing_category']);
    }

    /** @test */
    public function it_skips_validation_for_empty_qualifications()
    {
        $qualifications = [
            [
                'brands' => [], // Empty brands
                'models' => [], // Empty models
                'min_year' => 2020,
                'max_year' => 2024,
                'seat_numbers' => [4],
            ],
        ];

        $conflicts = $this->service->validateBrandModelCombinations($qualifications);

        $this->assertEmpty($conflicts); // Should be empty because brands/models are empty
    }

    /** @test */
    public function it_validates_within_rule_brand_model_combinations_and_finds_conflicts()
    {
        // Test qualifications with duplicate brand-model combinations within the same rule
        $qualifications = [
            [
                'brands' => [$this->brand->id],
                'models' => [$this->model->id, $this->secondModel->id], // Q1: Camry, Corolla
                'min_year' => 2018,
                'max_year' => 2024,
                'seat_numbers' => [4, 5],
            ],
            [
                'brands' => [$this->brand->id],
                'models' => [$this->model->id], // Q2: Camry (duplicate from Q1)
                'min_year' => 2020,
                'max_year' => 2024,
                'seat_numbers' => [4],
            ],
        ];

        $conflicts = $this->service->validateWithinRuleBrandModelCombinations($qualifications);

        $this->assertNotEmpty($conflicts);
        $this->assertCount(1, $conflicts);
        $this->assertEquals(1, $conflicts[0]['qualification_index']); // Second qualification
        $this->assertEquals($this->brand->id, $conflicts[0]['brand_id']);
        $this->assertEquals($this->model->id, $conflicts[0]['model_id']);
        $this->assertEquals(0, $conflicts[0]['first_seen_in_qualification']); // First seen in qualification 0
    }

    /** @test */
    public function it_validates_within_rule_brand_model_combinations_with_no_conflicts()
    {
        // Test qualifications with no duplicate brand-model combinations
        $qualifications = [
            [
                'brands' => [$this->brand->id],
                'models' => [$this->model->id], // Q1: Camry
                'min_year' => 2018,
                'max_year' => 2024,
                'seat_numbers' => [4, 5],
            ],
            [
                'brands' => [$this->brand->id],
                'models' => [$this->secondModel->id], // Q2: Corolla (different model)
                'min_year' => 2020,
                'max_year' => 2024,
                'seat_numbers' => [4],
            ],
        ];

        $conflicts = $this->service->validateWithinRuleBrandModelCombinations($qualifications);

        $this->assertEmpty($conflicts); // Should be empty because no conflicts
    }

    /** @test */
    public function it_formats_within_rule_validation_conflicts_correctly()
    {
        // Test qualifications with duplicate brand-model combinations
        $qualifications = [
            [
                'brands' => [$this->brand->id],
                'models' => [$this->model->id, $this->secondModel->id], // Q1: Camry, Corolla
                'min_year' => 2018,
                'max_year' => 2024,
                'seat_numbers' => [4, 5],
            ],
            [
                'brands' => [$this->brand->id],
                'models' => [$this->model->id], // Q2: Camry (duplicate from Q1)
                'min_year' => 2020,
                'max_year' => 2024,
                'seat_numbers' => [4],
            ],
        ];

        $conflicts = $this->service->validateWithinRuleBrandModelCombinations($qualifications);
        $formattedConflicts = $this->service->formatWithinRuleValidationConflicts($conflicts);

        $this->assertNotEmpty($formattedConflicts);
        $this->assertCount(1, $formattedConflicts);

        $conflict = $formattedConflicts[0];
        $this->assertStringContainsString('Toyota Camry', $conflict['message']);
        $this->assertStringContainsString('qualification 1', $conflict['message']);
        $this->assertEquals(1, $conflict['qualification_index']);
        $this->assertEquals('Toyota Camry', $conflict['brand_model']);
        $this->assertEquals(1, $conflict['first_qualification']);
        $this->assertEquals(2, $conflict['current_qualification']);
    }
}
