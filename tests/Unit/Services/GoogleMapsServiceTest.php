<?php

namespace Tests\Unit\Services;

use App\Services\GoogleMapsService;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class GoogleMapsServiceTest extends TestCase
{
    use RefreshDatabase;

    private GoogleMapsService $service;

    private array $mockOrigin;

    private array $mockDestination;

    private array $mockGoogleResponse;

    protected function setUp(): void
    {
        parent::setUp();

        $this->service = new GoogleMapsService;

        $this->mockOrigin = ['lat' => 32.8872, 'lng' => 13.1913]; // Tripoli, Libya
        $this->mockDestination = ['lat' => 32.9042, 'lng' => 13.1856]; // Another point in Tripoli

        $this->mockGoogleResponse = [
            'routes' => [
                [
                    'legs' => [
                        [
                            'distance' => [
                                'text' => '5.2 km',
                                'value' => 5200,
                            ],
                            'duration' => [
                                'text' => '12 mins',
                                'value' => 720,
                            ],
                            'duration_in_traffic' => [
                                'text' => '15 mins',
                                'value' => 900,
                            ],
                        ],
                    ],
                    'overview_polyline' => [
                        'points' => 'test_polyline_encoded_string',
                    ],
                ],
            ],
            'status' => 'OK',
        ];
    }

    protected function tearDown(): void
    {
        Cache::flush();
        parent::tearDown();
    }

    /** @test */
    public function it_can_get_distance_and_time_with_traffic()
    {
        Http::fake([
            'maps.googleapis.com/*' => Http::response($this->mockGoogleResponse, 200),
        ]);

        $result = $this->service->getDistanceAndTime($this->mockOrigin, $this->mockDestination, true);

        $this->assertNotNull($result);
        $this->assertEquals('5.2 km', $result['distance_text']);
        $this->assertEquals(5200, $result['distance_value']);
        $this->assertEquals('12 mins', $result['duration_text']);
        $this->assertEquals(720, $result['duration_value']);
        $this->assertEquals('15 mins', $result['duration_in_traffic_text']);
        $this->assertEquals(900, $result['duration_in_traffic_value']);
    }

    /** @test */
    public function it_can_get_distance_and_time_without_traffic()
    {
        Http::fake([
            'maps.googleapis.com/*' => Http::response($this->mockGoogleResponse, 200),
        ]);

        $result = $this->service->getDistanceAndTime($this->mockOrigin, $this->mockDestination, false);

        $this->assertNotNull($result);
        $this->assertEquals('5.2 km', $result['distance_text']);
        $this->assertEquals(5200, $result['distance_value']);
        $this->assertEquals('12 mins', $result['duration_text']);
        $this->assertEquals(720, $result['duration_value']);
        // Traffic data should not be included when traffic is disabled
        $this->assertArrayNotHasKey('duration_in_traffic_text', $result);
        $this->assertArrayNotHasKey('duration_in_traffic_value', $result);
    }

    /** @test */
    public function get_distance_and_time_with_traffic_method_works_correctly()
    {
        Http::fake([
            'maps.googleapis.com/*' => Http::response($this->mockGoogleResponse, 200),
        ]);

        $departureTime = Carbon::now()->addHours(2);
        $result = $this->service->getDistanceAndTimeWithTraffic($this->mockOrigin, $this->mockDestination, $departureTime);

        $this->assertNotNull($result);
        $this->assertEquals('5.2 km', $result['distance_text']);
        $this->assertEquals(5200, $result['distance_value']);
        $this->assertEquals('12 mins', $result['duration_text']);
        $this->assertEquals(720, $result['duration_value']);
        $this->assertEquals('15 mins', $result['duration_in_traffic_text']);
        $this->assertEquals(900, $result['duration_in_traffic_value']);
    }

    /** @test */
    public function get_distance_and_time_with_traffic_ignores_departure_time_parameter()
    {
        Http::fake([
            'maps.googleapis.com/*' => Http::response($this->mockGoogleResponse, 200),
        ]);

        // Call with different departure times
        $futureTime = Carbon::now()->addHours(5);
        $pastTime = Carbon::now()->subHours(2);

        $result1 = $this->service->getDistanceAndTimeWithTraffic($this->mockOrigin, $this->mockDestination, $futureTime);
        $result2 = $this->service->getDistanceAndTimeWithTraffic($this->mockOrigin, $this->mockDestination, $pastTime);

        // Results should be identical because departure time is ignored
        $this->assertEquals($result1, $result2);

        // Verify that only one API call was made due to caching
        Http::assertSentCount(1);
    }

    /** @test */
    public function it_caches_results_correctly()
    {
        Http::fake([
            'maps.googleapis.com/*' => Http::response($this->mockGoogleResponse, 200),
        ]);

        // First call should hit the API
        $result1 = $this->service->getDistanceAndTime($this->mockOrigin, $this->mockDestination, true);

        // Second call should use cache
        $result2 = $this->service->getDistanceAndTime($this->mockOrigin, $this->mockDestination, true);

        $this->assertEquals($result1, $result2);

        // Only one API call should have been made
        Http::assertSentCount(1);

        // Verify cache key exists
        $cacheKey = "route_info:{$this->mockOrigin['lat']},{$this->mockOrigin['lng']}:{$this->mockDestination['lat']},{$this->mockDestination['lng']}:1";
        $this->assertTrue(Cache::has($cacheKey));
    }

    /** @test */
    public function it_uses_different_cache_for_traffic_and_non_traffic()
    {
        Http::fake([
            'maps.googleapis.com/*' => Http::response($this->mockGoogleResponse, 200),
        ]);

        // Call with traffic
        $resultWithTraffic = $this->service->getDistanceAndTime($this->mockOrigin, $this->mockDestination, true);

        // Call without traffic
        $resultWithoutTraffic = $this->service->getDistanceAndTime($this->mockOrigin, $this->mockDestination, false);

        // Should make two API calls because cache keys are different
        Http::assertSentCount(2);

        // Verify different cache keys exist
        $cacheKeyWithTraffic = "route_info:{$this->mockOrigin['lat']},{$this->mockOrigin['lng']}:{$this->mockDestination['lat']},{$this->mockDestination['lng']}:1";
        $cacheKeyWithoutTraffic = "route_info:{$this->mockOrigin['lat']},{$this->mockOrigin['lng']}:{$this->mockDestination['lat']},{$this->mockDestination['lng']}:0";

        $this->assertTrue(Cache::has($cacheKeyWithTraffic));
        $this->assertTrue(Cache::has($cacheKeyWithoutTraffic));
    }

    /** @test */
    public function it_returns_fallback_data_when_api_fails()
    {
        Http::fake([
            'maps.googleapis.com/*' => Http::response(['status' => 'ZERO_RESULTS'], 200),
        ]);

        $result = $this->service->getDistanceAndTime($this->mockOrigin, $this->mockDestination, true);

        $this->assertNotNull($result);
        $this->assertEquals('1 km', $result['distance_text']);
        $this->assertEquals(1000, $result['distance_value']);
        $this->assertEquals('5 min', $result['duration_text']);
        $this->assertEquals(300, $result['duration_value']);
    }

    /** @test */
    public function it_can_get_route_polyline()
    {
        Http::fake([
            'maps.googleapis.com/*' => Http::response($this->mockGoogleResponse, 200),
        ]);

        $polyline = $this->service->getRoutePolyline($this->mockOrigin, $this->mockDestination);

        $this->assertEquals('test_polyline_encoded_string', $polyline);
    }

    /** @test */
    public function it_returns_fallback_polyline_in_development()
    {
        // Set environment to testing
        app()->detectEnvironment(function () {
            return 'testing';
        });

        Http::fake([
            'maps.googleapis.com/*' => Http::response(['status' => 'ZERO_RESULTS'], 200),
        ]);

        $polyline = $this->service->getRoutePolyline($this->mockOrigin, $this->mockDestination);

        $this->assertEquals('_p~iF~ps|U_ulLnnqC_mqNvxq`@', $polyline);
    }

    /** @test */
    public function it_can_calculate_estimated_arrival_time()
    {
        Http::fake([
            'maps.googleapis.com/*' => Http::response($this->mockGoogleResponse, 200),
        ]);

        $departureTime = Carbon::now();
        $arrivalTime = $this->service->calculateEstimatedArrivalTime($this->mockOrigin, $this->mockDestination, $departureTime);

        $this->assertNotNull($arrivalTime);
        $this->assertInstanceOf(Carbon::class, $arrivalTime);

        // Should add 15 minutes (900 seconds) from traffic duration
        $expectedArrivalTime = $departureTime->copy()->addSeconds(900);
        $this->assertEquals($expectedArrivalTime->timestamp, $arrivalTime->timestamp);
    }

    /** @test */
    public function it_uses_regular_duration_when_traffic_duration_unavailable()
    {
        $responseWithoutTraffic = $this->mockGoogleResponse;
        unset($responseWithoutTraffic['routes'][0]['legs'][0]['duration_in_traffic']);

        Http::fake([
            'maps.googleapis.com/*' => Http::response($responseWithoutTraffic, 200),
        ]);

        $departureTime = Carbon::now();
        $arrivalTime = $this->service->calculateEstimatedArrivalTime($this->mockOrigin, $this->mockDestination, $departureTime);

        $this->assertNotNull($arrivalTime);

        // Should add 12 minutes (720 seconds) from regular duration
        $expectedArrivalTime = $departureTime->copy()->addSeconds(720);
        $this->assertEquals($expectedArrivalTime->timestamp, $arrivalTime->timestamp);
    }

    /** @test */
    public function it_handles_api_errors_gracefully()
    {
        Http::fake([
            'maps.googleapis.com/*' => Http::response([], 500),
        ]);

        $result = $this->service->getDistanceAndTime($this->mockOrigin, $this->mockDestination, true);

        // Should return fallback data
        $this->assertNotNull($result);
        $this->assertEquals('1 km', $result['distance_text']);
        $this->assertEquals(1000, $result['distance_value']);
    }

    /** @test */
    public function it_handles_invalid_response_structure()
    {
        Http::fake([
            'maps.googleapis.com/*' => Http::response(['status' => 'OK', 'routes' => []], 200),
        ]);

        $result = $this->service->getDistanceAndTime($this->mockOrigin, $this->mockDestination, true);

        // Should return fallback data when routes array is empty
        $this->assertNotNull($result);
        $this->assertEquals('1 km', $result['distance_text']);
        $this->assertEquals(1000, $result['distance_value']);
    }

    /** @test */
    public function it_sends_correct_api_parameters_with_traffic()
    {
        Http::fake([
            'maps.googleapis.com/*' => Http::response($this->mockGoogleResponse, 200),
        ]);

        $this->service->getDistanceAndTime($this->mockOrigin, $this->mockDestination, true);

        Http::assertSent(function ($request) {
            $query = $request->data();

            return $query['origin'] === '32.8872,13.1913' &&
                   $query['destination'] === '32.9042,13.1856' &&
                   $query['departure_time'] === 'now' &&
                   $query['traffic_model'] === 'best_guess' &&
                   isset($query['key']);
        });
    }

    /** @test */
    public function it_sends_correct_api_parameters_without_traffic()
    {
        Http::fake([
            'maps.googleapis.com/*' => Http::response($this->mockGoogleResponse, 200),
        ]);

        $this->service->getDistanceAndTime($this->mockOrigin, $this->mockDestination, false);

        Http::assertSent(function ($request) {
            $query = $request->data();

            return $query['origin'] === '32.8872,13.1913' &&
                   $query['destination'] === '32.9042,13.1856' &&
                   ! isset($query['departure_time']) &&
                   ! isset($query['traffic_model']) &&
                   isset($query['key']);
        });
    }

    /** @test */
    public function multiple_calls_with_same_parameters_use_cache()
    {
        Http::fake([
            'maps.googleapis.com/*' => Http::response($this->mockGoogleResponse, 200),
        ]);

        // Make multiple calls with same traffic setting
        $this->service->getDistanceAndTime($this->mockOrigin, $this->mockDestination, true);
        $this->service->getRoutePolyline($this->mockOrigin, $this->mockDestination, true); // Now specify traffic=true
        $this->service->calculateEstimatedArrivalTime($this->mockOrigin, $this->mockDestination);

        // Should only make one API call due to caching
        Http::assertSentCount(1);
    }

    /** @test */
    public function cache_ttl_is_shorter_for_traffic_data()
    {
        Http::fake([
            'maps.googleapis.com/*' => Http::response($this->mockGoogleResponse, 200),
        ]);

        // Call with traffic
        $this->service->getDistanceAndTime($this->mockOrigin, $this->mockDestination, true);

        // Call without traffic
        $this->service->getDistanceAndTime($this->mockOrigin, $this->mockDestination, false);

        // Both should be cached but with different TTLs
        $cacheKeyWithTraffic = "route_info:{$this->mockOrigin['lat']},{$this->mockOrigin['lng']}:{$this->mockDestination['lat']},{$this->mockDestination['lng']}:1";
        $cacheKeyWithoutTraffic = "route_info:{$this->mockOrigin['lat']},{$this->mockOrigin['lng']}:{$this->mockDestination['lat']},{$this->mockDestination['lng']}:0";

        $this->assertTrue(Cache::has($cacheKeyWithTraffic));
        $this->assertTrue(Cache::has($cacheKeyWithoutTraffic));
    }

    /** @test */
    public function it_can_get_complete_route_info_with_traffic()
    {
        Http::fake([
            'maps.googleapis.com/*' => Http::response($this->mockGoogleResponse, 200),
        ]);

        $result = $this->service->getCompleteRouteInfo($this->mockOrigin, $this->mockDestination, true);

        $this->assertNotNull($result);
        $this->assertEquals('5.2 km', $result['distance_text']);
        $this->assertEquals(5200, $result['distance_value']);
        $this->assertEquals('12 mins', $result['duration_text']);
        $this->assertEquals(720, $result['duration_value']);
        $this->assertEquals('test_polyline_encoded_string', $result['polyline']);
        $this->assertEquals('15 mins', $result['duration_in_traffic_text']);
        $this->assertEquals(900, $result['duration_in_traffic_value']);
    }

    /** @test */
    public function it_can_get_complete_route_info_without_traffic()
    {
        Http::fake([
            'maps.googleapis.com/*' => Http::response($this->mockGoogleResponse, 200),
        ]);

        $result = $this->service->getCompleteRouteInfo($this->mockOrigin, $this->mockDestination, false);

        $this->assertNotNull($result);
        $this->assertEquals('5.2 km', $result['distance_text']);
        $this->assertEquals(5200, $result['distance_value']);
        $this->assertEquals('12 mins', $result['duration_text']);
        $this->assertEquals(720, $result['duration_value']);
        $this->assertEquals('test_polyline_encoded_string', $result['polyline']);
        // Traffic data should not be included
        $this->assertArrayNotHasKey('duration_in_traffic_text', $result);
        $this->assertArrayNotHasKey('duration_in_traffic_value', $result);
    }

    /** @test */
    public function complete_route_info_returns_fallback_data_when_api_fails()
    {
        Http::fake([
            'maps.googleapis.com/*' => Http::response(['status' => 'ZERO_RESULTS'], 200),
        ]);

        $result = $this->service->getCompleteRouteInfo($this->mockOrigin, $this->mockDestination, true);

        $this->assertNotNull($result);
        $this->assertEquals('1 km', $result['distance_text']);
        $this->assertEquals(1000, $result['distance_value']);
        $this->assertEquals('5 min', $result['duration_text']);
        $this->assertEquals(300, $result['duration_value']);
        $this->assertEquals('_p~iF~ps|U_ulLnnqC_mqNvxq`@', $result['polyline']); // fallback polyline in testing
    }

    /** @test */
    public function get_route_polyline_respects_traffic_parameter()
    {
        Http::fake([
            'maps.googleapis.com/*' => Http::response($this->mockGoogleResponse, 200),
        ]);

        // Call with traffic=true (default)
        $polyline1 = $this->service->getRoutePolyline($this->mockOrigin, $this->mockDestination);

        // Call with traffic=false
        $polyline2 = $this->service->getRoutePolyline($this->mockOrigin, $this->mockDestination, false);

        $this->assertEquals('test_polyline_encoded_string', $polyline1);
        $this->assertEquals('test_polyline_encoded_string', $polyline2);

        // Should make two API calls because traffic settings are different
        Http::assertSentCount(2);
    }
}
