<?php

namespace Tests\Unit\Services;

use App\Services\GoogleMapsService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class GoogleMapsServiceOptimizationTest extends TestCase
{
    use RefreshDatabase;

    private GoogleMapsService $service;
    private array $mockOrigin;
    private array $mockDestination;
    private array $mockGoogleResponse;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->service = new GoogleMapsService();
        
        $this->mockOrigin = ['lat' => 32.8872, 'lng' => 13.1913];
        $this->mockDestination = ['lat' => 32.9042, 'lng' => 13.1856];
        
        $this->mockGoogleResponse = [
            'routes' => [
                [
                    'legs' => [
                        [
                            'distance' => [
                                'text' => '5.2 km',
                                'value' => 5200
                            ],
                            'duration' => [
                                'text' => '12 mins',
                                'value' => 720
                            ],
                            'duration_in_traffic' => [
                                'text' => '15 mins',
                                'value' => 900
                            ]
                        ]
                    ],
                    'overview_polyline' => [
                        'points' => 'test_polyline_encoded_string'
                    ]
                ]
            ],
            'status' => 'OK'
        ];
    }

    protected function tearDown(): void
    {
        Cache::flush();
        parent::tearDown();
    }

    /** @test */
    public function old_pattern_makes_multiple_api_calls()
    {
        Http::fake([
            'maps.googleapis.com/*' => Http::response($this->mockGoogleResponse, 200)
        ]);

        // Simulate old pattern: separate calls for distance and polyline
        $distanceData = $this->service->getDistanceAndTime($this->mockOrigin, $this->mockDestination, false);
        $polyline = $this->service->getRoutePolyline($this->mockOrigin, $this->mockDestination, false);

        $this->assertNotNull($distanceData);
        $this->assertNotNull($polyline);
        
        // Should make only 1 API call due to caching (both use same traffic=false setting)
        Http::assertSentCount(1);
    }

    /** @test */
    public function new_pattern_makes_single_api_call()
    {
        Http::fake([
            'maps.googleapis.com/*' => Http::response($this->mockGoogleResponse, 200)
        ]);

        // New optimized pattern: single call for all data
        $completeData = $this->service->getCompleteRouteInfo($this->mockOrigin, $this->mockDestination, false);

        $this->assertNotNull($completeData);
        $this->assertArrayHasKey('distance_text', $completeData);
        $this->assertArrayHasKey('distance_value', $completeData);
        $this->assertArrayHasKey('duration_text', $completeData);
        $this->assertArrayHasKey('duration_value', $completeData);
        $this->assertArrayHasKey('polyline', $completeData);
        
        // Should make only 1 API call
        Http::assertSentCount(1);
    }

    /** @test */
    public function traffic_and_non_traffic_calls_are_properly_separated()
    {
        Http::fake([
            'maps.googleapis.com/*' => Http::response($this->mockGoogleResponse, 200)
        ]);

        // Call with traffic
        $trafficData = $this->service->getDistanceAndTime($this->mockOrigin, $this->mockDestination, true);
        
        // Call without traffic
        $nonTrafficData = $this->service->getDistanceAndTime($this->mockOrigin, $this->mockDestination, false);

        // Traffic data should include traffic information
        $this->assertArrayHasKey('duration_in_traffic_text', $trafficData);
        $this->assertArrayHasKey('duration_in_traffic_value', $trafficData);
        
        // Non-traffic data should not include traffic information
        $this->assertArrayNotHasKey('duration_in_traffic_text', $nonTrafficData);
        $this->assertArrayNotHasKey('duration_in_traffic_value', $nonTrafficData);
        
        // Should make 2 API calls (different cache keys)
        Http::assertSentCount(2);
    }

    /** @test */
    public function cache_keys_are_properly_generated()
    {
        Http::fake([
            'maps.googleapis.com/*' => Http::response($this->mockGoogleResponse, 200)
        ]);

        // Make calls with different traffic settings
        $this->service->getDistanceAndTime($this->mockOrigin, $this->mockDestination, true);
        $this->service->getDistanceAndTime($this->mockOrigin, $this->mockDestination, false);

        // Verify cache keys exist
        $trafficCacheKey = "route_info:{$this->mockOrigin['lat']},{$this->mockOrigin['lng']}:{$this->mockDestination['lat']},{$this->mockDestination['lng']}:1";
        $nonTrafficCacheKey = "route_info:{$this->mockOrigin['lat']},{$this->mockOrigin['lng']}:{$this->mockDestination['lat']},{$this->mockDestination['lng']}:0";
        
        $this->assertTrue(Cache::has($trafficCacheKey));
        $this->assertTrue(Cache::has($nonTrafficCacheKey));
    }

    /** @test */
    public function api_call_reduction_demonstration()
    {
        Http::fake([
            'maps.googleapis.com/*' => Http::response($this->mockGoogleResponse, 200)
        ]);

        // Simulate a typical trip creation scenario
        // Before optimization: separate calls for distance and polyline
        
        // First, clear cache to ensure fresh start
        Cache::flush();
        
        // Scenario 1: Trip creation (needs distance validation and polyline)
        $completeData = $this->service->getCompleteRouteInfo($this->mockOrigin, $this->mockDestination, false);
        
        // Scenario 2: Driver assignment (needs ETA calculation)
        $etaData = $this->service->getDistanceAndTime($this->mockOrigin, $this->mockDestination, true);
        
        // Scenario 3: Trip update (needs new route data)
        $newDestination = ['lat' => 32.9100, 'lng' => 13.1900];
        $updateData = $this->service->getCompleteRouteInfo($this->mockOrigin, $newDestination, false);

        $this->assertNotNull($completeData);
        $this->assertNotNull($etaData);
        $this->assertNotNull($updateData);
        
        // Should make 3 API calls total:
        // 1. Trip creation (non-traffic)
        // 2. Driver assignment (traffic)
        // 3. Trip update (different destination, non-traffic)
        Http::assertSentCount(3);
    }

    /** @test */
    public function caching_prevents_redundant_calls_in_same_request()
    {
        Http::fake([
            'maps.googleapis.com/*' => Http::response($this->mockGoogleResponse, 200)
        ]);

        // Simulate multiple calls in the same request with same parameters
        $data1 = $this->service->getDistanceAndTime($this->mockOrigin, $this->mockDestination, true);
        $data2 = $this->service->getRoutePolyline($this->mockOrigin, $this->mockDestination, true);
        $data3 = $this->service->calculateEstimatedArrivalTime($this->mockOrigin, $this->mockDestination);
        $data4 = $this->service->getCompleteRouteInfo($this->mockOrigin, $this->mockDestination, true);

        $this->assertNotNull($data1);
        $this->assertNotNull($data2);
        $this->assertNotNull($data3);
        $this->assertNotNull($data4);
        
        // All calls use the same cache key (traffic=true), so only 1 API call should be made
        Http::assertSentCount(1);
    }
}
