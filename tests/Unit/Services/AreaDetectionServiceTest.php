<?php

namespace Tests\Unit\Services;

use App\Models\Area;
use App\Services\AreaDetectionService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Cache;
use Tests\TestCase;

class AreaDetectionServiceTest extends TestCase
{
    use RefreshDatabase;

    private AreaDetectionService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new AreaDetectionService;
        // Clear cache before each test
        Cache::flush();
    }

    public function test_finds_area_by_coordinates_within_polygon()
    {
        // Create an area with a polygon (counter-clockwise winding)
        $area = Area::factory()->create([
            'name_en' => 'Test Area',
            'name_ar' => 'منطقة اختبار',
            'is_active' => true,
            'polygon' => [
                ['lat' => 32.0, 'lng' => 13.0],
                ['lat' => 33.0, 'lng' => 13.0],
                ['lat' => 33.0, 'lng' => 14.0],
                ['lat' => 32.0, 'lng' => 14.0],
            ],
        ]);

        // Test a point clearly inside the polygon
        $areaId = $this->service->getAreaIdFromCoordinates(32.5, 13.5);

        $this->assertEquals($area->id, $areaId);
    }

    public function test_returns_null_for_coordinates_outside_all_polygons()
    {
        // Create an area with a polygon
        $area = Area::factory()->create([
            'is_active' => true,
            'polygon' => [
                ['lat' => 32.0, 'lng' => 13.0],
                ['lat' => 33.0, 'lng' => 13.0],
                ['lat' => 33.0, 'lng' => 14.0],
                ['lat' => 32.0, 'lng' => 14.0],
            ],
        ]);

        // Test a point clearly outside the polygon
        $areaId = $this->service->getAreaIdFromCoordinates(35.0, 15.0);

        // The service should return null for coordinates outside all polygons
        $this->assertNull($areaId);
    }

    public function test_returns_null_when_no_polygon_matches()
    {
        // Create an area with a polygon that won't match our test coordinates
        $area = Area::factory()->create([
            'name_en' => 'Tripoli',
            'name_ar' => 'طرابلس',
            'is_active' => true,
            'polygon' => [
                ['lat' => 32.0, 'lng' => 13.0],
                ['lat' => 33.0, 'lng' => 13.0],
                ['lat' => 33.0, 'lng' => 14.0],
                ['lat' => 32.0, 'lng' => 14.0],
            ],
        ]);

        // Test coordinates that don't match the polygon
        $areaId = $this->service->getAreaIdFromCoordinates(35.0, 15.0);

        // Should return null since coordinates don't match any polygon
        $this->assertNull($areaId);
    }

    public function test_caches_results()
    {
        // Create an area with a polygon
        $area = Area::factory()->create([
            'is_active' => true,
            'polygon' => [
                ['lat' => 32.0, 'lng' => 13.0],
                ['lat' => 33.0, 'lng' => 13.0],
                ['lat' => 33.0, 'lng' => 14.0],
                ['lat' => 32.0, 'lng' => 14.0],
            ],
        ]);

        // First call should hit the database and cache the result
        $areaId1 = $this->service->getAreaIdFromCoordinates(32.5, 13.5);
        $this->assertEquals($area->id, $areaId1);

        // Verify that the result is cached by checking cache directly
        $cacheKey = 'area_coord_32.5_13.5';
        $cachedValue = Cache::get($cacheKey);
        $this->assertEquals($area->id, $cachedValue);

        // Second call should use the cache (even if we delete the area)
        $area->delete();
        $areaId2 = $this->service->getAreaIdFromCoordinates(32.5, 13.5);
        $this->assertEquals($area->id, $areaId2);
    }

    public function test_caches_null_results()
{
    // Create an area that won't match our test coordinates
    $area = Area::factory()->create([
        'is_active' => true,
        'polygon' => [
            ['lat' => 32.0, 'lng' => 13.0],
            ['lat' => 33.0, 'lng' => 13.0],
            ['lat' => 33.0, 'lng' => 14.0],
            ['lat' => 32.0, 'lng' => 14.0],
        ],
    ]);

    // First call should return null and cache it
    $areaId1 = $this->service->getAreaIdFromCoordinates(35.0, 15.0);
    $this->assertNull($areaId1);

    // Verify that the cache has the value `0` (stored intentionally for null)
    $cacheKey = 'area_coord_35.0_15.0';
    $cachedValue = Cache::get($cacheKey);
    $this->assertEquals(0, (int) $cachedValue);

    // Second call should use cached value and return null again
    $areaId2 = $this->service->getAreaIdFromCoordinates(35.0, 15.0);
    $this->assertNull($areaId2);
}


    public function test_handles_invalid_polygons()
    {
        // Create an area with an invalid polygon (less than 3 points)
        Area::factory()->create([
            'is_active' => true,
            'polygon' => [
                ['lat' => 32.0, 'lng' => 13.0],
                ['lat' => 32.0, 'lng' => 14.0],
            ],
        ]);

        // Test a point - should return null since polygon is invalid and gets filtered out
        $areaId = $this->service->getAreaIdFromCoordinates(32.0, 13.5);

        $this->assertNull($areaId);
    }

    public function test_handles_invalid_polygon_structure()
    {
        // Create an area with invalid polygon point structure
        Area::factory()->create([
            'is_active' => true,
            'polygon' => [
                ['lat' => 32.0, 'lng' => 13.0],
                ['latitude' => 32.0, 'longitude' => 14.0], // Wrong keys
                ['lat' => 33.0, 'lng' => 14.0],
            ],
        ]);

        // Test a point - should return null since polygon structure is invalid
        $areaId = $this->service->getAreaIdFromCoordinates(32.5, 13.5);

        $this->assertNull($areaId);
    }

    public function test_ignores_inactive_areas()
    {
        // Create an inactive area with a polygon
        $area = Area::factory()->create([
            'is_active' => false,
            'polygon' => [
                ['lat' => 32.0, 'lng' => 13.0],
                ['lat' => 33.0, 'lng' => 13.0],
                ['lat' => 33.0, 'lng' => 14.0],
                ['lat' => 32.0, 'lng' => 14.0],
            ],
        ]);

        // Test a point inside the polygon - should return null since area is inactive
        $areaId = $this->service->getAreaIdFromCoordinates(32.5, 13.5);

        $this->assertNull($areaId);
    }

    public function test_handles_json_string_polygons()
    {
        // Create an area with polygon as JSON string (as it might be stored in DB)
        $polygonArray = [
            ['lat' => 32.0, 'lng' => 13.0],
            ['lat' => 33.0, 'lng' => 13.0],
            ['lat' => 33.0, 'lng' => 14.0],
            ['lat' => 32.0, 'lng' => 14.0],
        ];

        $area = Area::factory()->create([
            'is_active' => true,
            'polygon' => json_encode($polygonArray),
        ]);

        // Test a point inside the polygon
        $areaId = $this->service->getAreaIdFromCoordinates(32.5, 13.5);

        $this->assertEquals($area->id, $areaId);
    }

    public function test_clear_cache_method()
    {
        // Create an area
        $area = Area::factory()->create([
            'is_active' => true,
            'polygon' => [
                ['lat' => 32.0, 'lng' => 13.0],
                ['lat' => 33.0, 'lng' => 13.0],
                ['lat' => 33.0, 'lng' => 14.0],
                ['lat' => 32.0, 'lng' => 14.0],
            ],
        ]);

        // Make a call to populate cache
        $this->service->getAreaIdFromCoordinates(32.5, 13.5);

        // Verify cache exists
        $this->assertTrue(Cache::has('active_areas_with_polygons'));

        // Clear cache
        $this->service->clearCache();

        // Verify cache is cleared
        $this->assertFalse(Cache::has('active_areas_with_polygons'));
    }
}
