<?php

namespace Tests\Unit;

use App\Models\Trip;
use App\Models\TripLocation;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TripLocationFullAddressTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function trip_location_can_store_full_address_fields()
    {
        // Create a trip location with full addresses
        $tripLocation = TripLocation::create([
            'departure_address' => 'Vicomte',
            'arrival_address' => 'ksar hlel',
            'departure_full_address' => 'Vicomte Street, Tripoli, Libya',
            'arrival_full_address' => 'Ksar Hlel District, Tripoli, Libya',
            'departure_lat' => 32.888736,
            'departure_lng' => 13.359754,
            'arrival_lat' => 32.868404,
            'arrival_lng' => 13.389752,
        ]);

        // Verify the data was saved correctly
        $this->assertNotNull($tripLocation->id);
        $this->assertEquals('Vicomte', $tripLocation->departure_address);
        $this->assertEquals('ksar hlel', $tripLocation->arrival_address);
        $this->assertEquals('Vicomte Street, Tripoli, Libya', $tripLocation->departure_full_address);
        $this->assertEquals('Ksar Hlel District, Tripoli, Libya', $tripLocation->arrival_full_address);

        // Verify we can retrieve it from database
        $retrieved = TripLocation::find($tripLocation->id);
        $this->assertEquals('Vicomte Street, Tripoli, Libya', $retrieved->departure_full_address);
        $this->assertEquals('Ksar Hlel District, Tripoli, Libya', $retrieved->arrival_full_address);
    }

    /** @test */
    public function trip_location_can_store_null_full_address_fields()
    {
        // Create a trip location without full addresses
        $tripLocation = TripLocation::create([
            'departure_address' => 'Vicomte',
            'arrival_address' => 'ksar hlel',
            'departure_full_address' => null,
            'arrival_full_address' => null,
            'departure_lat' => 32.888736,
            'departure_lng' => 13.359754,
            'arrival_lat' => 32.868404,
            'arrival_lng' => 13.389752,
        ]);

        // Verify the data was saved correctly
        $this->assertNotNull($tripLocation->id);
        $this->assertEquals('Vicomte', $tripLocation->departure_address);
        $this->assertEquals('ksar hlel', $tripLocation->arrival_address);
        $this->assertNull($tripLocation->departure_full_address);
        $this->assertNull($tripLocation->arrival_full_address);
    }

    /** @test */
    public function trip_resource_includes_full_address_fields()
    {
        // Create a trip with trip location
        $trip = Trip::factory()->create();
        
        // Verify the trip resource includes the full address fields
        $resource = new \App\Http\Resources\TripResource($trip);
        $resourceArray = $resource->toArray(request());

        $this->assertArrayHasKey('departureAddress', $resourceArray);
        $this->assertArrayHasKey('arrivalAddress', $resourceArray);
        $this->assertArrayHasKey('departure_full_address', $resourceArray['departureAddress']);
        $this->assertArrayHasKey('arrival_full_address', $resourceArray['arrivalAddress']);
    }
}
