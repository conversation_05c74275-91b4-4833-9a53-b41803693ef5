<?php

use App\Enums\UserStatus;
use App\Models\Driver;
use App\Models\Rider;
use App\Models\Trip;
use Illuminate\Support\Facades\Broadcast;
use Illuminate\Support\Facades\Log;

Broadcast::channel('App.Models.User.{id}', function ($user, $id) {
    return (int) $user->id === (int) $id;
});

Broadcast::channel('driver-location.{driverId}', function ($user, $driverId) {
    // Check if user is the driver
    $isDriver = Driver::where('id', $driverId)
        ->where('user_id', $user->id)
        ->exists();

    $isAssignedRider = Trip::where('driver_id', $driverId)
        ->whereHas('rider', function ($query) use ($user) {
            $query->where('user_id', $user->id);
        })
        ->whereIn('status', ['assigned', 'driver_arriving', 'driver_arrived', 'in_progress'])
        ->exists();

    return $isDriver || $isAssignedRider;
});

// Broadcast::channel('find-driver.{riderId}', function ($user, $riderId) {
//     $rider = Rider::find($riderId);

//     return $rider && $rider->user_id === $user->id;
// });

Broadcast::channel('ride-request.{driverId}', function ($user, $driverId) {
    $driver = Driver::find($driverId);

    return $driver && $driver->user_id === $user->id;
});

Broadcast::channel('trip.{tripId}', function ($user, $tripId) {
    $trip = Trip::find($tripId);

    return $trip && (
        $trip->rider->user_id === $user->id ||
        ($trip->driver && $trip->driver->user_id === $user->id)
    );
});

// Driver presence channel for real-time connection monitoring
Broadcast::channel('driver-presence.{driverId}', function ($user, $driverId) {
    // Verify the user is the driver
    $driver = Driver::where('id', $driverId)
        ->where('user_id', $user->id)
        ->first();

    if (! $driver) {
        Log::warning('Driver presence channel authorization failed', [
            'user_id' => $user->id,
            'requested_driver_id' => $driverId,
        ]);

        return false;
    }

    // Set driver online and update last activity when they connect
    try {
        $user->update([
            'status' => UserStatus::ONLINE->value,
        ]);

        // Update driver's last activity timestamp
        $driver->update(['last_heartbeat' => now()]);

        Log::info('Driver set to ONLINE via WebSocket connection', [
            'driver_id' => $driver->id,
            'user_id' => $user->id,
            'old_status' => $user->getOriginal('status'),
            'new_status' => 'online',
            'last_activity' => now()->toDateTimeString(),
        ]);
    } catch (\Exception $e) {
        Log::error('Failed to set driver online via WebSocket', [
            'driver_id' => $driver->id,
            'user_id' => $user->id,
            'error' => $e->getMessage(),
        ]);
    }

    // Return user data for successful authorization
    return ['id' => $user->id, 'name' => $user->name];
});
