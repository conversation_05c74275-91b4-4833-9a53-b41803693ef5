<?php

namespace App\Filament\Resources\Panel;

use App\Filament\Resources\Panel\AddressLabelResource\Pages;
use App\Models\AddressLabel;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Infolists\Components\IconEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Guava\FilamentIconPicker\Forms\IconPicker;
use Guava\FilamentIconPicker\Tables\IconColumn;

class AddressLabelResource extends Resource
{
    protected static ?string $model = AddressLabel::class;

    protected static ?string $navigationIcon = 'heroicon-o-tag';

    protected static ?string $navigationGroup = 'Configuration';

    protected static ?int $navigationSort = 5;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('label')
                    ->label('Address Label')
                    ->regex('/^[a-zA-Z\s]+$/')
                    ->unique(ignoreRecord: true)
                    ->required()
                    ->maxLength(30)
                    ->columnSpan('full'),

                IconPicker::make('icon')
                    ->label('Address Icon')
                    ->columns([
                        'default' => 1,
                        'sm' => 2,
                        'lg' => 3,
                        '2xl' => 5,
                    ])
                    ->placeholder('Click to select an icon')
                    ->required()
                    ->columnSpan('full'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('label'),
                IconColumn::make('icon')
                    ->label('Icon'),

                TextColumn::make('created_at')
                    ->dateTime(),

            ])

            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\DeleteAction::make(),
                ]),
            ])
            ->bulkActions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\BulkActionGroup::make([
                        // Tables\Actions\DeleteBulkAction::make(),
                    ]), ]),
            ])
            ->paginationPageOptions([5, 10, 25, 50, 100]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                TextEntry::make('label')
                    ->label('Address Label'),

                IconEntry::make('icon')
                    ->label('Address Icon')
                    ->icon(function ($state) {
                        return $state;
                    })
                    ->size(IconEntry\IconEntrySize::TwoExtraLarge),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAddressLabels::route('/'),
        ];
    }
}
