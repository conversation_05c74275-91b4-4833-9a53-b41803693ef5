<?php

namespace App\Filament\Resources\Panel;

use App\Enums\VehicleTypesCategories;
use App\Enums\WeightCategoryEnum;
use App\Filament\Resources\Panel\VehicleTypeResource\Pages;
use App\Models\VehicleType;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Filament\Infolists\Components\Grid;
use Filament\Infolists\Components\IconEntry;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\Tabs as InfolistTab;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\TextEntry\TextEntrySize;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class VehicleTypeResource extends Resource
{
    // Cached regex patterns to compile them once
    private const ARABIC_NAME_REGEX = '/^(?!.*[\x{0660}-\x{0669}\x{06F0}-\x{06F9}0-9])(?!^-)(?!.*-$)[\p{Arabic}-]+( [\p{Arabic}-]+)*$/u';

    private const ENGLISH_NAME_REGEX = '/^[A-Za-z0-9ŠšÉéÈèÊêËë\-]+( [A-Za-z0-9ŠšÉéÈèÊêËë\-]+)*$/';

    protected static ?string $model = VehicleType::class;

    protected static ?string $navigationIcon = 'mdi-train-car';

    protected static ?string $navigationGroup = 'Configuration';

    protected static ?int $navigationSort = 1;

    public static function getModelLabel(): string
    {
        return __('crud.vehicleTypes.itemTitle');
    }

    public static function getPluralModelLabel(): string
    {
        return __('crud.vehicleTypes.collectionTitle');
    }

    public static function getNavigationLabel(): string
    {
        return __('crud.vehicleTypes.collectionTitle');
    }

    public static function form(Form $form): Form
    {
        return $form->schema([
            Fieldset::make('Vehicle Type Details')
                ->columns(2)
                ->schema([
                    TextInput::make('name_ar')
                        ->columnSpan(1)
                        ->rules(['required'])
                        ->markAsRequired()
                        ->string()
                        ->autofocus()
                        ->placeholder('أدخل نوع المركبة باللغة العربية')
                        ->unique(table: 'vehicle_types', column: 'name_ar', ignoreRecord: true)
                        ->validationAttribute('vehicle type name')
                        ->regex(self::ARABIC_NAME_REGEX)
                        ->validationMessages([
                            'regex' => 'The type Name (Arabic) could contain only Arabic letters, with spaces between words allowed.',
                        ])
                        ->label('Type Name (Arabic)')
                        ->maxLength(30)
                        ->live(debounce: 500)
                        ->afterStateUpdated(function (HasForms $livewire, TextInput $component) {
                            $livewire->validateOnly($component->getStatePath(), [
                                $component->getStatePath() => [
                                    'required',
                                    'regex:'.self::ARABIC_NAME_REGEX,
                                ],
                            ], [
                                $component->getStatePath().'.regex' => 'The type Name (Arabic) could contain only Arabic letters, with spaces between words allowed.',
                            ]);
                        }),

                    TextInput::make('name_en')
                        ->columnSpan(1)
                        ->rules(['required'])
                        ->markAsRequired()
                        ->string()
                        ->autofocus()
                        ->autocapitalize('on')
                        ->afterStateUpdated(fn ($set, $state) => $set('name_en', preg_replace('/\s+/', ' ', trim(ucfirst($state)))))
                        ->regex(self::ENGLISH_NAME_REGEX)
                        ->validationMessages([
                            'regex' => 'The type Name (English) could contain only English letters, with spaces between words allowed.',
                        ])
                        ->placeholder('Enter the vehicle type name')
                        ->unique(table: 'vehicle_types', column: 'name_en', ignoreRecord: true)
                        ->validationAttribute('vehicle type name')
                        ->label('Type Name (English)')
                        ->maxLength(30)
                        ->live(debounce: 500)
                        ->afterStateUpdated(function (HasForms $livewire, TextInput $component) {
                            $livewire->validateOnly($component->getStatePath(), [
                                $component->getStatePath() => [
                                    'required',
                                    'regex:'.self::ENGLISH_NAME_REGEX,
                                ],
                            ], [
                                $component->getStatePath().'.regex' => 'The type Name (English) could contain only English letters, with spaces between words allowed.',
                            ]);
                        }),

                    Select::make('category')
                        ->columnSpanFull()
                        ->options(VehicleTypesCategories::class)
                        ->native(false)
                        ->reactive()
                        ->live()
                        ->debounce(100)
                        ->required()
                        ->afterStateUpdated(function ($set, $get) {
                            if ($get('category') === VehicleTypesCategories::Passenger->value) {
                                $set('is_covered', false);
                                $set('weight_category', null);
                            }
                        })
                        ->afterStateHydrated(function ($state, $set) {
                            if ($state === VehicleTypesCategories::Passenger->value) {
                                $set('is_covered', false);
                                $set('weight_category', null);
                            }

                            return $state;
                        }),

                    \Filament\Forms\Components\Grid::make()
                        ->columns(2)
                        ->columnSpanFull()
                        ->reactive()
                        ->visible(fn ($get) => $get('category') === VehicleTypesCategories::Freight->value)
                        ->schema([
                            Toggle::make('is_covered')
                                ->helperText('Check if the vehicle type is covered.')
                                ->label('Covered')
                                ->required()
                                ->rules(['boolean'])
                                ->onIcon('mdi-truck')
                                ->offIcon('mdi-truck-flatbed')
                                ->inline(false),

                            Select::make('weight_category')
                                ->label('Weight Category')
                                ->required()
                                ->native(false)
                                ->options(WeightCategoryEnum::class),
                        ]),

                    Fieldset::make('Descriptions')
                        ->columns(2)
                        ->schema([
                            TextInput::make('description_ar')
                                ->label('Type Description (Arabic)')
                                ->rules(['required'])
                                ->markAsRequired()
                                ->afterStateUpdated(fn ($set, $state) => $set('description_ar', preg_replace('/\s+/', ' ', trim($state))))
                                ->regex(self::ARABIC_NAME_REGEX)
                                ->validationMessages([
                                    'regex' => 'The type Description (Arabic) could contain only Arabic letters, with spaces between words allowed.',
                                ])
                                ->maxLength(50)
                                ->live(debounce: 500)
                                ->afterStateUpdated(function (HasForms $livewire, TextInput $component) {
                                    $livewire->validateOnly($component->getStatePath(), [
                                        $component->getStatePath() => [
                                            'required',
                                            'regex:'.self::ARABIC_NAME_REGEX,
                                        ],
                                    ], [
                                        $component->getStatePath().'.regex' => 'The type Description (Arabic) could contain only Arabic letters, with spaces between words allowed.',
                                    ]);
                                }),

                            TextInput::make('description_en')
                                ->label('Type Description (English)')
                                ->afterStateUpdated(fn ($set, $state) => $set('description_en', preg_replace('/\s+/', ' ', trim($state))))
                                ->regex(self::ENGLISH_NAME_REGEX)
                                ->rules(['required'])
                                ->markAsRequired()
                                ->validationMessages([
                                    'regex' => 'The type Description (English) could contain only English letters, with spaces between words allowed.',
                                ])
                                ->maxLength(50)
                                ->live(debounce: 500)
                                ->afterStateUpdated(function (HasForms $livewire, TextInput $component) {
                                    $livewire->validateOnly($component->getStatePath(), [
                                        $component->getStatePath() => [
                                            'required',
                                            'regex:'.self::ENGLISH_NAME_REGEX,
                                        ],
                                    ], [
                                        $component->getStatePath().'.regex' => 'The type Description (English) could contain only English letters, with spaces between words allowed.',
                                    ]);
                                }),
                        ]),

                    Fieldset::make('Status & Media')
                        ->columns(2)
                        ->schema([
                            Toggle::make('status')
                                ->columnSpan(1)
                                ->required()
                                ->rules(['boolean'])
                                ->default(true)
                                ->onIcon('heroicon-m-check')
                                ->offIcon('heroicon-m-x-mark')
                                ->inline(false)
                                ->helperText(fn ($record) => $record && $record->load('vehicles')->vehicles->isNotEmpty() ? 'This type is associated with one or more vehicles and cannot be deactivated.' : '')
                                ->dehydrated(fn ($state, $record) => $record && $record->load('vehicles')->vehicles->isNotEmpty() && $state === false ? true : $state)
                                ->disabled(fn ($record) => $record && $record->load('vehicles')->vehicles->isNotEmpty()),

                            FileUpload::make('image')
                                ->columnSpan(1)
                                ->image()
                                ->maxSize(5120)
                                ->maxFiles(1)
                                ->directory('vehicle_type_images')
                                ->disk('public')
                                ->visibility('public')
                                ->appendFiles(false)
                                ->required()
                                ->acceptedFileTypes(['image/jpeg', 'image/png', 'image/jpg'])
                                ->maxSize(5120) // Set client-side file size limit to 5MB (in KB)
                                ->rules(['mimes:jpeg,png,jpg', 'max:5120'])
                                ->validationMessages([
                                    'max' => 'The file must not be larger than 5MB.',
                                    'mimes' => 'The file must be a JPEG, PNG, or JPG image.',
                                ])
                                ->imageEditor()
                                ->imageEditorAspectRatios(['1.68:1'])
                                ->reactive()
                                ->removeUploadedFileButtonPosition('left')
                                ->afterStateUpdated(function ($state, $set) {
                                    if ($state && in_array(pathinfo($state, PATHINFO_EXTENSION), ['svg'])) {
                                        $set('image', null);
                                        Notification::make()
                                            ->title('Invalid file type!')
                                            ->danger()
                                            ->body('SVG files are not allowed.')
                                            ->send();
                                    }
                                }),
                        ]),
                ]),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->poll('60s')
            ->modifyQueryUsing(function (Builder $query) {
                $query->whereNotNull('name_ar')
                    ->orderBy('name_en', 'asc');
            })
            ->columns([
                ImageColumn::make('image')
                    ->height(50),

                TextColumn::make('name_ar')
                    ->label('Type Name (Arabic)')
                    ->searchable(['name_ar', 'name_en']),

                TextColumn::make('description_ar')
                    ->label('Type Description (Arabic)'),

                ToggleColumn::make('status')
                    ->label('Status')
                    ->afterStateUpdated(function ($state, $record) {
                        if ($state === false && $record->vehicles()->exists()) {
                            Notification::make()
                                ->title('Deactivation Prevented')
                                ->body('This type cannot be deactivated because it is assigned to one or more vehicles.')
                                ->warning()
                                ->send();
                            $record->status = true;
                            $record->save();
                        } else {
                            $record->status = $state;
                            $record->save();

                            // Send notification for activation or deactivation
                            Notification::make()
                                ->title($state ? 'Activated' : 'Deactivated')
                                ->body('The vehicle type has been '.($state ? 'activated' : 'deactivated').'.')
                                ->success()
                                ->send();
                        }
                    }),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\EditAction::make()
                        ->successNotification(
                            Notification::make()
                                ->success()
                                ->title('Vehicle type updated successfully')
                        ),
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\DeleteAction::make()
                        ->successNotification(
                            Notification::make()
                                ->success()
                                ->title('Vehicle type deleted successfully')
                        )
                        ->before(function ($record, Tables\Actions\DeleteAction $action) {
                            // Check if the VehicleType has any related Vehicles
                            if ($record->vehicles()->exists()) {
                                Notification::make()
                                    ->title('Delete Prevented')
                                    ->body('This type cannot be deleted because it is assigned to one or more vehicles')
                                    ->body('This type cannot be deleted because it is assigned to one or more vehicles')
                                    ->warning()
                                    ->send();

                                $action->cancel();
                            }
                        }),
                ]),
            ])
            ->bulkActions([])
            ->paginationPageOptions([5, 10, 25, 50, 100]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                infolistTab::make('Tabs')
                    ->tabs([
                        infolistTab\Tab::make('Details')
                            ->schema([
                                Grid::make(2)
                                    ->schema([
                                        TextEntry::make('name_ar')
                                            ->label('Type Name (Arabic)'),
                                        TextEntry::make('name_en')
                                            ->label('Type Name (English)'),
                                        TextEntry::make('description_ar')
                                            ->label('Type Description (Arabic)'),
                                        TextEntry::make('description_en')
                                            ->label('Type Description (English)'),

                                        TextEntry::make('category')
                                            ->label('Type Category (Arabic)')
                                            ->formatStateUsing(function ($state) {
                                                if ($state->value === 'passenger') {
                                                    return 'أشخاص';
                                                } else {
                                                    return 'بضائع';
                                                }
                                            })
                                            ->badge(),

                                        TextEntry::make('category')
                                            ->label('Type Category (English)')
                                            ->formatStateUsing(function ($state) {
                                                // Check if the category value is 'passenger'
                                                if ($state->value === 'passenger') {
                                                    return 'Passengers';
                                                } else {
                                                    return $state;
                                                }
                                            })
                                            ->badge(),

                                        TextEntry::make('weight_category')
                                            ->label('Type Weight Category (Arabic)')
                                            ->badge()
                                            ->icon('mdi-weight-kilogram')
                                            ->getStateUsing(fn ($record) => $record->weight_category ?: 'Weight category not assigned')
                                            ->formatStateUsing(function ($state) {
                                                if ($state && $state->value === 'less_than_1000kg') {
                                                    return 'أقل من 1000 كجم';
                                                } else {
                                                    return 'أكثر من 1000 كجم';
                                                }
                                            })
                                            ->visible(fn ($record) => $record->category !== VehicleTypesCategories::Passenger),

                                        TextEntry::make('weight_category')
                                            ->size(TextEntrySize::Large)
                                            ->label('Type Weight Category (English)')
                                            ->badge()
                                            ->icon('mdi-weight-kilogram')
                                            ->visible(fn ($record) => $record->category !== VehicleTypesCategories::Passenger),

                                        IconEntry::make('status')
                                            ->label('Type Status')
                                            ->size(IconEntry\IconEntrySize::TwoExtraLarge)
                                            ->boolean(),

                                        IconEntry::make('is_covered')
                                            ->label('Type is Covered')
                                            ->size(IconEntry\IconEntrySize::TwoExtraLarge)
                                            ->boolean()
                                            ->visible(fn ($record) => $record->category !== VehicleTypesCategories::Passenger),

                                    ]),
                                Section::make()
                                    ->schema([
                                        ImageEntry::make('image')
                                            ->label('')
                                            ->columnSpanFull()
                                            ->extraAttributes(['class' => 'mx-auto']),
                                    ])
                                    ->extraAttributes(['class' => 'flex justify-center']),
                            ]),
                        infolistTab\Tab::make('Pricing')
                            ->schema([
                                Grid::make(2)
                                    ->schema([
                                        TextEntry::make('base_fare_adjustment_type')
                                            ->badge()
                                            ->label('Base Fare Type')
                                            ->getStateUsing(function ($record) {
                                                return $record->base_fare_adjustment_type ?: '-';
                                            }),

                                        TextEntry::make('distance_fare_adjustment_type')
                                            ->badge()
                                            ->label('Distance Fare Type')
                                            ->getStateUsing(function ($record) {
                                                return $record->distance_fare_adjustment_type ?: '-';
                                            }),

                                        TextEntry::make('additional_base_fare')
                                            ->numeric()
                                            ->label('Base Fare Adjustment')
                                            ->visible(fn ($record) => $record->base_fare_adjustment_type === null),

                                        TextEntry::make('additional_base_fare')
                                            ->label('Base Fare Adjustment')
                                            ->suffix(fn ($record) => $record->base_fare_adjustment_type && $record->base_fare_adjustment_type->name === 'fixed' ? ' LYD' : null)
                                            ->visible(fn ($record) => $record->base_fare_adjustment_type && $record->base_fare_adjustment_type->name === 'fixed'),

                                        TextEntry::make('base_fare_adjustment')
                                            ->label('Base Fare Adjustment')
                                            ->suffix(fn ($record) => $record->base_fare_adjustment_type && $record->base_fare_adjustment_type->name === 'percentage' ? ' %' : null)
                                            ->visible(fn ($record) => $record->base_fare_adjustment_type && $record->base_fare_adjustment_type->name === 'percentage'),

                                        TextEntry::make('additional_price_per_km')
                                            ->numeric()
                                            ->label('Distance Fare Adjustment')
                                            ->visible(fn ($record) => $record->base_fare_adjustment_type === null),

                                        TextEntry::make('additional_price_per_km')
                                            ->label('Distance Fare Adjustment')
                                            ->suffix(fn ($record) => $record->distance_fare_adjustment_type && $record->distance_fare_adjustment_type->name === 'fixed' ? ' LYD' : null)
                                            ->visible(fn ($record) => $record->distance_fare_adjustment_type && $record->distance_fare_adjustment_type->name === 'fixed'),

                                        TextEntry::make('distance_fare_adjustment')
                                            ->label('Distance Fare Adjustment')
                                            ->suffix(fn ($record) => $record->distance_fare_adjustment_type && $record->distance_fare_adjustment_type->name === 'percentage' ? ' %' : null)
                                            ->visible(fn ($record) => $record->distance_fare_adjustment_type && $record->distance_fare_adjustment_type->name === 'percentage'),
                                    ]),
                            ]),
                    ])->columnSpanFull(),
            ])
            ->columns(2);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListVehicleTypes::route('/'),
        ];
    }
}
