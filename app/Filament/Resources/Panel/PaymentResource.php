<?php

namespace App\Filament\Resources\Panel;

use App\Enums\Payments\PaymentStatus;
use App\Filament\Resources\Panel\PaymentResource\Pages;
use App\Models\Payment;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Table;
use Illuminate\Support\Number;

class PaymentResource extends Resource
{
    protected static ?string $model = Payment::class;

    protected static ?string $navigationIcon = 'heroicon-o-banknotes';

    protected static ?int $navigationSort = 0;

    protected static ?string $navigationGroup = 'Admin';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Financials')->schema([
                    TextInput::make('amount')
                        ->required()
                        ->numeric()
                        ->minValue(0),
                    TextInput::make('driver_payout')
                        ->required()
                        ->numeric(),
                    TextInput::make('platform_commission')
                        ->required()
                        ->numeric()
                        ->minValue(0),
                    TextInput::make('taxes')
                        ->required()
                        ->numeric()
                        ->minValue(0),
                    TextInput::make('total_amount')
                        ->label('Total Amount')
                        ->numeric()
                        ->disabled() // Make it non-editable
                        ->reactive(),
                    // ->afterStateUpdated(function (Get $get, Set $set) {
                    //     $driverPayout = $get('driver_payout') ?? 0;
                    //     $platformCommission = $get('platform_commission') ?? 0;
                    //     $taxes = $get('taxes') ?? 0;
                    //     $total = $driverPayout + $platformCommission + $taxes;
                    //     $set('total_amount', $total);
                    // }),
                ])->columns(3),

                Select::make('payment_method')
                    ->options([
                        'cash' => 'Cash',
                        'card' => 'Card',
                        'wallet' => 'Wallet',
                    ])
                    ->required()
                    ->reactive()
                    ->afterStateUpdated(function ($state, Set $set) {
                        $set('show_transaction_id', in_array($state, ['card', 'wallet']));
                    }),

                Select::make('payment_status')
                    ->options(PaymentStatus::class)
                    ->required(),

                TextInput::make('transaction_id')
                    ->label('Transaction ID')
                    ->visible(fn (Get $get) => in_array($get('payment_method'), ['card', 'wallet']))
                    ->maxLength(255),

                // Placeholder::make('total_amount_placeholder')
                //     ->label('Total Amount')
                //     ->content(function (Get $get, Set $set) {
                //         // Initialize total amount
                //         $total = 0;

                //         // Fetch the required fields
                //         $driverPayout = $get('driver_payout');
                //         $platformCommission = $get('platform_commission');
                //         $taxes = $get('taxes');

                //         // Calculate the total amount
                //         $total = ($driverPayout ?? 0) + ($platformCommission ?? 0) + ($taxes ?? 0);

                //         // Set the calculated total amount in a hidden field
                //         $set('total_amount', $total);

                //         // Return the formatted total amount (e.g., as USD)
                //         return Number::currency($total, 'USD');
                //     }),

                // Hidden::make('total_amount')
                //     ->default(0),

            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('amount')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('driver_payout')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('platform_commission')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('taxes')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('payment_method'),

                Tables\Columns\TextColumn::make('payment_status'),

                Tables\Columns\TextColumn::make('transaction_id')
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('payment_status')
                    ->label('Status')
                    ->multiple()
                    ->options(PaymentStatus::class),
            ])
            ->filtersTriggerAction(
                fn (Action $action) => $action
                    ->slideOver()
                    ->modalIcon('heroicon-o-funnel')
                    ->button()
                    ->size('md')
                    ->icon('heroicon-o-funnel')
                    ->label('Filter'),
            )
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->paginationPageOptions([5, 10, 25, 50, 100]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPayments::route('/'),
            'create' => Pages\CreatePayment::route('/create'),
            'edit' => Pages\EditPayment::route('/{record}/edit'),
        ];
    }
}
