<?php

namespace App\Filament\Resources\Panel\VehicleClassificationRuleResource\Pages;

use App\Filament\Resources\Panel\VehicleClassificationRuleResource;
use App\Services\VehicleClassificationService;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;

class CreateVehicleClassificationRule extends CreateRecord
{
    protected static string $resource = VehicleClassificationRuleResource::class;

    protected function getClassificationService(): VehicleClassificationService
    {
        return app(VehicleClassificationService::class);
    }

    protected function beforeValidate(): void
    {
        $this->validateBrandModelCombinations();
    }

    protected function validateBrandModelCombinations(): void
    {
        $qualifications = $this->data['qualifications'] ?? [];

        if (empty($qualifications)) {
            return;
        }

        $service = $this->getClassificationService();

        // Check for cross-rule conflicts (between different rules)
        $crossRuleConflicts = $service->validateBrandModelCombinations($qualifications);

        // Check for within-rule conflicts (within same rule qualifications)
        $withinRuleConflicts = $service->validateWithinRuleBrandModelCombinations($qualifications);

        $hasConflicts = false;
        $allErrorMessages = [];

        // Handle cross-rule conflicts
        if (! empty($crossRuleConflicts)) {
            $formattedConflicts = $service->formatValidationConflicts($crossRuleConflicts);

            foreach ($formattedConflicts as $conflict) {
                $qualificationIndex = $conflict['qualification_index'];
                $this->addError("data.qualifications.{$qualificationIndex}.brands", $conflict['message']);
                $allErrorMessages[] = $conflict['message'];
            }
            $hasConflicts = true;
        }

        // Handle within-rule conflicts
        if (! empty($withinRuleConflicts)) {
            $formattedConflicts = $service->formatWithinRuleValidationConflicts($withinRuleConflicts);

            foreach ($formattedConflicts as $conflict) {
                $qualificationIndex = $conflict['qualification_index'];
                $this->addError("data.qualifications.{$qualificationIndex}.models", $conflict['message']);
                $allErrorMessages[] = $conflict['message'];
            }
            $hasConflicts = true;
        }

        if ($hasConflicts) {
            // Show notification with all conflicts
            $uniqueMessages = array_unique($allErrorMessages);
            $conflictCount = count($uniqueMessages);

            Notification::make()
                ->danger()
                ->title("Validation Error - {$conflictCount} conflict(s) detected")
                ->body(collect($uniqueMessages)->map(fn ($msg) => "• {$msg}")->implode("\n"))
                ->persistent()
                ->send();

            // Throw validation exception to prevent creation
            $validator = \Illuminate\Support\Facades\Validator::make($this->data, []);
            $validator->errors()->add('qualifications', 'Brand-Model conflicts detected');
            throw new \Illuminate\Validation\ValidationException($validator);
        }
    }

    protected function getCreatedNotification(): ?Notification
    {
        $vehicleType = $this->record->vehicleType;
        $qualificationCount = $this->record->qualifications->count();

        return Notification::make()
            ->success()
            ->title("Vehicle classification rule for '{$vehicleType->name_en}' created successfully")
            ->body("Rule created with {$qualificationCount} qualification(s)");
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
