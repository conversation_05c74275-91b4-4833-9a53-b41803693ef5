<?php

namespace App\Filament\Resources\Panel\VehicleClassificationRuleResource\Pages;

use App\Enums\VehicleTypesCategories;
use App\Filament\Resources\Panel\VehicleClassificationRuleResource;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Support\Facades\Log;

class EditVehicleClassificationRule extends EditRecord
{
    protected static string $resource = VehicleClassificationRuleResource::class;

    protected function beforeValidate(): void
    {
        $this->validateBrandModelCombinations();
    }

    protected function beforeSave(): void
    {
        $data = $this->data;
        $originalCategory = $this->record->category;
        $newCategory = $data['category'] ?? null;

        Log::channel('daily')->info('Before save info:', [
            'originalCategory' => $originalCategory?->value ?? $originalCategory,
            'newCategory' => $newCategory,
            'data' => $data,
        ]);

        // Only process if the category has changed
        if ($newCategory && $originalCategory && $originalCategory->value !== $newCategory) {
            if (isset($data['qualifications']) && is_array($data['qualifications'])) {
                $qualifications = $data['qualifications'];

                // Log::channel('daily')->info('Original qualifications data:', $qualifications);

                foreach ($qualifications as $key => $qualification) {
                    if (! is_array($qualification)) {
                        continue;
                    }

                    // Safely cast to arrays first
                    $qualifications[$key]['brands'] = $qualification['brands'] ?? null;
                    $qualifications[$key]['models'] = isset($qualification['models']) ? (array) $qualification['models'] : [];
                    $qualifications[$key]['seat_numbers'] = isset($qualification['seat_numbers']) ? (array) $qualification['seat_numbers'] : [];

                    if ($newCategory === VehicleTypesCategories::Freight->value) {
                        // Clear passenger-only fields
                        $qualifications[$key]['min_year'] = null;
                        $qualifications[$key]['max_year'] = null;
                        $qualifications[$key]['brands'] = [];
                        $qualifications[$key]['models'] = [];
                        $qualifications[$key]['seat_numbers'] = [];

                        // Preserve freight fields or set defaults
                        if (! isset($qualification['is_covered'])) {
                            $qualifications[$key]['is_covered'] = false;
                        }

                        if (! isset($qualification['weight_category']) || empty($qualification['weight_category'])) {
                            $qualifications[$key]['weight_category'] = 'less_than_1000kg';
                        }
                    } else {
                        // Clear freight-only fields
                        $qualifications[$key]['is_covered'] = null;
                        $qualifications[$key]['weight_category'] = null;
                    }
                }

                $this->data['qualifications'] = $qualifications;
                // Log::channel('daily')->info('Modified qualifications for save:', $this->data['qualifications']);
            }
        }
    }

    protected function validateBrandModelCombinations(): void
    {
        $qualifications = $this->data['qualifications'] ?? [];

        if (empty($qualifications)) {
            return;
        }

        $service = new \App\Services\VehicleClassificationService;

        // Check for cross-rule conflicts (between different rules)
        $crossRuleConflicts = $service->validateBrandModelCombinations($qualifications, $this->record->id);

        // Check for within-rule conflicts (within same rule qualifications)
        $withinRuleConflicts = $service->validateWithinRuleBrandModelCombinations($qualifications);

        $hasConflicts = false;
        $allErrorMessages = [];

        // Handle cross-rule conflicts
        if (! empty($crossRuleConflicts)) {
            $formattedConflicts = $service->formatValidationConflicts($crossRuleConflicts);

            foreach ($formattedConflicts as $conflict) {
                $qualificationIndex = $conflict['qualification_index'];
                $this->addError("data.qualifications.{$qualificationIndex}.brands", $conflict['message']);
                $allErrorMessages[] = $conflict['message'];
            }
            $hasConflicts = true;
        }

        // Handle within-rule conflicts
        if (! empty($withinRuleConflicts)) {
            $formattedConflicts = $service->formatWithinRuleValidationConflicts($withinRuleConflicts);

            foreach ($formattedConflicts as $conflict) {
                $qualificationIndex = $conflict['qualification_index'];
                $this->addError("data.qualifications.{$qualificationIndex}.models", $conflict['message']);
                $allErrorMessages[] = $conflict['message'];
            }
            $hasConflicts = true;
        }

        if ($hasConflicts) {
            // Show notification with all conflicts
            $errorMessage = 'Brand-Model conflicts detected:'.PHP_EOL.implode(PHP_EOL, array_unique($allErrorMessages));

            Notification::make()
                ->danger()
                ->title('Validation Error')
                ->body($errorMessage)
                ->persistent()
                ->send();

            // Throw validation exception to prevent saving
            $validator = \Illuminate\Support\Facades\Validator::make($this->data, []);
            $validator->errors()->add('qualifications', 'Brand-Model conflicts detected');
            throw new \Illuminate\Validation\ValidationException($validator);
        }
    }

    protected function afterSave(): void
    {
        // Refresh and ensure all qualifications are properly cleaned up
        $this->record->refresh()->load('qualifications');
        $currentCategory = $this->record->category;

        $categoryValue = $currentCategory?->value ?? null;

        // Log::channel('daily')->info('After save info:', [
        //     'currentCategory' => $categoryValue,
        //     'record_id' => $this->record->id,
        //     'qualification_count' => $this->record->qualifications->count(),
        // ]);

        $this->record->qualifications->each(function ($qualification) use ($categoryValue) {
            // Log::channel('daily')->info('Processing qualification after save:', [
            //     'id' => $qualification->id,
            //     'is_covered' => $qualification->is_covered,
            //     'weight_category' => $qualification->weight_category,
            // ]);

            $updateData = [];

            // Critical fix: Compare with string value, not the enum object
            if ($categoryValue === VehicleTypesCategories::Freight->value) {
                // For freight category
                $updateData = [
                    'min_year' => null,
                    'max_year' => null,
                    'brands' => json_encode([]),
                    'models' => json_encode([]),
                    'seat_numbers' => json_encode([]),
                ];

                // Preserve existing values or set defaults for freight fields
                // DO NOT set these to null!
                $updateData['is_covered'] = $qualification->is_covered ?? false;
                $updateData['weight_category'] = ! empty($qualification->weight_category)
                    ? $qualification->weight_category
                    : 'less_than_1000kg';
            } else {
                // For passenger category
                $updateData = [
                    'is_covered' => null,
                    'weight_category' => null,
                ];
            }

            // Log::channel('daily')->info('Updating qualification with:', $updateData);
            $qualification->update($updateData);

            // Verify the update worked
            $qualification->refresh();
            // Log::channel('daily')->info('Qualification after update:', [
            //     'id' => $qualification->id,
            //     'is_covered' => $qualification->is_covered,
            //     'weight_category' => $qualification->weight_category,
            // ]);
        });
    }

    protected function getSavedNotification(): ?Notification
    {
        return Notification::make()
            ->title('Vehicle classification rule updated successfully')
            ->success();
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl();
    }
}
