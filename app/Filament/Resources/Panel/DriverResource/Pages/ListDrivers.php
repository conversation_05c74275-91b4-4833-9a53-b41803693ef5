<?php

namespace App\Filament\Resources\Panel\DriverResource\Pages;

use App\Enums\Drivers\DriverGlobalStatus;
use App\Filament\Resources\Panel\DriverResource;
use App\Filament\Resources\Panel\DriverResource\Widgets\DriverGenderChart;
use App\Filament\Resources\Panel\DriverResource\Widgets\DriversCurrentlyOnTrip;
use App\Filament\Resources\Panel\DriverResource\Widgets\DriverStats;
use App\Filament\Resources\Panel\DriverResource\Widgets\DriverStatsOverview;
use App\Filament\Resources\Panel\DriverResource\Widgets\NumberOfRegistration;
use App\Models\Driver;
use Filament\Resources\Components\Tab;
use Filament\Resources\Pages\ListRecords;
use Illuminate\Database\Eloquent\Builder;

class ListDrivers extends ListRecords
{
    protected static string $resource = DriverResource::class;

    public ?string $activeTab = 'all';

    public function mount(): void
    {
        $tab = request()->get('activeTab');
        if (in_array($tab, ['all', 'pending', 'deleted', 'requests'])) {
            $this->activeTab = $tab;
        }

        parent::mount();
    }

    public function updatedActiveTab(): void
    {
        $this->tableFilters['global_status']['global_status'] = [];

        $this->redirectRoute('filament.admin.resources.panel.drivers.index', [
            'activeTab' => $this->activeTab,
        ]);

        $this->dispatch('$refresh');
    }

    protected function getHeaderWidgets(): array
    {
        return [
            DriverStatsOverview::class,
            NumberOfRegistration::class,
            DriverStats::class,
            DriverGenderChart::class,
            DriversCurrentlyOnTrip::class,
        ];
    }

    protected function resolveRecord($key): ?\Illuminate\Database\Eloquent\Model
    {
        return static::getModel()::withTrashed()->findOrFail($key);
    }

    public function getTabs(): array
    {
        // Reduce DB queries to one grouped query
        $counts = Driver::query()
            ->whereIn('global_status', [
                DriverGlobalStatus::in_progress->value,
                DriverGlobalStatus::pending->value,
            ])
            ->selectRaw('global_status, COUNT(*) as count')
            ->groupBy('global_status')
            ->pluck('count', 'global_status');

        return [
            'all' => Tab::make('All')
                ->icon('mdi-account-multiple')
                ->modifyQueryUsing(fn (Builder $query) => $query->whereNotIn('global_status', [
                    DriverGlobalStatus::deleted->value,
                    DriverGlobalStatus::in_progress->value,
                    DriverGlobalStatus::pending->value,
                ])
                ),

            'requests' => Tab::make('Requests')
                ->icon('mdi-account-arrow-down')
                ->badge(fn () => $counts[DriverGlobalStatus::in_progress->value] ?? 0)
                ->modifyQueryUsing(fn (Builder $query) => $query->where('global_status', DriverGlobalStatus::in_progress->value)
                ),

            'pending' => Tab::make('Pending')
                ->icon('mdi-account-clock')
                ->badge(fn () => $counts[DriverGlobalStatus::pending->value] ?? 0)
                ->modifyQueryUsing(fn (Builder $query) => $query->where('global_status', DriverGlobalStatus::pending->value)
                ),

            'deleted' => Tab::make('Deleted')
                ->icon('mdi-account-multiple-remove')
                ->modifyQueryUsing(fn (Builder $query) => $query->withTrashed()
                    ->where('global_status', DriverGlobalStatus::deleted->value)
                    ->orderByDesc('updated_at')
                ),
        ];
    }
}
