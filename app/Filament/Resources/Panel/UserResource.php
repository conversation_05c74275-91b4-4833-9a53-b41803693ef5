<?php

namespace App\Filament\Resources\Panel;

use App\Filament\Resources\Panel\UserResource\Pages;
use App\Models\User;
use App\Rules\UniqueTransformedPhoneNumber;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Infolists\Components\Section as InfolistSection;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;
use Ysfkaya\FilamentPhoneInput\Infolists\PhoneEntry;
use Ysfkaya\FilamentPhoneInput\PhoneInputNumberType;
use Ysfkaya\FilamentPhoneInput\Tables\PhoneColumn;

class UserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-user-group';

    protected static ?string $navigationGroup = 'Users';

    protected static ?int $navigationSort = 13;

    public $currentStep = 1;

    public static function canView(Model $record): bool
    {
        return auth()->user()?->can('view', $record);
    }

    public static function canEdit(Model $record): bool
    {
        return auth()->user()?->can('update', $record);
    }

    public static function canDelete(Model $record): bool
    {
        return auth()->user()?->can('delete', $record);

    }

    public static function getGlobalSearchResultTitle(Model $record): string|Htmlable
    {
        return $record->name;
    }

    public static function getGloballySearchableAttributes(): array
    {
        return ['name', 'phone_number'];
    }

    public static function getGlobalSearchResultDetails(Model $record): array
    {
        return [
            'Phone' => '',
            'phone_number' => $record->phone_number,
        ];
    }

    public static function getModelLabel(): string
    {
        return __('crud.users.itemTitle');
    }

    public static function getPluralModelLabel(): string
    {
        return __('crud.users.collectionTitle');
    }

    public static function getNavigationLabel(): string
    {
        return __('crud.users.collectionTitle');
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Grid::make(1)
                    ->schema([
                        // User Details Section
                        Section::make(__('User Details'))
                            ->description(__('Provide the user’s basic information.'))
                            ->aside()
                            ->collapsible()
                            ->schema([
                                Grid::make(['default' => 2, 'sm' => 1])
                                    ->schema([
                                        TextInput::make('email')
                                            ->label(__('Email'))
                                            ->required()
                                            ->rule([
                                                'required',
                                                'email',
                                                'regex:/^(?!.*[._-]{2})[A-Za-z0-9]+[A-Za-z0-9._-]*[A-Za-z0-9]@([A-Za-z0-9-]+\.)+[A-Za-z]{2,6}$/',
                                            ])
                                            ->unique('users', 'email', ignoreRecord: true)
                                            ->placeholder(__('Enter email address'))
                                            ->helperText(__('This email must be unique.')),

                                        Hidden::make('name')->live(),
                                        Hidden::make('last_name')->live(),

                                        TextInput::make('username')
                                            ->label(__('Username'))
                                            ->placeholder(__('Enter Username'))
                                            ->rules(['required', 'max:30'])
                                            ->extraInputAttributes([
                                                'maxlength' => 30,
                                                'oninput' => 'this.value = this.value.slice(0, 30);',
                                            ])
                                            ->placeholder(__('Enter username'))
                                            ->afterStateHydrated(function ($state, callable $set, $record) {
                                                // Hydrate the 'username' field with the combined 'name' and 'last_name' on load
                                                if ($record) {
                                                    $set('username', trim($record->name.' '.$record->last_name));
                                                }
                                            })
                                            ->afterStateUpdated(function ($state, callable $set) {
                                                // Update 'name' and 'last_name' based on the 'username' field
                                                $parts = explode(' ', trim($state), 2);
                                                $set('name', $parts[0] ?? '');
                                                $set('last_name', $parts[1] ?? '');
                                            }),
                                    ]),

                                TextInput::make('phone_number')
                                    ->label(__('Phone Number'))
                                    ->prefix(new HtmlString('<img src="'.asset('images/lybia.png').'" alt="Libya Flag" class="w-6 h-4 inline mr-1"> '))
                                    ->extraInputAttributes([
                                        'inputmode' => 'numeric',
                                        'pattern' => '[0-9-]*',
                                        'maxlength' => 10,
                                        'oninput' => "this.value = this.value.replace(/[^0-9]/g, '').slice(0, 9); if (this.value.length > 2) this.value = this.value.slice(0, 2) + '-' + this.value.slice(2);",
                                    ])
                                    ->rules(function ($livewire) {
                                        $rules = [
                                            'required',
                                            'regex:/^(91|92|93|94|95)-\d{7}$/',
                                        ];

                                        // Add the UniqueTransformedPhoneNumber rule with the current record ID if editing
                                        if ($livewire instanceof Pages\EditUser && $livewire->record) {
                                            $rules[] = new UniqueTransformedPhoneNumber($livewire->record->id);
                                        } else {
                                            $rules[] = new UniqueTransformedPhoneNumber;
                                        }

                                        return $rules;
                                    })
                                    ->helperText('Must start with 91, 92, 93, 94, or 95 followed by seven digits.')
                                    ->placeholder('00-0000000'),
                            ]),

                        // Role and Permissions Section
                        Section::make(__('Role and Permissions'))
                            ->description(__('Assign a role and permissions to the user.'))
                            ->aside()
                            ->collapsible()
                            ->hidden(fn (?Model $record) => auth()->user()?->id === $record?->id
                            && auth()->user()?->hasAnyRole([
                                'super_admin', 'admin', 'Trips Manager', 'Drivers Manager',
                                'Riders Manager', 'Cars Manager', 'Areas Manager', 'Pricing Manager',
                                'Chief Financial Officer', 'Chief Human Resources Officer',
                            ]))
                            ->schema([
                                Grid::make(['default' => 1])
                                    ->schema([
                                        Select::make('role')
                                            ->label(__('Role'))
                                            ->relationship('roles', 'name', function ($query) {
                                                $excludedRoles = ['super_admin'];

                                                $user = auth()->user();

                                                if ($user->hasRole('admin')) {
                                                    $excludedRoles[] = 'admin';
                                                } elseif ($user->hasAnyRole([
                                                    'Trips Manager', 'Drivers Manager',
                                                    'Riders Manager', 'Cars Manager', 'Areas Manager', 'Pricing Manager',
                                                    'Chief Financial Officer', 'Chief Human Resources Officer',
                                                ])) {
                                                    // rh can't assing role admin or rh to other user
                                                    $excludedRoles = array_merge($excludedRoles, ['admin', 'Chief Human Resources Officer']);
                                                }

                                                return $query->whereNotIn('name', $excludedRoles);
                                            })
                                            ->native(false)
                                            ->searchable()
                                            ->preload()
                                            ->required()
                                            ->placeholder(__('Select a role'))
                                            ->afterStateUpdated(fn ($record) => $record?->update(['updated_at' => now()]))
                                            ->visible(fn (?Model $record) => ! (auth()->user()?->hasRole('super_admin') && auth()->user()?->id === $record?->id)),
                                    ]),

                                Hidden::make('password')
                                    ->default(fn (?Model $record) => $record ? null : Hash::make(Str::random(32)))
                                    ->dehydrated(fn (?Model $record) => $record === null),

                                Hidden::make('type')
                                    ->default('admin'),
                            ]),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->query(
                User::query()
                    ->where('type', 'admin')
                    ->when(function () {
                        $user = auth()->user();

                        return ! $user || ! $user->roles()->where('name', 'super_admin')->exists();
                    }, function ($query) {
                        $query->whereDoesntHave('roles', function ($q) {
                            $q->where('name', 'super_admin');
                        });
                    })
            )
            ->poll('60s')
            ->columns([

                TextColumn::make(name: 'email')
                    ->searchable()
                    ->sortable(),
                TextColumn::make('name')
                    ->searchable()
                    ->label('Username')
                    ->formatStateUsing(function ($state, User $record) {
                        return $record->name.' '.$record->last_name;
                    })
                    ->searchable(),
                PhoneColumn::make('phone_number')
                    ->displayFormat(PhoneInputNumberType::INTERNATIONAL)
                    ->label('Phone Number')
                    ->searchable(),

                // Role
                TextColumn::make('roles.name')
                    ->sortable()
                    ->searchable()
                    ->label('Role'),
            ])
            ->filters([])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\EditAction::make()
                        ->successNotification(
                            Notification::make()
                                ->success()
                                ->title('User updated successfully')
                        ),
                    Tables\Actions\ViewAction::make()
                        ->modal(),
                    Tables\Actions\DeleteAction::make()
                        ->successNotification(
                            Notification::make()
                                ->success()
                                ->title('User deleted successfully')
                        ),
                ]),
            ])
            ->defaultSort('id', 'desc')
            ->paginationPageOptions([5, 10, 25, 50, 100]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                // User Information Section
                InfolistSection::make(__('User Information'))
                    ->description(__('All user-related details.'))
                    ->columns(2)
                    ->schema([
                        // Personal Information
                        TextEntry::make('full_name')
                            ->label(__('Username')),

                        // Contact Information
                        PhoneEntry::make('phone_number')->displayFormat(PhoneInputNumberType::INTERNATIONAL)
                            ->label(__('Phone Number'))
                            ->icon('heroicon-m-phone')
                            ->iconColor('primary'),

                        TextEntry::make('email')
                            ->label(__('Email Address'))
                            ->visible(fn ($record) => $record->email !== null)
                            ->icon('heroicon-m-envelope')
                            ->iconColor('primary'),

                        // Account Details
                        TextEntry::make('roles.name')
                            ->label(__('Account Type'))
                            ->formatStateUsing(fn ($state) => ucfirst($state))
                            ->iconColor('primary'),
                    ]),

                // Timestamps Section
                InfolistSection::make(__('Timestamps'))
                    ->description(__('Record creation and last update times.'))
                    ->columns(2)
                    ->schema([
                        TextEntry::make('created_at')
                            ->label(__('Created At'))
                            ->dateTime(),

                        TextEntry::make('updated_at')
                            ->label(__('Last Updated'))
                            ->dateTime(),
                    ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'view' => Pages\ViewUser::route('/{record}'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
        ];
    }
}
