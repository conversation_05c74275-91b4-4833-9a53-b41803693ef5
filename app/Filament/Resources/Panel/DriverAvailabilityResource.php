<?php

namespace App\Filament\Resources\Panel;

use App\Enums\Drivers\DriverAvailabilities;
use App\Filament\Resources\Panel\DriverAvailabilityResource\Pages;
use App\Models\DriverAvailability;
use Filament\Forms\Components\RichEditor;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TimePicker;
use Filament\Forms\Components\ToggleButtons;
use Filament\Forms\Form;
use Filament\Infolists\Components;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Support\Enums\IconPosition;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class DriverAvailabilityResource extends Resource
{
    protected static ?string $model = DriverAvailability::class;

    protected static ?string $navigationIcon = 'heroicon-o-clock';

    protected static bool $shouldRegisterNavigation = false;

    protected static ?string $navigationGroup = 'Admin';

    public static function getModelLabel(): string
    {
        return __('crud.driverAvailabilities.itemTitle');
    }

    public static function getPluralModelLabel(): string
    {
        return __('crud.driverAvailabilities.collectionTitle');
    }

    public static function getNavigationLabel(): string
    {
        return __('crud.driverAvailabilities.collectionTitle');
    }

    public static function form(Form $form): Form
    {
        return $form->schema([

            Section::make('Driver Assignment')
                ->description('Assign a driver and set their availability status.')
                ->schema([
                    Select::make('driver_id')
                        ->required()
                        ->relationship('driver', 'license_number')
                        ->searchable()
                        ->preload()
                        ->native(false),

                    ToggleButtons::make('status')
                        ->inline()
                        ->default('available')
                        ->required()
                        ->options(DriverAvailabilities::class)->columnSpan(1),
                ])->columns(2),

            Section::make('Availability Schedule')
                ->description('Define the start and end time for the driver\'s availability.')
                ->schema([
                    TimePicker::make('start_time')
                        ->required()
                        ->native(false),

                    TimePicker::make('end_time')
                        ->required()
                        ->native(false),
                ])->columns(2),

            RichEditor::make('notes')
                ->nullable()
                ->string()
                ->columnSpan(2),
        ]);

    }

    public static function table(Table $table): Table
    {
        return $table
            ->poll('60s')
            ->columns([

                TextColumn::make('driver.user.full_name')
                    ->label('Driver Name')
                    ->sortable()
                    ->searchable(),

                TextColumn::make('status')
                    ->label('Status')
                    ->badge(),

                TextColumn::make('start_time')
                    ->sortable(),

                TextColumn::make('end_time')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Status')
                    ->multiple()
                    ->options(DriverAvailabilities::class),

                Tables\Filters\Filter::make('available_time')
                    ->label('Available During')
                    ->form([
                        TimePicker::make('start_time')
                            ->label('Start Time')
                            ->required()
                            ->native(false),

                        TimePicker::make('end_time')
                            ->label('End Time')
                            ->required()
                            ->native(false),
                    ])
                    ->query(function (Builder $query, array $data) {
                        if (! empty($data['start_time']) && ! empty($data['end_time'])) {
                            $query->where(function ($query) use ($data) {
                                $query->where(function ($query) use ($data) {
                                    $query->where('start_time', '<=', $data['end_time'])
                                        ->where('end_time', '>=', $data['start_time']);
                                });
                            });
                        }
                    }),
            ])
            ->filtersTriggerAction(
                fn (Action $action) => $action
                    ->slideOver()
                    ->modalIcon('heroicon-o-funnel')
                    ->button()
                    ->size('md')
                    ->icon('heroicon-o-funnel')
                    ->label('Filter'),
            )
            ->actions([
                // Tables\Actions\DeleteAction::make(),
                // Tables\Actions\EditAction::make(),
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('id', 'desc')
            ->paginationPageOptions([5, 10, 25, 50, 100]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Components\Section::make()
                    ->schema([
                        Components\Split::make([
                            Components\Grid::make(2)
                                ->schema([
                                    Components\Group::make([
                                        Components\TextEntry::make('driver.user.full_name')
                                            ->size(TextEntry\TextEntrySize::Large)
                                            ->icon('heroicon-m-user')
                                            ->iconPosition(IconPosition::Before),

                                        Components\TextEntry::make('status')
                                            ->size(TextEntry\TextEntrySize::Large)
                                            ->badge(),
                                    ]),
                                    Components\Group::make([
                                        Components\TextEntry::make('start_time')
                                            ->dateTime(),
                                        Components\TextEntry::make('end_time')
                                            ->dateTime(),
                                    ]),
                                ]),
                            Components\ImageEntry::make('image')
                                ->hiddenLabel()
                                ->grow(false),
                        ])->from('lg'),
                    ]),
                Components\Section::make('Driver Notes')
                    ->schema([
                        Components\TextEntry::make('notes')
                            ->prose()
                            ->markdown()
                            ->hiddenLabel(),
                    ])
                    ->collapsible(),
            ]);
    }

    public static function getRelations(): array
    {
        return [];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDriverAvailabilities::route('/'),
            // 'create' => Pages\CreateDriverAvailability::route('/create'),
            // 'view' => Pages\ViewDriverAvailability::route('/{record}'),
            // 'edit' => Pages\EditDriverAvailability::route('/{record}/edit'),
        ];
    }
}
