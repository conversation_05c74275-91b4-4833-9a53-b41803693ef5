<?php

namespace App\Filament\Resources\Panel;

use App\Enums\GenderEnum;
use App\Enums\RiderAvailabilities;
use App\Enums\RiderGlobalStatus;
use App\Filament\Resources\Panel\RiderResource\Pages;
use App\Filament\Resources\Panel\RiderResource\Pages\EditRider;
use App\Filament\Traits\HasDateRangeFilter;
use App\Helpers\TripHelper;
use App\Livewire\Riders\RiderTripHistory;
use App\Models\Rider;
use App\Models\TripCancellation;
use App\Models\User;
use App\Notifications\Firebase_notifications\NotifyUser;
use App\Notifications\Firebase_notifications\Silent_Notify_User;
use App\Rules\UniqueTransformedPhoneNumber;
use Cheesegrits\FilamentGoogleMaps\Fields\Map;
use Filament\Forms\Components\Fieldset as ComponentsFieldset;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\ToggleButtons;
use Filament\Forms\Form;
use Filament\Infolists\Components\Grid as InfoListGrid;
use Filament\Infolists\Components\Group;
use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\Section as InfolistSection;
use Filament\Infolists\Components\Split;
use Filament\Infolists\Components\Tabs;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use IbrahimBougaoua\FilamentRatingStar\Columns\Components\RatingStar as ColumnRatingStar;
use IbrahimBougaoua\FilamentRatingStar\Entries\Components\RatingStar as InfolistRatingStar;
use IbrahimBougaoua\FilamentRatingStar\Forms\Components\RatingStar as FormRatingStar;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Carbon;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;
use PhpParser\Node\Stmt\Label;
use Ysfkaya\FilamentPhoneInput\Infolists\PhoneEntry;
use Ysfkaya\FilamentPhoneInput\PhoneInputNumberType;
use Ysfkaya\FilamentPhoneInput\Tables\PhoneColumn;

class RiderResource extends Resource
{
    use HasDateRangeFilter;

    protected static ?string $model = Rider::class;

    protected static ?string $navigationIcon = 'mdi-hail';

    protected static ?string $navigationGroup = 'Admin';

    public static function getModelLabel(): string
    {
        return __('crud.riders.itemTitle');
    }

    public static function getPluralModelLabel(): string
    {
        return __('crud.riders.collectionTitle');
    }

    public static function getNavigationLabel(): string
    {
        return __('crud.riders.collectionTitle');
    }

    public static function form(Form $form): Form
    {
        return $form->schema([
            Section::make('User Information')
                ->schema([
                    TextInput::make('phone_number')
                        ->label(__('Phone Number'))
                        ->prefix(new HtmlString('<img src="'.asset('images/lybia.png').'" alt="Libya Flag" class="w-6 h-4 inline mr-1"> '))
                        ->extraInputAttributes([
                            'inputmode' => 'numeric',
                            'pattern' => '[0-9-]*$',
                            'maxlength' => 10,
                            'oninput' => "this.value = this.value.replace(/[^0-9]/g, '').slice(0, 9); if (this.value.length > 2) this.value = this.value.slice(0, 2) + '-' + this.value.slice(2);",
                        ])
                        ->rules(function ($livewire) {
                            $rules = [
                                'required',
                                'regex:/^(91|92|93|94|95)-\d{7}$/',
                            ];

                            // Add the UniqueTransformedPhoneNumber rule with the current record ID if editing
                            if ($livewire instanceof EditRider && $livewire->record && $livewire->record->user) {
                                // When editing, exclude the user's ID, not the driver ID
                                $rules[] = new UniqueTransformedPhoneNumber($livewire->record->user->id);
                            } else {
                                // When creating a new driver
                                $rules[] = new UniqueTransformedPhoneNumber;
                            }

                            return $rules;
                        })
                        ->afterStateHydrated(function (TextInput $component, $state) {
                            if (Str::startsWith($state, '+218')) {
                                $cleanNumber = substr($state, 4);
                                $formatted = substr($cleanNumber, 0, 2).'-'.substr($cleanNumber, 2);
                                $component->state($formatted);
                            }
                        })
                        ->helperText('Must start with 91, 92, 93, 94, or 95 followed by seven digits.')
                        ->columns(1)
                        ->placeholder('00-0000000'),

                    TextInput::make('address')
                        ->label('Address')
                        ->rules(['required', 'max:60'])
                        ->maxLength(60)
                        ->debounce(100)
                        ->markAsRequired(),
                    Grid::make()
                        ->extraAttributes(['class' => 'hideMap', 'style' => 'display:none;'])->schema([
                            Map::make('location')
                                ->reactive()
                                ->afterStateUpdated(function ($state, callable $get, callable $set) {
                                    logger()->info('Reverse Geocode Result:', [$state]);
                                    $set('latitude', $state['lat']);
                                    $set('longitude', $state['lng']);
                                })
                                ->live()
                                ->geolocate()
                                ->autocomplete('address', ['geocode'], countries: ['LY'])
                                // ->autocompleteReverse(true)
                                ->reverseGeocode([
                                    'street' => '%n %S',
                                    'city' => '%L',
                                    'state' => '%A1',
                                ]),
                        ], ),
                    Hidden::make('street'),
                    Hidden::make('city'),
                    Hidden::make('state'),
                    Hidden::make('latitude'),
                    Hidden::make('longitude'),
                    Hidden::make('type')
                        ->default('passenger')
                        ->dehydrated(),

                    ComponentsFieldset::make()
                        ->schema([
                            Toggle::make('global_status')
                                ->label('Unblock Rider')
                                ->reactive()
                                ->offIcon('mdi-restore')
                                ->onIcon('heroicon-o-no-symbol')
                                ->inline(false)
                                ->hidden(fn ($record) => $record->global_status !== RiderGlobalStatus::blocked)
                                ->offColor('warning')
                                ->onColor('danger')
                                ->beforeStateDehydrated(fn ($state, $record) => $state = $record->global_status === RiderGlobalStatus::blocked)
                                ->afterStateUpdated(function ($state, $record, callable $set) {
                                    // Only validate when trying to unblock (state = false when toggled off)
                                    if (! $state && $record && TripHelper::riderHasActiveTrips($record)) {
                                        Notification::make()
                                            ->title('Unblocking Prevented')
                                            ->body('This rider cannot be unblocked because they have active trips in progress.')
                                            ->warning()
                                            ->send();

                                        // Revert the toggle state
                                        $set('global_status', true);
                                    }
                                })
                                ->helperText('Toggle to unblock this rider'),

                            Toggle::make('block_toggle')
                                ->label('Block Rider')
                                ->reactive()
                                ->inline(false)
                                ->onIcon('heroicon-o-no-symbol')
                                ->hidden(fn ($record) => $record->global_status === RiderGlobalStatus::blocked)
                                ->onColor('danger')
                                ->afterStateUpdated(function ($state, $record, callable $set) {
                                    // Only validate when trying to block (state = true when toggled on)
                                    if ($state && $record && TripHelper::riderHasActiveTrips($record)) {
                                        Notification::make()
                                            ->title('Blocking Prevented')
                                            ->body('This rider cannot be blocked because they have active trips in progress.')
                                            ->warning()
                                            ->send();

                                        // Revert the toggle state
                                        $set('block_toggle', false);
                                    }
                                })
                                ->helperText('Toggle to block this rider'),
                        ])
                        ->extraAttributes(['class' => 'border-none p-0'])
                        ->hidden(fn ($record) => $record->global_status === RiderGlobalStatus::pending),

                    Hidden::make('password')
                        ->dehydrated(fn (?Model $record) => $record === null),

                ])->columns(2)
                ->columnSpan(['lg' => fn (?Rider $record) => $record === null ? 2 : 3]),

            Section::make([
                Textarea::make('blocking_reason')
                    ->label('Blocking Reason')
                    ->rows(3)
                    ->disabled(fn ($get) => ! $get('block_toggle'))
                    ->autosize(false)
                    ->rules(['required', 'max:60'])
                    ->maxLength(50)
                    ->cols(10)
                    ->extraInputAttributes(fn ($get, $record) => $record->global_status->value == RiderGlobalStatus::blocked->value
        ? ['class' => 'bg-gray-100 dark:bg-gray-800']
        : []
                    ),

            ])->hidden(function ($get, $record) {
                if ($record->global_status->value == RiderGlobalStatus::blocked->value && ! $get('global_status')) {
                    return true;
                }

                if ($record->global_status->value != RiderGlobalStatus::blocked->value) {
                    return ! $get('block_toggle');
                }

                return false;
            }),
        ])->columns(2);
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->poll('60s')
            ->modifyQueryUsing(fn (Builder $query) => $query->with([
                'user.address' => function ($query) {
                    $query->orderBy('created_at', 'asc');
                },
                'user.preferences',
                'trips',
            ]))
            ->columns([
                TextColumn::make('id')
                    ->label('ID')
                    ->searchable(),

                ImageColumn::make('user.cover_picture')
                    ->label('Profile Photo')
                    ->size(60)
                    ->circular()
                    ->defaultImageUrl(url('/images/avatar.png')),

                TextColumn::make('user.name')
                    ->label('First Name')
                    ->searchable(),

                TextColumn::make('user.last_name')
                    ->label('Last Name')
                    ->searchable(),

                TextColumn::make('user.gender')
                    ->getStateUsing(function ($record) {
                        if (! $record->user->gender) {
                            return '';
                        }

                        return $record->user->gender->value === 'male' ? 'ذكر' : 'أنثى';
                    })
                    ->badge()
                    ->label('Gender'),

                PhoneColumn::make('user.phone_number')
                    ->label('Phone Number')
                    ->searchable()
                    ->displayFormat(PhoneInputNumberType::INTERNATIONAL),

                TextColumn::make('user.address.address')
                    ->label('Address')
                    ->limit(30)
                    ->getStateUsing(function ($record) {
                        return $record->user?->address?->first()?->address;
                    }),
                ColumnRatingStar::make('average_rider_rating')
                    ->label('Ratings')
                    ->size('sm'),

                TextColumn::make('global_status')
                    ->badge()
                    ->label('Status'),
            ])->persistSearchInSession()

            ->filters([

                self::dateRangeFilter(),

                Filter::make('Gender')
                    ->form([
                        Select::make('Gender')
                            ->options(GenderEnum::class),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        if (isset($data['Gender']) && $data['Gender'] == 'male') {
                            return $query->whereHas('user', function (Builder $query) use ($data) {
                                $query->where('gender', '=', $data['Gender'])
                                    ->whereNotNull('gender');
                            });
                        } elseif (isset($data['Gender']) && $data['Gender'] == 'female') {
                            return $query->whereHas('user', function (Builder $query) use ($data) {
                                $query->where('gender', '=', $data['Gender'])
                                    ->whereNotNull('gender');
                            });
                        }

                        return $query;
                    })
                    ->indicateUsing(function (array $data): ?string {
                        if (! isset($data['Gender']) || ! $data['Gender']) {
                            return null;
                        }

                        return ucfirst(str_replace('_', ' ', $data['Gender']));
                    }),

                SelectFilter::make('global_status')
                    ->options(
                        collect(RiderGlobalStatus::cases())
                            ->reject(fn ($case) => $case === RiderGlobalStatus::deleted)
                            ->reject(fn ($case) => $case === RiderGlobalStatus::pending)
                            ->mapWithKeys(fn ($case) => [$case->value => $case->name])
                            ->toArray()
                    )
                    ->visible(function ($livewire) {
                        // Only show this filter in the 'all' tab
                        return $livewire->activeTab === 'all';
                    })
                    ->multiple(),

                // SelectFilter::make('user_address')
                //     ->label('Address')
                //     ->options(fn () => Rider::getFirstAddresses()) // Fetch distinct first-created addresses
                //     ->searchable()
                //     ->query(function ($query, $data) {
                //         if (! empty($data['value'])) {
                //             $addressValue = $data['value']; // The selected address (e.g., "Sousse")

                //             $query->whereExists(function ($subQuery) use ($addressValue) {
                //                 $subQuery->selectRaw(1)
                //                     ->from('users')
                //                     ->whereColumn('riders.user_id', 'users.id')
                //                     ->whereExists(function ($addressQuery) use ($addressValue) {
                //                         $addressQuery->selectRaw(1)
                //                             ->from('addresses')
                //                             ->whereColumn('addresses.addressable_id', 'users.id')
                //                             ->where('addresses.addressable_type', User::class)
                //                             ->where('addresses.address', 'LIKE', "%{$addressValue}%")
                //                             ->orderBy('addresses.created_at', 'asc')
                //                             ->limit(1);
                //                     });
                //             });
                //         }
                //     }),

                SelectFilter::make('user_address')
                    ->label('Address')
                    ->options(fn () => Rider::getFirstAddresses() ?? ['' => 'No addresses found'])
                    ->searchable()
                    ->query(function ($query, $data) {
                        if (! empty($data['value'])) {
                            $addressValue = $data['value'];

                            $query->whereExists(function ($subQuery) use ($addressValue) {
                                $subQuery->selectRaw(1)
                                    ->from('users')
                                    ->whereColumn('riders.user_id', 'users.id')
                                    ->whereExists(function ($addressQuery) use ($addressValue) {
                                        $addressQuery->selectRaw(1)
                                            ->from('addresses')
                                            ->whereColumn('addresses.addressable_id', 'users.id')
                                            ->where('addresses.addressable_type', User::class)
                                            ->where('addresses.address', 'LIKE', "%{$addressValue}%")
                                            ->orderBy('addresses.created_at', 'asc')
                                            ->limit(1);
                                    });
                            });
                        }
                        $query->withTrashed();
                    }),

                Filter::make('Rating Star')
                    ->form([
                        FormRatingStar::make('average_rider_rating')
                            ->label('Ratings'),
                    ])
                    ->query(function ($query, $data) {
                        if (isset($data['average_rider_rating'])) {
                            $query->where('average_rider_rating', $data['average_rider_rating']);
                        }
                    })
                    ->indicateUsing(function (array $data): ?string {
                        if (! isset($data['average_rider_rating'])) {
                            return null;
                        }

                        return 'Ratings: '.$data['average_rider_rating'];
                    }),

            ])->persistFiltersInSession()

            ->filtersTriggerAction(
                fn (Action $action) => $action
                    ->slideOver()
                    ->modalIcon('heroicon-o-funnel')
                    ->button()
                    ->size('md')
                    ->icon('heroicon-o-funnel')
                    ->label('Filter'),
            )
            ->actions([
                ActionGroup::make([
                    Tables\Actions\EditAction::make()
                        ->visible(fn ($record) => $record->global_status->value != RiderGlobalStatus::deleted->value),
                    Tables\Actions\ViewAction::make(),

                    Action::make('block')
                        ->visible(fn (Rider $record) => $record->global_status->value === RiderGlobalStatus::active->value)
                        ->icon('heroicon-o-no-symbol')
                        ->requiresConfirmation()
                        ->modalHeading('Block Rider')
                        ->modalDescription('Please enter a reason for blocking this rider.')
                        ->form([
                            Textarea::make('blocking_reason')
                                ->label('Blocking Reason')
                                ->rules(['required', 'max:50'])
                                ->maxLength(50)
                                ->rules(function ($record) {
                                    return $record->user->status->value != RiderAvailabilities::busy->value
                                        ? ['required']
                                        : [];
                                })
                                ->markAsRequired(),
                        ])
                        ->action(function (array $data, Rider $record) {
                            // Check if rider has active trips using the hasActiveTrips function
                            if (TripHelper::riderHasActiveTrips($record)) {
                                Notification::make()
                                    ->title('Blocking Prevented')
                                    ->body('This rider cannot be blocked because they have active trips in progress.')
                                    ->warning()
                                    ->send();

                                return;
                            }

                            // Keep the existing busy status check as additional validation
                            if ($record->user->status->value === RiderAvailabilities::busy->value) {
                                Notification::make()
                                    ->title('Rider is busy')
                                    ->body('Cannot block rider. Rider is currently on a trip.')
                                    ->warning()
                                    ->send();

                                return;
                            }

                            $record->update([
                                'previous_global_status' => $record->global_status,
                                'global_status' => RiderGlobalStatus::blocked,
                            ]);

                            $record->user->update([
                                'blocking_reason' => $data['blocking_reason'],
                            ]);

                            // Success notification
                            Notification::make()
                                ->success()
                                ->title('Rider Blocked Successfully')
                                ->send();
                        })
                        ->after(function ($record, $data) {
                            // Only execute after logic if the action was successful (no active trips)
                            if (! TripHelper::riderHasActiveTrips($record) && $record->user->status->value !== RiderAvailabilities::busy->value) {
                                $reason = $data['blocking_reason'];

                                $notificationData = [
                                    'title' => 'تم حظر الراكب',
                                    'description' => "تم حظر حسابك بسبب $reason. لن تتمكن من حجز الرحلات حتى يتم حل المشكلة",
                                ];

                                if ($record->user->tokens->isNotEmpty()) {
                                    $record->user->notifyNow(new NotifyUser($record->user->id, $notificationData));
                                }
                            }
                        })
                        ->color('warning'),

                    Action::make('Unblock')
                        ->visible(fn (Rider $record) => $record->global_status === RiderGlobalStatus::blocked && $record->global_status !== RiderGlobalStatus::deleted)
                        ->icon('heroicon-o-no-symbol')
                        ->requiresConfirmation()
                        ->modalDescription('Are you sure you want to unblock this rider ?')
                        ->color('warning')
                        ->modalHeading('Unblock Rider')
                        // ->form([
                        //     Section::make([
                        //         ToggleButtons::make('global_status')
                        //             // ->label('Rider Status')
                        //             ->inline()
                        //             ->required()
                        //             ->reactive()
                        //             ->options([
                        //                 'pending' => 'pending',
                        //                 'active' => 'active',
                        //             ])
                        //             ->icons([
                        //                 'pending' => 'heroicon-o-clock',
                        //                 'active' => 'heroicon-o-check',
                        //             ])
                        //             ->colors([
                        //                 'pending' => 'gray',
                        //                 'active' => 'success',
                        //             ])
                        //             ->helperText('Select the user status after unblocking'),
                        //     ])->columnSpan(['lg' => 1]),
                        // ])
                        ->successNotification(
                            Notification::make()
                                ->success()
                                ->title('Rider Unblocked successfully')
                        )
                        ->after(function ($record, $data) {
                            $record->update([
                                'global_status' => $record->previous_global_status,
                                'previous_global_status' => RiderGlobalStatus::blocked->value,
                            ]);
                            $record->user->update(['blocking_reason' => null]);
                            $data = [
                                'title' => 'تم إلغاء حظر الراكب',
                                'description' => 'تم إلغاء حظر حسابك. يمكنك الآن حجز الرحلات مجددًا',
                            ];
                            if ($record->user->tokens->isNotEmpty()) {
                                $record->user->notifyNow(new NotifyUser($record->user->id, $data));
                            }
                            TripCancellation::where('user_id', $record->user->id)->delete();
                        }),

                    Tables\Actions\DeleteAction::make()
                        ->action(function (Rider $record) {
                            // Check if rider has active trips using the hasActiveTrips function
                            if (TripHelper::riderHasActiveTrips($record)) {
                                Notification::make()
                                    ->title('Deletion Prevented')
                                    ->body('This rider cannot be deleted because they have active trips in progress.')
                                    ->warning()
                                    ->send();

                                return;
                            }

                            // Keep the existing busy status check as additional validation
                            if ($record->user->status->value === RiderAvailabilities::busy->value) {
                                Notification::make()
                                    ->title('Rider is busy')
                                    ->body('Cannot delete rider. Rider is currently on a trip.')
                                    ->warning()
                                    ->send();

                                return;
                            }

                            // Proceed with deletion if no active trips and not busy
                            $record->update(['global_status' => RiderGlobalStatus::deleted->value]);
                            $record->delete();

                            // Send success notification
                            Notification::make()
                                ->success()
                                ->title('Rider deleted successfully')
                                ->send();

                            $record->user->notify(new Silent_Notify_User($record, ['title' => 'Rider_deleted']));

                        })
                        ->requiresConfirmation()
                        ->modalDescription('Are you sure you want to delete this rider?'),

                    // ->modalSubmitActionLabel('Yes, delete it'),

                    // Tables\Actions\RestoreAction::make()
                    //     ->visible(fn ($record) => $record->trashed())
                    //     ->form([
                    //         Section::make([
                    //             ToggleButtons::make('global_status')
                    //                 // ->label('Rider Status')
                    //                 ->inline()
                    //                 ->required()
                    //                 ->reactive()
                    //                 ->options([
                    //                     'pending' => 'pending',
                    //                     'active' => 'active',
                    //                 ])
                    //                 ->icons([
                    //                     'pending' => 'heroicon-o-clock',
                    //                     'active' => 'heroicon-o-check',
                    //                 ])
                    //                 ->colors([
                    //                     'pending' => 'gray',
                    //                     'active' => 'success',
                    //                 ])
                    //                 ->helperText('Select the user status after restoration'),
                    //         ])->columnSpan(['lg' => 1]),
                    //     ])
                    //     ->successNotification(
                    //         Notification::make()
                    //             ->success()
                    //             ->title('Rider restored successfully')
                    //     )
                    //     ->action(function ($record, $data) {
                    //         $globalStatus = $data['global_status'];

                    //         // Update the global_status of the record
                    //         $record->global_status = $globalStatus;
                    //         $record->save();
                    //     }),
                ]),

            ])

            ->defaultSort('created_at', 'desc');
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([

                Tabs::make('Tabs')
                    ->tabs([
                        Tabs\Tab::make('Rider Information')
                            ->schema([
                                InfolistSection::make('Personal Informations')
                                    ->schema([
                                        Split::make([
                                            InfoListGrid::make(3)
                                                ->schema([
                                                    Group::make([
                                                        ImageEntry::make('user.cover_picture')
                                                            ->defaultImageUrl(url('/images/avatar.png'))
                                                            ->circular()
                                                            ->size(200)
                                                            ->label('Profile photo'),
                                                    ])->columnSpan(1),
                                                    Group::make([
                                                        TextEntry::make('user.name')
                                                            ->label('First Name')
                                                            ->getStateUsing(function ($record) {
                                                                return $record->user->name ?: 'No first name provided';
                                                            }),

                                                        TextEntry::make('user.last_name')
                                                            ->label('Last Name')
                                                            ->getStateUsing(function ($record) {
                                                                return $record->user->last_name ?: 'No last name provided';
                                                            }),

                                                        TextEntry::make('user.gender')
                                                            ->label('Gender')
                                                            ->badge()
                                                            ->icon('heroicon-s-user-circle')
                                                            ->getStateUsing(function ($record) {
                                                                if (! $record->user->gender) {
                                                                    return 'No gender provided';
                                                                }

                                                                return $record->user->gender->value === 'male' ? 'ذكر' : 'أنثى';
                                                            }),

                                                        TextEntry::make('user.address.address')
                                                            ->label('Address')
                                                            ->icon('heroicon-o-map')
                                                            ->getStateUsing(function ($record) {
                                                                return $record->user->address?->first()?->address
                                                                ?: 'No address provided';
                                                            })
                                                            ->iconColor('primary'),

                                                        PhoneEntry::make('user.phone_number')
                                                            ->displayFormat(PhoneInputNumberType::INTERNATIONAL)
                                                            ->label('Primary Phone')
                                                            ->label('Phone Number')
                                                            ->iconColor('primary')
                                                            ->icon('heroicon-o-device-phone-mobile')
                                                        // ->url(fn ($record) => "tel:{$record->user->phone_number}")
                                                            ->getStateUsing(function ($record) {
                                                                return $record->user->phone_number
                                                                ? $record->user->phone_number
                                                                : 'No phone number provided';
                                                            }),
                                                        // Group::make([

                                                        TextEntry::make('user.email')
                                                            ->label('Email Address')
                                                            ->icon('heroicon-o-envelope')
                                                            ->iconColor('primary')
                                                            // ->url(fn ($record) => $record->user->email ? "mailto:{$record->user->email}" : '#')
                                                            ->getStateUsing(function ($record) {
                                                                return $record->user->email
                                                                    ? $record->user->email
                                                                    : 'No email address provided';
                                                            }),

                                                        TextEntry::make('user.phone_verified_at')
                                                            ->label('Registration Date')
                                                            ->badge()
                                                            ->icon('heroicon-s-calendar-days')
                                                            ->getStateUsing(function ($record) {
                                                                return $record->user->created_at
                                                                    ? Carbon::parse($record->user->created_at)->format('Y-m-d')
                                                                    : ' ';
                                                            }),

                                                        TextEntry::make('women_only_service')
                                                            ->label('Women Services')
                                                            ->badge()
                                                            ->visible(fn ($record) => $record->user->gender?->value === 'female')
                                                            ->icon('heroicon-o-user-group')
                                                            ->getStateUsing(function ($record) {
                                                                return $record->user->preferences?->driver_gender === 'female' ? 'Active' : 'Not Active';
                                                            })
                                                            ->color(fn ($record) => $record->user->preferences?->driver_gender === 'female' ? 'success' : 'danger'),

                                                        TextEntry::make('global_status')
                                                            ->label('Account Status')
                                                            ->badge(),

                                                    ])->columnSpan(2)->columns(2),

                                                ]),
                                        ])->from('lg'),
                                    ]),

                                InfolistSection::make('Blocking Reason')
                                    ->schema([
                                        TextEntry::make('user.blocking_reason')
                                            ->hiddenLabel(),
                                    ])->visible(fn ($record) => $record->global_status->value === RiderGlobalStatus::blocked->value),
                                InfolistSection::make('Rider performance')
                                    ->schema([

                                        InfoListGrid::make(3)
                                            ->schema([
                                                TextEntry::make('trips')
                                                    ->label('Number Of Trips')
                                                    ->badge()
                                                    ->icon('mdi-taxi')
                                                    ->getStateUsing(function ($record) {
                                                        return $record->trips->count();
                                                    }),

                                                InfolistRatingStar::make('average_rider_rating')
                                                    ->size('md')
                                                    ->label('Ratings'),

                                                TextEntry::make('trips')
                                                    ->label('Cancellation Rate')
                                                    ->badge()
                                                    ->icon('heroicon-s-x-mark')
                                                    ->getStateUsing(function ($record) {
                                                        $totalTrips = $record->trips->count();

                                                        $canceledTrips = $record->trips->where('status', 'canceled')
                                                            ->where('cancelled_by', 'rider')->count();

                                                        if ($totalTrips > 0) {
                                                            $cancellationRate = ($canceledTrips / $totalTrips) * 100;

                                                            return round($cancellationRate, 2).'%';
                                                        } else {
                                                            return 'No cancellation rate provided';
                                                        }
                                                    }),
                                            ]),
                                    ]),
                            ]),
                        Tabs\Tab::make('Trips History')
                            ->schema(
                                function ($record) {
                                    return [
                                        \Filament\Infolists\Components\Livewire::make(RiderTripHistory::class, ['rider' => $record])->key(Str::random()),
                                    ];
                                }
                            )->visible(fn ($record) => in_array($record->global_status, [RiderGlobalStatus::blocked, RiderGlobalStatus::active])),
                    ])->columnSpanFull(),

            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRiders::route('/'),
            'create' => Pages\CreateRider::route('/create'),
            'view' => Pages\ViewRider::route('/{record}'),
            'edit' => Pages\EditRider::route('/{record}/edit'),
            'view_trip' => Pages\ViewRiderTrip::route('/{record}/trip/{tripId}'),
        ];
    }
}
