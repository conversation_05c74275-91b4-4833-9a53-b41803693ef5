<?php

namespace App\Filament\Resources\AvailableIconResource\Pages;

use App\Filament\Resources\AvailableIconResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListAvailableIcons extends ListRecords
{
    protected static string $resource = AvailableIconResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
