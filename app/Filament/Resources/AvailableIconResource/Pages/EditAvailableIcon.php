<?php

namespace App\Filament\Resources\AvailableIconResource\Pages;

use App\Filament\Resources\AvailableIconResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditAvailableIcon extends EditRecord
{
    protected static string $resource = AvailableIconResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
