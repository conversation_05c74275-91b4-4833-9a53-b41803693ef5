<?php

namespace App\Filament\Resources;

use App\Filament\Resources\AvailableIconResource\Pages;
use App\Models\AvailableIcon;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Guava\FilamentIconPicker\Forms\IconPicker;

class AvailableIconResource extends Resource
{
    protected static ?string $model = AvailableIcon::class;

    protected static ?string $navigationIcon = 'heroicon-o-star';

    protected static ?string $navigationGroup = 'Configuration';

    protected static ?int $navigationSort = 3;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                
                IconPicker::make('icon_name')
                    ->label('Address Icon')
                    ->columns([
                        'default' => 1,
                        'sm' => 2,
                        'lg' => 3,
                        '2xl' => 5,
                    ])
                    ->placeholder('Click to select an icon')
                    ->required()
                    ->columnSpan('full'),

                Forms\Components\TextInput::make('display_name')
                    ->required()
                    ->maxLength(255)
                    ->helperText('Human-readable name for the icon')
                    ->placeholder('Home'),

                Forms\Components\Select::make('category')
                    ->required()
                    ->options([
                        'general' => 'General',
                        'location' => 'Location',
                        'business' => 'Business',
                        'food' => 'Food & Dining',
                        'transport' => 'Transportation',
                        'health' => 'Health & Medical',
                        'entertainment' => 'Entertainment',
                        'shopping' => 'Shopping',
                    ])
                    ->default('general'),
                Forms\Components\Toggle::make('status')
                    ->required()
                    ->default(true)
                    ->helperText('Whether this icon is available for use'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ViewColumn::make('icon_preview')
                    ->label('Icon')
                    ->view('filament.tables.columns.icon-preview')
                    ->sortable(false),
                Tables\Columns\TextColumn::make('icon_name')
                    ->searchable()
                    ->copyable(),
                Tables\Columns\TextColumn::make('display_name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('category')
                    ->searchable()
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        'general' => 'gray',
                        'location' => 'success',
                        'business' => 'info',
                        'food' => 'warning',
                        'transport' => 'primary',
                        'health' => 'danger',
                        'entertainment' => 'secondary',
                        'shopping' => 'success',
                        default => 'gray',
                    }),
                Tables\Columns\IconColumn::make('status')
                    ->boolean(),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('category')
                    ->options([
                        'general' => 'General',
                        'location' => 'Location',
                        'business' => 'Business',
                        'food' => 'Food & Dining',
                        'transport' => 'Transportation',
                        'health' => 'Health & Medical',
                        'entertainment' => 'Entertainment',
                        'shopping' => 'Shopping',
                    ]),
                Tables\Filters\TernaryFilter::make('status')
                    ->label('Status')
                    ->boolean()
                    ->trueLabel('Active')
                    ->falseLabel('Inactive')
                    ->native(false),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAvailableIcons::route('/'),
            'create' => Pages\CreateAvailableIcon::route('/create'),
            'edit' => Pages\EditAvailableIcon::route('/{record}/edit'),
        ];
    }
}
