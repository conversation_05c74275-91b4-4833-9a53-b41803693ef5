<?php

namespace App\Filament\Pages\Auth;

use DiogoGPinto\AuthUIEnhancer\Pages\Auth\Concerns\HasCustomLayout;
use Filament\Actions\Action;
use Filament\Facades\Filament;
use Filament\Forms\Form;
use Filament\Pages\Auth\Login;
use Illuminate\Contracts\Support\Htmlable;

class InvalidInvitation extends Login
{
    use HasCustomLayout;

    public ?string $message = null;

    public function mount(): void
    {
        if (Filament::auth()->check()) {
            redirect()->intended(Filament::getUrl());
        }

        $this->message = session('invitation_error', 'This invitation link is invalid or has expired.');
    }

    public function form(Form $form): Form
    {
        // Return empty form - we don't want any form fields
        return $form->schema([]);
    }

    public function getTitle(): string|Htmlable
    {
        return __('Invalid Invitation');
    }

    public function getHeading(): string|Htmlable
    {
        return __('Invalid Invitation');
    }

    public function getSubheading(): string|Htmlable
    {
        return $this->message;
    }

    /**
     * @return array<Action>
     */
    protected function getFormActions(): array
    {
        return [
            Action::make('login')
                ->label(__('Go to Login'))
                ->url(route('filament.admin.auth.login'))
                ->color('primary'),
        ];
    }

    // Override authenticate to prevent any login attempts
    public function authenticate(): ?\Filament\Http\Responses\Auth\Contracts\LoginResponse
    {
        // Do nothing - this page is just for display
        return null;
    }
}
