<?php

namespace App\Filament\Pages\Auth;

use App\Services\UserInvitationService;
use <PERSON><PERSON><PERSON><PERSON>\LivewireRateLimiting\Exceptions\TooManyRequestsException;
use Dan<PERSON><PERSON>rin\LivewireRateLimiting\WithRateLimiting;
use DiogoGPinto\AuthUIEnhancer\Pages\Auth\Concerns\HasCustomLayout;
use Filament\Actions\Action;
use Filament\Facades\Filament;
use Filament\Forms\Components\Component;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Http\Responses\Auth\Contracts\RegistrationResponse;
use Filament\Notifications\Notification;
use Filament\Pages\Auth\Register;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rules\Password;
use Livewire\Attributes\Locked;

class AcceptInvitation extends Register
{
    use HasCustomLayout;
    use WithRateLimiting;

    /**
     * @var view-string
     */
    protected static string $view = 'filament-panels::pages.auth.login';

    #[Locked]
    public ?string $email = null;

    #[Locked]
    public ?string $token = null;

    // public ?string $name = '';

    // public ?string $last_name = '';

    // public ?string $phone_number = '';

    public ?string $password = '';

    public ?string $passwordConfirmation = '';

    public function mount(?string $email = null, ?string $token = null): void
    {
        if (Filament::auth()->check()) {
            redirect()->intended(Filament::getUrl());
        }
        $this->token = $token ?? request()->query('token');
        $this->email = $email ?? request()->query('email');

        // Validate invitation
        if (! $this->token || ! $this->email) {
            $this->redirectToInvalidPage('Missing invitation parameters.');

            return;
        }

        $invitationService = app(UserInvitationService::class);
        $validation = $invitationService->validateInvitation($this->token, $this->email);
        if (! $validation['valid']) {
            $this->redirectToInvalidPage($validation['message']);

            return;
        }

        $this->form->fill([
            'email' => $this->email,
        ]);
    }

    public function register(): ?RegistrationResponse
    {
        try {
            $this->rateLimit(2);
        } catch (TooManyRequestsException $exception) {
            $this->getRateLimitedNotification($exception)?->send();

            return null;
        }

        // Validate the form first
        $this->form->validate();

        $data = $this->form->getState();

        try {
            $userData = [
                // 'name' => $data['name'],
                // 'last_name' => $data['last_name'],
            ];

            // Add phone number if provided
            // if (! empty($data['phone_number'])) {
            //     $userData['phone_number'] = '+218'.preg_replace('/[^0-9]/', '', $data['phone_number']);
            // }

            $invitationService = app(UserInvitationService::class);
            $user = $invitationService->acceptInvitation(
                $this->token,
                $this->email,
                $data['password'],
                $userData
            );

            // Log the user in
            Auth::login($user);

            Notification::make()
                ->title("Welcome {$user->full_name}! Your account has been created successfully.")
                ->success()
                ->send();

            return app(RegistrationResponse::class);

        } catch (\Exception $e) {
            Notification::make()
                ->title('Failed to accept invitation')
                ->body($e->getMessage())
                ->danger()
                ->send();

            return null;
        }
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                $this->getEmailFormComponent(),
                // $this->getNameFormComponent(),
                // $this->getLastNameFormComponent(),
                // $this->getPhoneNumberFormComponent(),
                $this->getPasswordFormComponent(),
                $this->getPasswordConfirmationFormComponent(),
            ]);
    }

    protected function getEmailFormComponent(): Component
    {
        return TextInput::make('email')
            ->label(__('Email Address'))
            ->disabled()
            ->autofocus();
    }

    // protected function getNameFormComponent(): Component
    // {
    //     return TextInput::make('name')
    //         ->label(__('First Name'))
    //         ->required()
    //         ->maxLength(255);
    // }

    // protected function getLastNameFormComponent(): Component
    // {
    //     return TextInput::make('last_name')
    //         ->label(__('Last Name'))
    //         ->maxLength(255);
    // }

    // protected function getPhoneNumberFormComponent(): Component
    // {
    //     return TextInput::make('phone_number')
    //         ->label(__('Phone Number (Optional)'))
    //         ->prefix('+218')
    //         ->placeholder('91-2345678')
    //         ->helperText(__('Format: 91-2345678 (without country code)'))
    //         ->maxLength(20);
    // }

    protected function getPasswordFormComponent(): Component
    {
        return TextInput::make('password')
            ->label(__('Password'))
            ->password()
            ->revealable(filament()->arePasswordsRevealable())
            ->rule(['required', Password::defaults()]);
    }

    protected function getPasswordConfirmationFormComponent(): Component
    {
        return TextInput::make('passwordConfirmation')
            ->label(__('Confirm Password'))
            ->password()
            ->revealable(filament()->arePasswordsRevealable())
            ->rule(['required'])
            ->same('password')
            ->dehydrated(false);
    }

    public function getTitle(): string|Htmlable
    {
        return __('Accept Invitation');
    }

    public function getHeading(): string|Htmlable
    {
        return __('Complete Your Account Setup');
    }

    /**
     * @return array<Action>
     */
    protected function getFormActions(): array
    {
        return [
            $this->getRegisterFormAction(),
        ];
    }

    public function getRegisterFormAction(): Action
    {
        return Action::make('register')
            ->label(__('Create Account'))
            ->action('register');
    }

    protected function getRateLimitedNotification(TooManyRequestsException $exception): ?Notification
    {
        return Notification::make()
            ->title(__('Too many attempts'))
            ->body(__('Please try again in :seconds seconds.', [
                'seconds' => $exception->secondsUntilAvailable,
            ]))
            ->danger();
    }

    protected function redirectToInvalidPage(string $message): void
    {
        session()->flash('invitation_error', $message);
        redirect()->route('invitation.invalid');
    }

    // Override any authenticate method that might be inherited
    public function authenticate(): void
    {
        // Do nothing - we handle authentication in register() method
    }
}
