<?php

namespace App\Observers;

use App\Models\User;
use App\Services\UserInvitationService;
use Illuminate\Support\Facades\Log;

class UserObserver
{
    /**
     * Handle the User "created" event.
     */
    public function created(User $user): void
    {
        if ($user->type === 'admin' && $user->email) {
            try {
                $invitationService = app(UserInvitationService::class);

                // Get the current authenticated user who created this admin user
                $invitedBy = auth()->user()?->id ?? 1; // Fallback to user ID 1 if no auth user

                // Create invitation for the newly created user (they need to set their password)
                $invitationService->createInvitationForNewUser($user->email, $invitedBy);

                Log::info("Invitation sent automatically for new admin user: {$user->email}");
            } catch (\Exception $e) {
                Log::error("Failed to send invitation for new admin user {$user->email}: ".$e->getMessage());
            }
        }
    }
}
