<?php

namespace App\Rules;

use App\Models\Address;
use App\Models\AddressLabel;
use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Facades\Auth;

class UniqueShortcut implements ValidationRule
{
    protected $userId;

    protected $excludeAddressId;

    protected $validationType;

    protected $addressData;

    /**
     * Create a new rule instance.
     *
     * @param  int|null  $userId  The user ID to check uniqueness for
     * @param  int|null  $excludeAddressId  Address ID to exclude from uniqueness check (for updates)
     * @param  string  $validationType  Type of validation: 'label', 'address', or 'both'
     * @param  array  $addressData  Additional address data for comprehensive checking
     */
    public function __construct(
        ?int $userId = null,
        ?int $excludeAddressId = null,
        string $validationType = 'both',
        array $addressData = []
    ) {
        $this->userId = $userId ?? Auth::id();
        $this->excludeAddressId = $excludeAddressId;
        $this->validationType = $validationType;
        $this->addressData = $addressData;
    }

    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (! $this->userId) {
            $fail('User authentication required for shortcut validation.');

            return;
        }

        // Check based on validation type
        switch ($this->validationType) {
            case 'label':
                $this->validateLabelUniqueness($value, $fail);
                break;
            case 'address':
                // For address validation, get coordinates from request
                $request = request();
                $addressData = [
                    'address' => $value,
                    'latitude' => $request->input('latitude'),
                    'longitude' => $request->input('longitude'),
                ];
                $this->validateAddressUniqueness($addressData, $fail);
                break;
            case 'both':
                $this->validateLabelUniqueness($value, $fail);
                if (! empty($this->addressData)) {
                    $this->validateAddressUniqueness($this->addressData, $fail);
                }
                break;
        }
    }

    /**
     * Validate that the label is unique among user's shortcuts.
     */
    protected function validateLabelUniqueness($label, Closure $fail): void
    {
        if (empty($label)) {
            return;
        }

        // Check if label already exists in address_label table for user's shortcuts
        $query = AddressLabel::whereHas('address', function ($addressQuery) {
            $addressQuery->where('addressable_type', 'App\Models\User')
                ->where('addressable_id', $this->userId)
                ->where('shortcut', true);
        })->where('label', $label);

        // Exclude current address if updating
        if ($this->excludeAddressId) {
            $query->whereHas('address', function ($addressQuery) {
                $addressQuery->where('id', '!=', $this->excludeAddressId);
            });
        }

        if ($query->exists()) {
            $fail('A shortcut with this label already exists. Please choose a different label.');
        }
    }

    /**
     * Validate that the address is unique among user's shortcuts.
     */
    protected function validateAddressUniqueness($addressData, Closure $fail): void
    {
        if (empty($addressData)) {
            return;
        }

        $address = is_array($addressData) ? $addressData['address'] ?? null : $addressData;
        $latitude = is_array($addressData) ? $addressData['latitude'] ?? null : null;
        $longitude = is_array($addressData) ? $addressData['longitude'] ?? null : null;

        if (empty($address)) {
            return;
        }

        // Build query to check for duplicate addresses
        $query = Address::where('addressable_type', 'App\Models\User')
            ->where('addressable_id', $this->userId)
            ->where('shortcut', true);

        // Exclude current address if updating
        if ($this->excludeAddressId) {
            $query->where('id', '!=', $this->excludeAddressId);
        }

        // Check for exact address match or coordinate match
        $query->where(function ($subQuery) use ($address, $latitude, $longitude) {
            $subQuery->where('address', $address);

            // Also check coordinates if provided (with small tolerance for GPS precision)
            if ($latitude !== null && $longitude !== null) {
                $subQuery->orWhere(function ($coordQuery) use ($latitude, $longitude) {
                    $coordQuery->whereBetween('latitude', [(float) $latitude - 0.0001, (float) $latitude + 0.0001])
                        ->whereBetween('longitude', [(float) $longitude - 0.0001, (float) $longitude + 0.0001]);
                });
            }
        });

        if ($query->exists()) {
            $fail('A shortcut for this address already exists. Please choose a different address.');
        }
    }

    /**
     * Get validation error message.
     */
    public function message(): string
    {
        return 'This shortcut already exists. Please use a unique label and address.';
    }
}
