<?php

namespace App\Rules;

use App\Services\PricingRuleConflictService;
use Illuminate\Contracts\Validation\Rule;

class CombinedPricingRuleConflictRule implements Rule
{
    protected float $newBasePrice;

    protected float $newDistancePrice;

    protected float $currentBasePrice;

    protected float $currentDistancePrice;

    protected ?string $message = null;

    private PricingRuleConflictService $conflictService;

    /**
     * Create a new rule instance.
     *
     * @param  float  $newBasePrice  The new base price
     * @param  float  $newDistancePrice  The new distance price
     * @param  float  $currentBasePrice  The current base price
     * @param  float  $currentDistancePrice  The current distance price
     */
    public function __construct(
        float $newBasePrice,
        float $newDistancePrice,
        float $currentBasePrice,
        float $currentDistancePrice
    ) {
        $this->newBasePrice = $newBasePrice;
        $this->newDistancePrice = $newDistancePrice;
        $this->currentBasePrice = $currentBasePrice;
        $this->currentDistancePrice = $currentDistancePrice;
        $this->conflictService = new PricingRuleConflictService;
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     */
    public function passes($attribute, $value): bool
    {
        $combinedConflicts = $this->conflictService->checkForCombinedConflicts(
            $this->newBasePrice,
            $this->newDistancePrice,
            $this->currentBasePrice,
            $this->currentDistancePrice
        );

        // Check if there are any conflicts
        $hasConflicts = ! empty($combinedConflicts['base_fare_conflicts']) ||
                       ! empty($combinedConflicts['distance_fare_conflicts']);

        if (! $hasConflicts) {
            return true;
        }

        $this->message = $this->buildCombinedErrorMessage($combinedConflicts);

        return false;
    }

    /**
     * Get the validation error message.
     */
    public function message(): string
    {
        return $this->message ?? 'The pricing rules conflict with existing component pricing.';
    }

    /**
     * Build a detailed error message from the combined conflicts.
     */
    private function buildCombinedErrorMessage(array $combinedConflicts): string
    {
        $message = "<div class='space-y-6'>";

        // Handle base fare conflicts
        if (! empty($combinedConflicts['base_fare_conflicts'])) {
            $message .= $this->buildConflictSection('base fare', $combinedConflicts['base_fare_conflicts']);
        }

        // Handle distance fare conflicts
        if (! empty($combinedConflicts['distance_fare_conflicts'])) {
            $message .= $this->buildConflictSection('distance fare', $combinedConflicts['distance_fare_conflicts']);
        }

        // Add a combined call to action
        $message .= "<div class='mt-4 pt-3 border-t border-gray-200 dark:border-gray-700'>";
        $message .= "<p class='flex items-start'>";
        $message .= '<span>Please update the pricing for the components mentioned above before reducing the global pricing.</span>';
        $message .= '</p>';
        $message .= '</div>';

        $message .= '</div>';

        return $message;
    }

    /**
     * Build a conflict section for a specific fare type.
     */
    private function buildConflictSection(string $fareType, array $conflicts): string
    {
        $message = "<div class='mb-4'>";
        $message .= "<h4 class='font-medium text-warning-600 mb-3'>".ucfirst($fareType).' Conflicts:</h4>';
        $message .= "<p class='text-sm text-gray-600 dark:text-gray-400 mb-3'>Lowering the global {$fareType} will result in pricing issues in the following components:</p>";

        $conflictTypes = [
            'areas' => 'Areas',
            'vehicle_types' => 'Vehicle Types',
            'seat_numbers' => 'Seat Configurations',
            'gender_rules' => 'Gender Rules',
            'equipment' => 'Equipment',
            'day_time_configs' => 'Day-Time Configurations',
        ];

        foreach ($conflictTypes as $key => $title) {
            if (! isset($conflicts[$key]) || empty($conflicts[$key])) {
                continue;
            }

            $message .= "<div class='mb-3'>";
            $message .= "<h5 class='font-medium text-gray-800 dark:text-gray-200 mb-2'>{$title}</h5>";
            $message .= "<ul class='list-disc list-inside space-y-1 ml-4'>";

            foreach ($conflicts[$key] as $item) {
                $message .= '<li class="text-sm text-gray-700 dark:text-gray-300">';

                // Get the item name based on the conflict type
                $itemName = match ($key) {
                    'areas' => $item['name'] ?? 'Unknown Area',
                    'vehicle_types' => $item['name'] ?? 'Unknown Vehicle Type',
                    'seat_numbers' => ($item['seats_number'] ?? 'Unknown').' Seats',
                    'gender_rules' => ucfirst($item['gender'] ?? 'Unknown').' Gender Rule',
                    'equipment' => $item['name'] ?? 'Unknown Equipment',
                    'day_time_configs' => $item['name'] ?? 'Unknown Day-Time Config',
                    default => 'Unknown Component',
                };

                // Determine which fare type to show details for
                $fareKey = $fareType === 'base fare' ? 'base_fare' : 'distance_fare';

                // Add conflict details if they exist for the current fare type
                if (isset($item['conflicts'][$fareKey])) {
                    $conflictInfo = $item['conflicts'][$fareKey];
                    $currentValue = number_format((float) $conflictInfo['current_value'], 2);
                    $minAllowed = number_format((float) $conflictInfo['min_allowed'], 2);

                    $message .= "<span class='block'><span class='font-medium'>{$itemName}</span> — Current: <span class='text-danger-600'>{$currentValue} LYD</span>, Minimum allowed: <span class='text-success-600'>{$minAllowed} LYD</span></span>";
                } else {
                    $message .= "<span>{$itemName}</span>";
                }

                $message .= '</li>';
            }

            $message .= '</ul>';
            $message .= '</div>';
        }

        $message .= '</div>';

        return $message;
    }
}
