<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class ValidIcon implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string, ?string=): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $iconExists = \App\Models\AvailableIcon::active()
            ->where('icon_name', $value)
            ->exists();

        if (! $iconExists) {
            $fail('The selected icon is not available. Please choose from the available icons.');
        }
    }
}
