<?php

namespace App\Enums;

enum SeatConfiguration: int
{
    case Two = 2;
    case Four = 4;
    case Six = 6;

    /**
     * Get all seat configuration options for forms
     */
    public static function options(): array
    {
        return collect(self::cases())
            ->mapWithKeys(fn ($case) => [$case->value => "{$case->value} Seats"])
            ->toArray();
    }

    /**
     * Get the label for display
     */
    public function getLabel(): string
    {
        return "{$this->value} Seats";
    }
}
