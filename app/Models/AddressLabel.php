<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AddressLabel extends Model
{
    /** @use HasFactory<\Database\Factories\AddressLabelFactory> */
    use HasFactory;

    protected $fillable = ['label', 'icon', 'address_id', 'available_icon_id'];

    protected $table = 'address_label';

    public function address()
    {
        return $this->belongsTo(Address::class, 'address_id');
    }

    public function availableIcon()
    {
        return $this->belongsTo(AvailableIcon::class, 'available_icon_id');
    }

    /**
     * Get the icon name, preferring the relationship over the direct icon field
     */
    public function getIconNameAttribute()
    {
        return $this->availableIcon?->icon_name ?? $this->icon;
    }

    /**
     * Get the SVG content for this label's icon
     */
    public function getIconSvgAttribute()
    {
        $iconName = $this->getIconNameAttribute();

        return $iconName ? svg($iconName)->toHtml() : null;
    }
}
