<?php

// File: app/Models/TripLocation.php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TripLocation extends Model
{
    use HasFactory;

    protected $fillable = [
        'departure_address',
        'arrival_address',
        'departure_full_address',
        'arrival_full_address',
        'departure_lat',
        'departure_lng',
        'arrival_lat',
        'arrival_lng',
        'trip_id',
        'polyline',
        'new_polyline',
    ];

    public function trip()
    {
        return $this->hasOne(Trip::class, 'trip_location_id');
    }
}
