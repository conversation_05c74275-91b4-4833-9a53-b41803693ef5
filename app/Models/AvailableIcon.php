<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AvailableIcon extends Model
{
    use HasFactory;

    protected $fillable = [
        'icon_name',
        'display_name',
        'category',
        'status',
    ];

    protected $casts = [
        'status' => 'boolean',
    ];

    /**
     * Scope to get only active icons
     */
    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    /**
     * Scope to filter by category
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Get the SVG content for this icon
     */
    public function getSvgAttribute()
    {
        return svg($this->icon_name)->toHtml();
    }

    /**
     * Get available categories
     */
    public static function getCategories()
    {
        return [
            'general' => 'General',
            'location' => 'Location',
            'business' => 'Business',
            'food' => 'Food & Dining',
            'transport' => 'Transportation',
            'health' => 'Health & Medical',
            'entertainment' => 'Entertainment',
            'shopping' => 'Shopping',
        ];
    }

    /**
     * Relationship with address labels
     */
    public function addressLabels()
    {
        return $this->hasMany(AddressLabel::class, 'available_icon_id');
    }
}
