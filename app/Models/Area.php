<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Area extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $casts = [
        'polygon' => 'array',
        'base_fare_adjustment_type' => \App\Enums\Payments\PaymentTypeEnum::class,
        'distance_fare_adjustment_type' => \App\Enums\Payments\PaymentTypeEnum::class,
    ];

    public function departureTrips()
    {
        return $this->hasMany(Trip::class, 'departure_area_id');
    }

    public function arrivalTrips()
    {
        return $this->hasMany(Trip::class, 'arrival_area_id');
    }

    // Mutator for base fare adjustment type
    public function setBaseFareAdjustmentTypeAttribute($value)
    {
        $this->attributes['base_fare_adjustment_type'] = $value;

        if ($value === 'fixed') {
            $this->attributes['base_fare_adjustment'] = 0;
        } elseif ($value === 'percentage') {
            $this->attributes['base_fare'] = 0;
        }
    }

    public function setDistanceFareAdjustmentTypeAttribute($value)
    {
        $this->attributes['distance_fare_adjustment_type'] = $value;

        if ($value === 'fixed') {
            $this->attributes['distance_fare_adjustment'] = 0;
        } elseif ($value === 'percentage') {
            $this->attributes['distance_fare'] = 0;
        }
    }

    public function getBaseFareValueAttribute(): string
    {
        $baseFareAdjustmentType = $this->getRawOriginal('base_fare_adjustment_type');

        return match ($baseFareAdjustmentType) {
            'percentage' => $this->base_fare_adjustment ? "{$this->base_fare_adjustment}%" : '-',
            'fixed' => $this->base_fare ? number_format($this->base_fare, 2).' LYD' : '-',
            default => '-',
        };
    }

    public function getDistanceFareValueAttribute(): string
    {
        $distanceFareAdjustmentType = $this->getRawOriginal('distance_fare_adjustment_type');

        return match ($distanceFareAdjustmentType) {
            'percentage' => $this->distance_fare_adjustment ? "{$this->distance_fare_adjustment}%" : '-',
            'fixed' => $this->distance_fare ? number_format($this->distance_fare, 2).' LYD' : '-',
            default => '-',
        };
    }

    public function setNameEnAttribute($value)
    {
        $this->attributes['name_en'] = ucfirst(strtolower($value));  // Capitalizes the first letter and ensures the rest are lowercase
    }

    public function isUsableForPricing(): bool
    {
        return $this->is_active &&
               $this->base_fare_adjustment_type !== null &&
               $this->distance_fare_adjustment_type !== null;
    }
}
