<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Rider extends Model
{
    use HasFactory, SoftDeletes;

    protected $guarded = [];

    protected $casts = [
        'global_status' => \App\Enums\RiderGlobalStatus::class,
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function trips()
    {
        return $this->hasMany(Trip::class);
    }

    public function tripRatings()
    {
        return $this->hasMany(TripRating::class);
    }

    public function lastTrip()
    {
        return $this->hasOne(Trip::class)->latestOfMany();
    }

    // public static function getFirstAddresses()
    // {
    //     return Address::where('addressable_type', User::class)
    //         ->whereHas('addressable', function ($query) {
    //             $query->whereHas('rider'); // Ensure the user has a rider
    //         })
    //         ->selectRaw('MIN(created_at) as first_created, address') // Get first created
    //         ->groupBy('address')
    //         ->orderBy('first_created', 'asc')
    //         ->pluck('address', 'address') // Returns ['Sousse' => 'Sousse']
    //         ->filter(); // Remove null values
    // }
    public static function getFirstAddresses()
    {
        return Address::where('addressable_type', User::class)
            ->whereHas('addressable', function ($query) {
                $query->whereHas('rider', function ($q) {
                    $q->withTrashed(); // ✅ Include soft-deleted riders
                });
            })
            ->selectRaw('MIN(created_at) as first_created, address') // ✅ Get first created address
            ->groupBy('address')
            ->orderBy('first_created', 'asc')
            ->pluck('address', 'address') // ✅ Returns ['Sousse' => 'Sousse']
            ->filter(); // ✅ Remove null values
    }

    /**
     * Check if rider has enough ratings to display average rating
     * Only considers the last 200 ratings as per business rules
     */
    public function hasEnoughRatingsForAverage(): bool
    {
        $ratingsCount = TripRating::whereHas('trip', function ($query) {
            $query->where('status', 'completed');
        })
            ->where('rider_id', $this->id)
            ->whereNotNull('driver_to_rider_rating') // Only count actual ratings, not null
            ->orderBy('created_at', 'desc')
            ->limit(200)
            ->count();

        return $ratingsCount >= 5;
    }

    /**
     * Get the rider's average rating only if they have enough ratings
     */
    public function getDisplayableAverageRating(): ?float
    {
        if (! $this->hasEnoughRatingsForAverage()) {
            return null;
        }

        return $this->average_rider_rating;
    }
}
