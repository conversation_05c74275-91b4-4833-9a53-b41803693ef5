<?php

namespace App\Models;

use App\Enums\Vehicles\VehicleStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Driver extends Model
{
    use HasFactory, SoftDeletes;

    protected $guarded = [];

    protected $casts = [
        'rejection_reason_columns' => 'array',
        'global_status' => \App\Enums\Drivers\DriverGlobalStatus::class,
        'last_heartbeat' => 'datetime',
        // 'driver_license' => 'array',
        // 'id_card' => 'array',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function trips()
    {
        return $this->hasMany(Trip::class);
    }

    public function driverAvailabilities()
    {
        return $this->hasMany(DriverAvailability::class);
    }

    // change relationship
    public function vehicles()
    {
        return $this->belongsToMany(Vehicle::class)->withTrashed();
    }

    public function tripRatings()
    {
        return $this->hasManyThrough(TripRating::class, Trip::class, 'driver_id', 'trip_id');
    }

    public function documents()
    {
        return $this->hasOne(DriverDocuments::class, 'driver_id');
    }

    public function vehicleEquipments()
    {
        return $this->hasManyThrough(VehicleEquipment::class, Vehicle::class, 'driver_id', 'vehicle_id', 'id', 'id');
    }

    // public static function getFirstAddresses()
    // {
    //     return Address::whereHas('addressable', function ($query) {
    //         $query->whereHas('driver'); // Ensure only users that have drivers
    //     })
    //         ->whereNotNull('address') // Prevent null addresses
    //         ->selectRaw('MIN(id) as min_id, address') // Select first-created address per unique address
    //         ->groupBy('address') // Group by address to ensure distinct values
    //         ->orderBy('min_id', 'asc') // Order by first-created address
    //         ->pluck('address', 'address') // Use address as key and value
    //         ->filter() // Remove any null values just in case
    //         ->toArray();
    // }
    public static function getFirstAddresses()
    {
        return Address::whereHas('addressable', function ($query) {
            $query->whereHas('driver', function ($q) {
                $q->withTrashed(); // ✅ Include soft-deleted drivers
            });
        })
            ->whereNotNull('address') // ✅ Prevent null addresses
            ->selectRaw('MIN(id) as min_id, address') // ✅ Select first-created address per unique address
            ->groupBy('address') // ✅ Ensure distinct addresses
            ->orderBy('min_id', 'asc') // ✅ Order by first-created address
            ->pluck('address', 'address') // ✅ Returns ['Sousse' => 'Sousse']
            ->filter() // ✅ Remove null values
            ->toArray();
    }

    public function getActiveVehicles()
    {
        return $this->vehicles()->where('status', VehicleStatus::active->value)->first();
    }

    /**
     * Check if driver has enough ratings to display average rating
     * Only considers the last 200 ratings as per business rules
     */
    public function hasEnoughRatingsForAverage(): bool
    {
        $ratingsCount = TripRating::whereHas('trip', function ($query) {
            $query->where('status', 'completed');
        })
            ->where('driver_id', $this->id)
            ->whereNotNull('rider_to_driver_rating') // Only count actual ratings, not null
            ->orderBy('created_at', 'desc')
            ->limit(200)
            ->count();

        return $ratingsCount >= 5;
    }

    /**
     * Get the driver's average rating only if they have enough ratings
     */
    public function getDisplayableAverageRating(): ?float
    {
        if (! $this->hasEnoughRatingsForAverage()) {
            return null;
        }

        return $this->average_driver_rating;
    }
}
