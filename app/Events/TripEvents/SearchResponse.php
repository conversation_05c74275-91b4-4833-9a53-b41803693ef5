<?php

namespace App\Events\TripEvents;

use App\Models\Trip;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class SearchResponse implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $trip;

    public $response;

    public $lng;

    public $lat;

    public function __construct(Trip $trip, string $response, $lng = null, $lat = null)
    {
        $this->trip = $trip;
        $this->response = $response;
        $this->lng = $lng;
        $this->lat = $lat;
    }

    public function broadcastOn()
    {
        return new PrivateChannel('trip.'.$this->trip->id);
    }

    public function broadcastWith()
    {
        $data = [
            'response' => $this->response,
        ];

        if ($this->response === 'assigned') {
            $data = array_merge($data, [
                'trip' => [
                    'trip_id' => $this->trip->id,
                    'pricing' => json_decode($this->trip->pricing_breakdown, true),
                    'estimated_departure_time' => $this->trip->estimated_departure_time,
                    'estimated_arrival_time' => $this->trip->estimated_arrival_time,
                ],
                'driver' => [
                    'id' => $this->trip->driver->id,
                    'full_name' => $this->trip->driver->user->full_name,
                    'name' => $this->trip->driver->user->name,
                    'last_name' => $this->trip->driver->user->last_name,
                    'phone_number' => $this->trip->driver->user->phone_number,
                    'image' => $this->trip->driver->user->cover_picture
                        ? env('APP_URL', '/').'/storage/'.$this->trip->driver->user->cover_picture
                        : env('APP_URL', '/').'/images/avatar.png',
                    'gender' => $this->trip->driver->user->gender,
                    'driver_rating' => $this->trip->tripRatings()->exists() ? $this->trip->tripRatings->first()->rider_to_driver_rating : null,
                    'driver_average_rating' => $this->trip->driver->getDisplayableAverageRating(),
                ],

                'rider' => [
                    'id' => $this->trip->rider->id,
                    'name' => $this->trip->rider->user->name,
                    'last_name' => $this->trip->rider->user->last_name,
                    'full_name' => $this->trip->rider->user->full_name,
                    'gender' => $this->trip->rider->user->gender,
                    'image' => $this->trip->rider->user->cover_picture
                        ? env('APP_URL', '/').'/storage/'.$this->trip->rider->user->cover_picture
                        : env('APP_URL', '/').'/images/avatar.png',
                    'average_rider_rating' => $this->trip->rider->getDisplayableAverageRating(),
                    'phone_number' => $this->trip->rider->user->phone_number,
                ],

                'vehicle' => [
                    'id' => $this->trip->vehicle->id,
                    'image' => $this->trip->vehicle->image
                        ? env('APP_URL', '/').'/storage/'.$this->trip->vehicle->image
                        : env('APP_URL', '/').'/images/vehicle.jpg',
                    'license_plate_number' => $this->trip->vehicle->license_plate_number ?? null,
                    'vehicle_type_ar' => $this->trip->vehicle->vehicleType->name_ar ?? null,
                    'vehicle_type_en' => $this->trip->vehicle->vehicleType->name_en ?? null,
                    'vehicle_model_ar' => $this->trip->vehicle->vehicleModel->name_ar ?? null,
                    'vehicle_model_en' => $this->trip->vehicle->vehicleModel->name_en ?? null,
                    'vehicle_brand_ar' => $this->trip->vehicle->vehicleModel->vehicleBrand->name_ar ?? null,
                    'vehicle_brand_en' => $this->trip->vehicle->vehicleModel->vehicleBrand->name_en ?? null,
                    'vehicle_color' => $this->trip->vehicle->color ?? null,
                    'vehicle_rating' => $this->trip->tripRatings()->exists() ? $this->trip->tripRatings->first()->rider_to_car_rating : null,
                    'vehicle_average_rating' => $this->trip->vehicle->getDisplayableAverageRating(),
                ],
                'status' => $this->trip->status,
            ]);

            if ($this->lng !== null && $this->lat !== null) {
                $data['driver']['lng'] = $this->lng;
                $data['driver']['lat'] = $this->lat;
            }
        }

        return $data;
    }

    public function broadcastAs()
    {
        return 'search-response';
    }
}
