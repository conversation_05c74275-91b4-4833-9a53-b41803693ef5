<?php

namespace App\Services;

use App\Enums\VehicleTypesCategories;
use App\Models\Vehicle;
use App\Models\VehicleBrand;
use App\Models\VehicleClassificationQualification;
use App\Models\VehicleClassificationRule;
use App\Models\VehicleModel;
use App\Models\VehicleType;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class VehicleClassificationService
{
    /**
     * Cache for vehicle categories to avoid repeated calculations
     */
    private array $categoryCache = [];

    /**
     * Cache for classification rules to avoid repeated database queries
     */
    private array $rulesCache = [];

    /**
     * Get cached classification rules for a category
     */
    private function getCachedRules(string $category): Collection
    {
        return Cache::remember("vehicle_classification_rules_{$category}", now()->addHours(1), function () use ($category) {
            return VehicleClassificationRule::where('category', $category)
                ->with('qualifications')
                ->get();
        });
    }

    /**
     * Classify a vehicle based on classification rules
     */
    public function classifyVehicle(Vehicle $vehicle): ?int
    {
        try {
            if (! $this->validateVehicleForClassification($vehicle)) {
                return null;
            }

            $category = $this->determineVehicleCategory($vehicle);
            if (! $category) {
                return null;
            }

            return $this->findMatchingRule($vehicle, $category);

        } catch (\Exception $e) {
            Log::error('Vehicle classification error', [
                'vehicle_id' => $vehicle->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return null;
        }
    }

    /**
     * Get suggested vehicle type for driver approval (shows classification result first)
     */
    public function getSuggestedVehicleTypes(Vehicle $vehicle): array
    {
        $this->ensureVehicleRelationsLoaded($vehicle);

        $category = $this->determineVehicleCategory($vehicle);
        if (! $category) {
            return $this->getFormattedVehicleTypeOptions();
        }

        $suggestedTypeId = $this->classifyVehicle($vehicle);
        $availableTypes = $this->getAvailableVehicleTypes($vehicle, $category);

        return $this->formatOptionsWithSuggestion($availableTypes, $suggestedTypeId);
    }

    /**
     * Validate vehicle has required data for classification
     */
    private function validateVehicleForClassification(Vehicle $vehicle): bool
    {
        if (! $vehicle->vehicleModel?->vehicleBrand) {
            Log::warning('Vehicle classification failed: Missing vehicle model or brand', [
                'vehicle_id' => $vehicle->id,
                'vehicle_model_id' => $vehicle->vehicle_model_id,
                'has_vehicle_model' => $vehicle->vehicleModel !== null,
                'has_brand' => $vehicle->vehicleModel?->vehicleBrand !== null,
            ]);

            return false;
        }

        return true;
    }

    /**
     * Find matching classification rule for vehicle
     */
    private function findMatchingRule(Vehicle $vehicle, VehicleTypesCategories $category): ?int
    {
        $rules = $this->getCachedRules($category->value);

        foreach ($rules as $rule) {
            if ($this->vehicleMatchesRule($vehicle, $rule, $category)) {
                Log::info('Vehicle classified successfully', [
                    'vehicle_id' => $vehicle->id,
                    'rule_id' => $rule->id,
                    'vehicle_type_id' => $rule->vehicle_type_id,
                ]);

                return $rule->vehicle_type_id;
            }
        }

        Log::info('Vehicle classification: No matching rules found', [
            'vehicle_id' => $vehicle->id,
            'category' => $category->value,
            'rules_count' => $rules->count(),
        ]);

        return null;
    }

    /**
     * Ensure vehicle has necessary relationships loaded
     */
    private function ensureVehicleRelationsLoaded(Vehicle $vehicle): void
    {
        if (! $vehicle->relationLoaded('vehicleModel.vehicleBrand') || ! $vehicle->relationLoaded('vehicleType')) {
            $vehicle->load(['vehicleModel.vehicleBrand', 'vehicleType']);
        }
    }

    /**
     * Get available vehicle types for the given category and vehicle
     */
    private function getAvailableVehicleTypes(Vehicle $vehicle, VehicleTypesCategories $category): Collection
    {
        $query = VehicleType::where('category', $category)
            ->where('status', true)
            ->where('name_en', 'NOT LIKE', 'Default_%');

        // For freight vehicles, filter by vehicle characteristics
        if ($category === VehicleTypesCategories::Freight && $vehicle->vehicleType) {
            $query->where('is_covered', $vehicle->vehicleType->is_covered ?? false)
                ->where('weight_category', $vehicle->vehicleType->weight_category ?? 'less_than_1000kg');
        }

        return $query->get();
    }

    /**
     * Format vehicle type options with suggestion highlighted
     */
    private function formatOptionsWithSuggestion(Collection $vehicleTypes, ?int $suggestedTypeId): array
    {
        $options = [];

        // Add suggested type first if found
        if ($suggestedTypeId) {
            $suggestedType = $vehicleTypes->firstWhere('id', $suggestedTypeId);
            if ($suggestedType) {
                $options[$suggestedType->id] = '⭐ '.($suggestedType->name_ar ?? $suggestedType->name_en).' (Suggested)';
                $vehicleTypes = $vehicleTypes->reject(fn ($type) => $type->id === $suggestedTypeId);
            }
        }

        // Add remaining types
        foreach ($vehicleTypes as $vehicleType) {
            $options[$vehicleType->id] = $vehicleType->name_ar ?? $vehicleType->name_en ?? "Vehicle Type #{$vehicleType->id}";
        }

        return $options;
    }

    /**
     * Auto-classify and update vehicle type
     */
    public function autoClassifyVehicle(Vehicle $vehicle): bool
    {
        $classifiedTypeId = $this->classifyVehicle($vehicle);

        if ($classifiedTypeId && $classifiedTypeId !== $vehicle->vehicle_type_id) {
            $vehicle->update([
                'vehicle_type_id' => $classifiedTypeId,
            ]);

            Log::info('Vehicle auto-classified', [
                'vehicle_id' => $vehicle->id,
                'old_type_id' => $vehicle->getOriginal('vehicle_type_id'),
                'new_type_id' => $classifiedTypeId,
            ]);

            return true;
        }

        return false;
    }

    /**
     * Check if vehicle matches a specific rule
     */
    private function vehicleMatchesRule(Vehicle $vehicle, VehicleClassificationRule $rule, VehicleTypesCategories $category): bool
    {
        foreach ($rule->qualifications as $qualification) {
            if ($this->vehicleMatchesQualification($vehicle, $qualification, $category)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if vehicle matches a specific qualification
     */
    private function vehicleMatchesQualification(Vehicle $vehicle, VehicleClassificationQualification $qualification, VehicleTypesCategories $category): bool
    {
        Log::debug('Checking vehicle qualification match', [
            'vehicle_id' => $vehicle->id,
            'qualification_id' => $qualification->id,
            'category' => $category->value,
        ]);

        // Null safety checks first
        if (! $vehicle->vehicleModel?->vehicleBrand) {
            Log::debug('Vehicle qualification failed: missing model or brand');

            return false;
        }

        // Consistent array casting with null coalescing
        $brands = (array) ($qualification->brands ?? []);
        $models = (array) ($qualification->models ?? []);
        $seatNumbers = (array) ($qualification->seat_numbers ?? []);

        // Safe property access with null coalescing
        $vehicleBrandId = $vehicle->vehicleModel->vehicleBrand->id;
        $vehicleModelId = $vehicle->vehicleModel->id;

        // Check brand (empty array means "any brand")
        if (! empty($brands) && ! in_array($vehicleBrandId, $brands)) {
            return false;
        }

        // Check model (empty array means "any model")
        if (! empty($models) && ! in_array($vehicleModelId, $models)) {
            return false;
        }

        // Check year range
        if ($qualification->min_year && $vehicle->year < $qualification->min_year) {
            return false;
        }

        if ($qualification->max_year && $vehicle->year > $qualification->max_year) {
            return false;
        }

        // Category-specific checks
        if ($category === VehicleTypesCategories::Passenger) {
            if (! empty($seatNumbers) && ! in_array($vehicle->seat_number, $seatNumbers)) {
                return false;
            }
        } elseif ($category === VehicleTypesCategories::Freight) {
            // For freight vehicles, check against the vehicle type properties
            if ($qualification->is_covered !== null && $qualification->is_covered !== ($vehicle->vehicleType->is_covered ?? false)) {
                Log::debug('Freight vehicle classification failed: is_covered mismatch', [
                    'qualification_is_covered' => $qualification->is_covered,
                    'vehicle_is_covered' => $vehicle->vehicleType->is_covered ?? false,
                ]);

                return false;
            }

            if ($qualification->weight_category) {
                // Handle both enum objects and string values
                $qualificationWeight = is_object($qualification->weight_category)
                    ? $qualification->weight_category->value
                    : $qualification->weight_category;

                $vehicleWeight = $vehicle->vehicleType->weight_category?->value ?? '';

                if ($qualificationWeight !== $vehicleWeight) {
                    Log::debug('Freight vehicle classification failed: weight_category mismatch', [
                        'qualification_weight' => $qualificationWeight,
                        'vehicle_weight' => $vehicleWeight,
                    ]);

                    return false;
                }
            }
        }

        return true;
    }

    /**
     * Determine vehicle category from vehicle data with caching
     */
    private function determineVehicleCategory(Vehicle $vehicle): ?VehicleTypesCategories
    {
        $cacheKey = "vehicle_{$vehicle->id}_category";

        if (isset($this->categoryCache[$cacheKey])) {
            return $this->categoryCache[$cacheKey];
        }

        $category = null;

        // First try to get from current vehicle type
        if ($vehicle->vehicleType?->category) {
            $category = $vehicle->vehicleType->category;
        } elseif ($vehicle->seat_number) {
            // Try to determine from vehicle characteristics
            $category = VehicleTypesCategories::Passenger;
        }

        // For freight vehicles, we can't determine from vehicle properties alone
        // since is_covered and weight_category are stored in vehicle_type
        // We'll need to rely on the current vehicle type or return null

        return $this->categoryCache[$cacheKey] = $category;
    }

    /**
     * Get formatted vehicle type options for forms (shared method for consistency)
     */
    public function getFormattedVehicleTypeOptions(): array
    {
        $vehicleTypes = VehicleType::where('status', true)
            ->where('name_en', 'NOT LIKE', 'Default_%')
            ->get();

        $options = [];
        foreach ($vehicleTypes as $vehicleType) {
            $options[$vehicleType->id] = $vehicleType->name_ar ?? $vehicleType->name_en ?? "Vehicle Type #{$vehicleType->id}";
        }

        return $options;
    }

    /**
     * Validate brand-model combinations to prevent conflicts across vehicle types
     */
    public function validateBrandModelCombinations(array $qualifications, ?int $excludeRuleId = null): array
    {
        $conflicts = [];

        foreach ($qualifications as $qualificationIndex => $qualification) {
            $brands = (array) ($qualification['brands'] ?? []);
            $models = (array) ($qualification['models'] ?? []);

            // Skip if no brand or models are selected
            if (empty($brands) || empty($models)) {
                continue;
            }

            // For each brand-model combination in this qualification
            foreach ($brands as $brandId) {
                foreach ($models as $modelId) {
                    $existingRules = $this->findExistingBrandModelRules($brandId, $modelId, $excludeRuleId);

                    if (! empty($existingRules)) {
                        $conflicts[] = [
                            'qualification_index' => $qualificationIndex,
                            'brand_id' => $brandId,
                            'model_id' => $modelId,
                            'existing_rules' => $existingRules,
                        ];
                    }
                }
            }
        }

        return $conflicts;
    }

    /**
     * Find existing rules that use the same brand-model combination
     */
    private function findExistingBrandModelRules(int $brandId, int $modelId, ?int $excludeRuleId = null): array
    {
        $query = VehicleClassificationRule::with(['vehicleType', 'qualifications'])
            ->whereHas('qualifications', function ($q) use ($brandId, $modelId) {
                // Use database-agnostic JSON queries
                if (config('database.default') === 'mysql') {
                    $q->whereRaw('JSON_CONTAINS(brands, ?)', [json_encode($brandId)])
                        ->whereRaw('JSON_CONTAINS(models, ?)', [json_encode($modelId)]);
                } else {
                    // For SQLite and PostgreSQL, use LIKE with JSON format (numbers don't have quotes)
                    $q->where('brands', 'LIKE', '%'.$brandId.'%')
                        ->where('models', 'LIKE', '%'.$modelId.'%');
                }
            });

        if ($excludeRuleId) {
            $query->where('id', '!=', $excludeRuleId);
        }

        return $query->get()->map(function ($rule) use ($brandId, $modelId) {
            return [
                'rule_id' => $rule->id,
                'vehicle_type_name' => $rule->vehicleType->name_en,
                'category' => $rule->category->getLabel(),
                'brand_name' => VehicleBrand::find($brandId)?->name_en,
                'model_name' => VehicleModel::find($modelId)?->name_en,
            ];
        })->toArray();
    }

    /**
     * Format validation conflicts for display
     */
    public function formatValidationConflicts(array $conflicts): array
    {
        $formatted = [];

        foreach ($conflicts as $conflict) {
            $brandName = VehicleBrand::find($conflict['brand_id'])?->name_en ?? 'Unknown Brand';
            $modelName = VehicleModel::find($conflict['model_id'])?->name_en ?? 'Unknown Model';

            foreach ($conflict['existing_rules'] as $existingRule) {
                $formatted[] = [
                    'message' => "Brand-Model combination '{$brandName} {$modelName}' is already used in vehicle type '{$existingRule['vehicle_type_name']}' ({$existingRule['category']})",
                    'qualification_index' => $conflict['qualification_index'],
                    'brand_model' => "{$brandName} {$modelName}",
                    'existing_vehicle_type' => $existingRule['vehicle_type_name'],
                    'existing_category' => $existingRule['category'],
                ];
            }
        }

        return $formatted;
    }

    /**
     * Validate brand-model combinations within the same rule (within qualifications)
     */
    public function validateWithinRuleBrandModelCombinations(array $qualifications): array
    {
        $conflicts = [];
        $seenCombinations = [];

        foreach ($qualifications as $qualificationIndex => $qualification) {
            $brands = (array) ($qualification['brands'] ?? []);
            $models = (array) ($qualification['models'] ?? []);

            // Skip if no brand or models are selected
            if (empty($brands) || empty($models)) {
                continue;
            }

            // For each brand-model combination in this qualification
            foreach ($brands as $brandId) {
                foreach ($models as $modelId) {
                    $combination = "{$brandId}-{$modelId}";

                    if (isset($seenCombinations[$combination])) {
                        // Found a duplicate
                        $conflicts[] = [
                            'qualification_index' => $qualificationIndex,
                            'brand_id' => $brandId,
                            'model_id' => $modelId,
                            'first_seen_in_qualification' => $seenCombinations[$combination],
                        ];
                    } else {
                        $seenCombinations[$combination] = $qualificationIndex;
                    }
                }
            }
        }

        return $conflicts;
    }

    /**
     * Format within-rule validation conflicts for display
     */
    public function formatWithinRuleValidationConflicts(array $conflicts): array
    {
        $formatted = [];

        foreach ($conflicts as $conflict) {
            $brandName = VehicleBrand::find($conflict['brand_id'])?->name_en ?? 'Unknown Brand';
            $modelName = VehicleModel::find($conflict['model_id'])?->name_en ?? 'Unknown Model';

            $firstQualificationIndex = (int) $conflict['first_seen_in_qualification'];
            $currentQualificationIndex = (int) $conflict['qualification_index'];

            $formatted[] = [
                'message' => "Brand-Model combination '{$brandName} {$modelName}' is already selected in qualification ".($firstQualificationIndex + 1),
                'qualification_index' => $currentQualificationIndex,
                'brand_model' => "{$brandName} {$modelName}",
                'first_qualification' => $firstQualificationIndex + 1,
                'current_qualification' => $currentQualificationIndex + 1,
            ];
        }

        return $formatted;
    }
}
