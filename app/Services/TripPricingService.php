<?php

namespace App\Services;

use App\Models\Trip;
use App\Models\VehicleType;
use Carbon\Carbon;

class TripPricingService
{
    /**
     * Calculate and set pricing for a trip
     *
     * @param  Trip  $trip  The trip to update
     * @param  array  $googleData  Google Maps data
     * @param  array  $data  Trip data
     * @param  int|null  $departureAreaId  Departure area ID
     */
    public function calculateAndSetTripPricing(
        Trip $trip,
        array $googleData,
        array $data,
        ?int $departureAreaId
    ): void {
        // Get vehicle types
        $vehicleTypeIds = $this->getValidVehicleTypeIds($data);

        if (empty($vehicleTypeIds)) {
            return;
        }

        // Set the estimated departure time
        $departureTime = isset($data['estimated_departure_time'])
            ? Carbon::parse($data['estimated_departure_time'])
            : now();
        // Check if the rider has requested a female driver
        // This is determined by checking the rider's preferences for driver_gender
        $requestedFemaleDriver = false;

        // Use eager loading to avoid N+1 queries
        $trip->load('rider.user.preferences');
        if ($trip->rider && $trip->rider->user && $trip->rider->user->preferences) {
            $requestedFemaleDriver = $trip->rider->user->preferences->driver_gender === 'female';
        }

        // Apply female pricing only if the rider has requested a female driver
        $gender = $requestedFemaleDriver ? 'female' : 'male';

        // Ensure we have a valid distance value
        if (! isset($googleData['distance_value']) && $trip->distance > 0) {
            // If Google Maps data doesn't have distance_value but trip has distance, use that
            $googleData['distance_value'] = $trip->distance * 1000; // Convert km to meters
        }

        // Calculate pricing for all vehicle types
        $pricingData = PricingService::calculatePrices(
            $googleData,
            $vehicleTypeIds,
            $gender,
            $data['vehicle_equipments'] ?? [],
            $departureTime,
            $departureAreaId,
            $trip->estimated_arrival_time,
            $trip->actual_arrival_time
        );

        // Store the pricing breakdown
        if (isset($pricingData[$trip->vehicle_type_id])) {
            $trip->storePricingBreakdown($pricingData[$trip->vehicle_type_id]);
        }
    }

    /**
     * Calculate trip pricing
     *
     * @param  float  $distance  Distance in kilometers
     * @param  int|null  $departureAreaId  Departure area ID
     * @param  int|null  $vehicleTypeId  Vehicle type ID
     * @param  bool  $isFemale  Whether the rider is female
     * @param  Carbon|null  $departureTime  Departure time
     * @param  array  $equipmentIds  Equipment IDs
     * @param  Carbon|null  $estimatedArrivalTime  Estimated arrival time
     * @param  Carbon|null  $actualArrivalTime  Actual arrival time
     * @return array Pricing breakdown
     */
    public function calculateTripPricing(
        float $distance,
        ?int $departureAreaId,
        ?int $vehicleTypeId,
        bool $isFemale,
        ?Carbon $departureTime,
        array $equipmentIds = [],
        ?Carbon $estimatedArrivalTime = null,
        ?Carbon $actualArrivalTime = null
    ): array {
        // The $isFemale parameter should indicate if the rider has requested a female driver
        // We pass 'female' or 'male' to ensure consistent pricing
        $gender = $isFemale ? 'female' : 'male';

        return PricingService::calculatePrice(
            $distance,
            $departureAreaId,
            $vehicleTypeId,
            $gender,
            $departureTime,
            $equipmentIds,
            $estimatedArrivalTime,
            $actualArrivalTime
        );
    }

    /**
     * Get valid vehicle type IDs based on trip data
     *
     * @param  array  $data  Trip data
     * @return array Array of vehicle type IDs
     */
    public function getValidVehicleTypeIds(array $data): array
    {
        // Filter valid vehicle types
        $query = VehicleType::where('status', true)
            ->where('category', $data['vehicle_category']);

        if ($data['vehicle_category'] === 'freight' && isset($data['is_covered']) && isset($data['weight_category'])) {
            $query->where([
                'is_covered' => $data['is_covered'],
                'weight_category' => $data['weight_category'],
            ]);
        }

        return $query->pluck('id')->toArray();
    }

    /**
     * Get processed vehicle types with pricing
     *
     * @param  array  $vehicleTypeIds  Array of vehicle type IDs
     * @param  array  $pricingData  Pricing data
     * @return array Processed vehicle types
     */
    public function getProcessedVehicleTypes(array $vehicleTypeIds, array $pricingData): array
    {
        $vehicleTypes = VehicleType::whereIn('id', $vehicleTypeIds)->get();
        $processedVehicleTypes = [];

        foreach ($vehicleTypes as $type) {
            $processedType = [
                'id' => $type->id,
                'created_at' => $type->created_at,
                'updated_at' => $type->updated_at,
                'name_en' => $type->name_en,
                'name_ar' => $type->name_ar,
                'image' => ! empty($type->image)
                    ? rtrim(env('APP_URL', '/'), '/').'/storage/'.ltrim($type->image, '/')
                    : null,
                'description_ar' => $type->description_ar,
                'description_en' => $type->description_en,
                'status' => $type->status,
                'category' => $type->category,
                'is_covered' => $type->is_covered,
                'weight_category' => $type->weight_category,
            ];

            // Add pricing if available
            if (isset($pricingData[$type->id])) {
                $processedType['pricing'] = [
                    'price' => $pricingData[$type->id]['total'] ?? 0,
                    'distance' => $pricingData[$type->id]['distance'] ?? 0,
                    'currency' => 'LYD',
                ];
            }

            $processedVehicleTypes[] = $processedType;
        }

        // Sort by price from smaller to bigger
        usort($processedVehicleTypes, function ($a, $b) {
            return ($a['pricing']['price'] ?? 0) <=> ($b['pricing']['price'] ?? 0);
        });

        return $processedVehicleTypes;
    }
}
