<?php

namespace App\Services;

use App\Enums\UserStatus;
use App\Models\Vehicle;
use Illuminate\Support\Facades\DB;

class VehicleTrackingService
{
    private const CACHE_TTL = 30; // 30 seconds cache for static data

    private const POSITION_CACHE_TTL = 5; // 5 seconds cache for position data

    /**
     * Get optimized vehicle data for tracking using Eloquent with performance optimizations
     */
    public static function getOptimizedVehicleData(): array
    {
        // Use raw query for better performance with large datasets
        $vehicles = Vehicle::select([
            'vehicles.id',
            'vehicles.global_status',
            'vehicles.status',
            'vehicles.average_vehicle_rating',
            'vehicles.image',
            'vehicles.vehicle_model_id',
            'vehicles.vehicle_type_id',
        ])
            ->with([
                'drivers' => function ($query) {
                    $baseSelect = [
                        'drivers.id',
                        'drivers.global_status',
                        'drivers.average_driver_rating',
                        'drivers.user_id',
                        'drivers.location',
                    ];

                    // Add database-specific location extraction
                    if (DB::connection()->getDriverName() === 'pgsql') {
                        $baseSelect[] = DB::raw('ST_X(location::geometry) as driver_lng');
                        $baseSelect[] = DB::raw('ST_Y(location::geometry) as driver_lat');
                    }

                    $query->select($baseSelect)
                        ->whereNotNull('location')
                        ->with([
                            'user:id,name,last_name,full_name,phone_number,gender,status,cover_picture',
                            'trips' => function ($tripQuery) {
                                $tripQuery->select([
                                    'trips.id',
                                    'trips.driver_id',
                                    'trips.rider_id',
                                    'trips.status',
                                    'trips.distance',
                                    'trips.estimated_departure_time',
                                    'trips.estimated_arrival_time',
                                    'trips.actual_departure_time',
                                    'trips.rider_notes',
                                    'trips.pricing_breakdown',
                                    'trips.trip_location_id', // Add this to properly load the relationship
                                ])
                                    ->whereIn('status', [
                                        'assigned',
                                        'driver_arriving',
                                        'driver_arrived',
                                        'on_trip',
                                        'waiting_for_driver_confirmation',
                                    ])
                                    ->with([
                                        'rider:id,user_id,average_rider_rating',
                                        'rider.user:id,name,last_name,full_name,phone_number,gender,cover_picture',
                                        'tripLocation:id,trip_id,departure_address,arrival_address,departure_lat,departure_lng,arrival_lat,arrival_lng',
                                    ])
                                    ->limit(1); // Only get the most recent active trip
                            },
                        ]);
                },
                'vehicleModel:id,name_en,vehicle_brand_id',
                'vehicleModel.vehicleBrand:id,name_en',
                'vehicleType:id,name_en',
                'vehicleEquipments:id,name_en',
            ])
            ->whereHas('drivers', function ($query) {
                $query->whereNotNull('location')
                    ->whereIn('global_status', ['active', 'blocked']);
            })
            ->whereIn('global_status', ['active', 'blocked'])
            ->where('status', 'online')
            ->get();

        // Process the results with optimized filtering
        return $vehicles
            ->filter(function ($vehicle) {
                $driver = $vehicle->drivers->first();

                if (! $driver || ! $driver->location) {
                    return false;
                }

                // For PostgreSQL, check pre-extracted coordinates
                if (isset($driver->driver_lat) && isset($driver->driver_lng)) {
                    return $driver->driver_lat != 0 && $driver->driver_lng != 0;
                }

                // For other databases, extract coordinates from location field
                $coordinates = self::extractLocationCoordinates($driver->location);

                return $coordinates && $coordinates['lat'] != 0 && $coordinates['lng'] != 0;
            })
            ->map(function ($vehicle) {
                return self::prepareOptimizedVehicleData($vehicle);
            })
            ->values()
            ->toArray();
    }

    /**
     * Get optimized data for a single vehicle using Eloquent
     */
    public static function getOptimizedSingleVehicleData(Vehicle $vehicle): array
    {
        // Load the vehicle with all necessary relationships
        $vehicle->load([
            'drivers' => function ($query) {
                $baseSelect = ['drivers.*'];

                // Add database-specific location extraction
                if (DB::connection()->getDriverName() === 'pgsql') {
                    $baseSelect[] = DB::raw('ST_X(location::geometry) as driver_lng');
                    $baseSelect[] = DB::raw('ST_Y(location::geometry) as driver_lat');
                }

                $query->whereNotNull('location')
                    ->with('user')
                    ->addSelect($baseSelect);
            },

            'vehicleModel.vehicleBrand',
            'vehicleType',
            'vehicleEquipments',
            'drivers.trips' => function ($query) {
                $query->select([
                    'trips.*', // Select all trip fields including trip_location_id
                ])
                    ->whereIn('status', [
                        'assigned',
                        'driver_arriving',
                        'driver_arrived',
                        'on_trip',
                        'waiting_for_driver_confirmation',
                    ])->with([
                        'rider.user',
                        'tripLocation:id,trip_id,departure_address,arrival_address,departure_lat,departure_lng,arrival_lat,arrival_lng',
                    ]);
            },
        ]);

        $driver = $vehicle->drivers->first();
        if (! $driver || ! $driver->location || ! isset($driver->driver_lat) || ! isset($driver->driver_lng)) {
            return [];
        }

        return self::prepareEloquentVehicleData($vehicle);
    }

    /**
     * Prepare optimized vehicle data with minimal processing
     */
    private static function prepareOptimizedVehicleData(Vehicle $vehicle): array
    {
        $driver = $vehicle->drivers->first();
        $currentTrip = $driver?->trips->first();

        // Extract coordinates based on database type
        $coordinates = ['lat' => 0, 'lng' => 0];
        if ($driver) {
            if (isset($driver->driver_lat) && isset($driver->driver_lng)) {
                // Use pre-extracted coordinates (PostgreSQL)
                $coordinates = [
                    'lat' => (float) $driver->driver_lat,
                    'lng' => (float) $driver->driver_lng,
                ];
            } elseif ($driver->location) {
                // Extract coordinates from location field (SQLite/other databases)
                $extracted = self::extractLocationCoordinates($driver->location);
                if ($extracted) {
                    $coordinates = $extracted;
                }
            }
        }

        // Pre-calculate status to avoid repeated checks
        $status = self::calculateVehicleStatus($vehicle, $driver, $currentTrip);
        $markerColor = self::getMarkerColorForStatus($status);

        return [
            'id' => $vehicle->id,
            'lat' => $coordinates['lat'],
            'lng' => $coordinates['lng'],
            'driver' => $driver ? self::prepareDriverData($driver) : null,
            'vehicle' => self::prepareVehicleData($vehicle),
            'availability' => $driver->user->status?->value ?? 'offline',
            'status' => $status,
            'current_trip' => $currentTrip ? self::prepareTripData($currentTrip) : null,
            'marker_color' => $markerColor,
        ];
    }

    /**
     * Calculate vehicle status efficiently
     */
    private static function calculateVehicleStatus(Vehicle $vehicle, $driver, $currentTrip): string
    {
        if ($currentTrip) {
            return 'on trip';
        }

        if (! $driver || $vehicle->status?->value !== 'online') {
            return 'offline';
        }

        // Check if driver is blocked
        if ($driver->global_status?->value === 'blocked') {
            return 'blocked';
        }

        if ($vehicle->global_status?->value !== 'active' ||
            $driver->global_status?->value !== 'active') {
            return 'offline';
        }

        $userStatus = $driver->user->status?->value ?? UserStatus::OFFLINE->value;

        return match ($userStatus) {
            UserStatus::ONLINE->value => 'available',
            UserStatus::BUSY->value => 'on trip',
            default => 'offline'
        };
    }

    /**
     * Get marker color for status
     */
    private static function getMarkerColorForStatus(string $status): string
    {
        return match ($status) {
            'available' => '#16a34a', // green-600
            'on trip' => '#dc2626',   // red-600
            'blocked' => '#6b7280',   // gray-500 (same as offline but for blocked drivers)
            default => '#6b7280'      // gray-500
        };
    }

    /**
     * Prepare driver data efficiently
     */
    private static function prepareDriverData($driver): array
    {
        // Fallback to manual concatenation if full_name is not available
        $driverName = $driver->user->full_name ??
                     trim(($driver->user->name ?? '').' '.($driver->user->last_name ?? '')) ?:
                     'Unknown';

        return [
            'id' => $driver->id,
            'name' => $driverName,
            'phone' => $driver->user->phone_number_formatted ?? 'N/A',
            'rating' => $driver->average_driver_rating ?? 0,
            'gender' => $driver->user->gender?->value ?? 'unknown',
            'global_status' => $driver->global_status?->value ?? 'unknown',
            'user_status' => $driver->user->status?->value ?? 'unknown',
            'cover_picture' => self::getImageUrl($driver->user->cover_picture, 'images/avatar.png'),
        ];
    }

    /**
     * Prepare vehicle data efficiently
     */
    private static function prepareVehicleData(Vehicle $vehicle): array
    {
        return [
            'model' => $vehicle->vehicleModel->name_en ?? 'Unknown',
            'brand' => $vehicle->vehicleModel->vehicleBrand->name_en ?? 'Unknown',
            'type' => $vehicle->vehicleType->name_en ?? 'Unknown',
            'rating' => $vehicle->average_vehicle_rating ?? 0,
            'equipments' => $vehicle->vehicleEquipments->pluck('name_en')->toArray(),
            'global_status' => $vehicle->global_status?->value ?? 'unknown',
            'status' => $vehicle->status?->value ?? 'unknown',
            'image' => self::getImageUrl($vehicle->image, 'images/vehicle.jpg'),
        ];
    }

    /**
     * Prepare trip data efficiently
     */
    private static function prepareTripData($trip): array
    {
        // Ensure tripLocation is loaded
        $tripLocation = $trip->tripLocation;

        // Fallback: Try to load tripLocation if it's not loaded but trip_location_id exists
        if (! $tripLocation && $trip->trip_location_id) {
            try {
                $trip->load('tripLocation');
                $tripLocation = $trip->tripLocation;
            } catch (\Exception $e) {
                // Silently handle the error - tripLocation will remain null
            }
        }

        return [
            'id' => $trip->id,
            'status' => $trip->status->value,
            'status_label' => $trip->status->getLabel(),
            'departure_address' => $tripLocation?->departure_address ?? 'Unknown',
            'arrival_address' => $tripLocation?->arrival_address ?? 'Unknown',
            'departure_coordinates' => [
                'lat' => $tripLocation?->departure_lat ?? null,
                'lng' => $tripLocation?->departure_lng ?? null,
            ],
            'arrival_coordinates' => [
                'lat' => $tripLocation?->arrival_lat ?? null,
                'lng' => $tripLocation?->arrival_lng ?? null,
            ],
            'distance' => $trip->distance ?? 0,
            'estimated_departure_time' => $trip->estimated_departure_time?->format('Y-m-d H:i:s'),
            'estimated_arrival_time' => $trip->estimated_arrival_time?->format('Y-m-d H:i:s'),
            'actual_departure_time' => $trip->actual_departure_time?->format('Y-m-d H:i:s'),
            'eta_minutes' => $trip->estimated_arrival_time ?
                max(0, (int) ceil(now()->diffInMinutes($trip->estimated_arrival_time, false))) : null,
            'rider' => [
                'id' => $trip->rider->id,
                'name' => $trip->rider->user->full_name ??
                         trim(($trip->rider->user->name ?? '').' '.($trip->rider->user->last_name ?? '')) ?:
                         'Unknown',
                'phone' => $trip->rider->user->phone_number_formatted ?? 'N/A',
                'rating' => $trip->rider->average_rider_rating ?? 0,
                'gender' => $trip->rider->user->gender?->value ?? 'unknown',
                'cover_picture' => self::getImageUrl($trip->rider->user->cover_picture, 'images/avatar.png'),
            ],
            'rider_notes' => $trip->rider_notes,
            'pricing' => $trip->pricing_breakdown_array ?? [],
        ];
    }

    /**
     * Prepare vehicle data from Eloquent model (legacy method for backward compatibility)
     */
    private static function prepareEloquentVehicleData(Vehicle $vehicle): array
    {
        $driver = $vehicle->drivers->first();
        $currentTrip = $driver?->trips->first();

        // Use pre-extracted driver location coordinates or extract from location field
        $driverLocation = null;
        if ($driver) {
            if (isset($driver->driver_lat) && isset($driver->driver_lng)) {
                // Use pre-extracted coordinates (PostgreSQL)
                $driverLocation = [
                    'lat' => (float) $driver->driver_lat,
                    'lng' => (float) $driver->driver_lng,
                ];
            } elseif ($driver->location) {
                // Extract coordinates from location field (SQLite/other databases)
                $driverLocation = self::extractLocationCoordinates($driver->location);
            }
        }

        // Determine vehicle status based on multiple factors
        $status = 'offline';
        $markerColor = '#6b7280'; // gray-500

        // Check if vehicle has an active trip first
        if ($currentTrip) {
            $status = 'on trip';
            $markerColor = '#dc2626'; // red-600
        }
        // Check if driver is blocked
        elseif ($driver && $driver->global_status?->value === 'blocked') {
            $status = 'blocked';
            $markerColor = '#6b7280'; // gray-500
        }
        // Check if vehicle and driver are active
        elseif (
            $vehicle->global_status?->value === 'active' &&
            $vehicle->status?->value === 'online' &&
            $driver->global_status?->value === 'active'
        ) {
            // Check user status - this is the key factor
            $userStatus = $driver->user->status?->value ?? UserStatus::OFFLINE->value;

            if ($userStatus === UserStatus::ONLINE->value) {
                $status = 'available';
                $markerColor = '#16a34a'; // green-600
            } elseif ($userStatus === UserStatus::BUSY->value) {
                $status = 'on trip'; // Busy user means driver is on a trip
                $markerColor = '#dc2626'; // red-600
            }
            // If user is offline, keep vehicle as offline (gray)
        }

        return [
            'id' => $vehicle->id,
            'lat' => $driverLocation['lat'] ?? 0,
            'lng' => $driverLocation['lng'] ?? 0,
            'driver' => $driver ? [
                'id' => $driver->id,
                'name' => $driver->user->full_name ??
                         trim(($driver->user->name ?? '').' '.($driver->user->last_name ?? '')) ?:
                         'Unknown',
                'phone' => $driver->user->phone_number_formatted ?? 'N/A',
                'rating' => $driver->average_driver_rating ?? 0,
                'gender' => $driver->user->gender?->value ?? 'unknown',
                'global_status' => $driver->global_status?->value ?? 'unknown',
                'user_status' => $driver->user->status?->value ?? 'unknown',
                'cover_picture' => self::getImageUrl($driver->user->cover_picture, 'images/avatar.png'),
            ] : null,
            'vehicle' => [
                'model' => $vehicle->vehicleModel->name_en ?? 'Unknown',
                'brand' => $vehicle->vehicleModel->vehicleBrand->name_en ?? 'Unknown',
                'type' => $vehicle->vehicleType->name_en ?? 'Unknown',
                'rating' => $vehicle->average_vehicle_rating ?? 0,
                'equipments' => $vehicle->vehicleEquipments->pluck('name_en')->toArray(),
                'global_status' => $vehicle->global_status?->value ?? 'unknown',
                'status' => $vehicle->status?->value ?? 'unknown',
                'image' => self::getImageUrl($vehicle->image, 'images/vehicle.jpg'),
            ],
            'availability' => $driver->user->status?->value ?? 'offline',
            'status' => $status,
            'current_trip' => $currentTrip ? [
                'id' => $currentTrip->id,
                'status' => $currentTrip->status->value,
                'status_label' => $currentTrip->status->getLabel(),
                'departure_address' => $currentTrip->tripLocation?->departure_address ?? 'Unknown',
                'arrival_address' => $currentTrip->tripLocation?->arrival_address ?? 'Unknown',
                'departure_coordinates' => [
                    'lat' => $currentTrip->tripLocation?->departure_lat ?? null,
                    'lng' => $currentTrip->tripLocation?->departure_lng ?? null,
                ],
                'arrival_coordinates' => [
                    'lat' => $currentTrip->tripLocation?->arrival_lat ?? null,
                    'lng' => $currentTrip->tripLocation?->arrival_lng ?? null,
                ],
                'distance' => $currentTrip->distance ?? 0,
                'estimated_departure_time' => $currentTrip->estimated_departure_time?->format('Y-m-d H:i:s'),
                'estimated_arrival_time' => $currentTrip->estimated_arrival_time?->format('Y-m-d H:i:s'),
                'actual_departure_time' => $currentTrip->actual_departure_time?->format('Y-m-d H:i:s'),
                'eta_minutes' => $currentTrip->estimated_arrival_time ?
                    max(0, (int) ceil(now()->diffInMinutes($currentTrip->estimated_arrival_time, false))) : null,
                'rider' => [
                    'id' => $currentTrip->rider->id,
                    'name' => $currentTrip->rider->user->full_name ??
                             trim(($currentTrip->rider->user->name ?? '').' '.($currentTrip->rider->user->last_name ?? '')) ?:
                             'Unknown',
                    'phone' => $currentTrip->rider->user->phone_number_formatted ?? 'N/A',
                    'rating' => $currentTrip->rider->average_rider_rating ?? 0,
                    'gender' => $currentTrip->rider->user->gender?->value ?? 'unknown',
                    'cover_picture' => self::getImageUrl($currentTrip->rider->user->cover_picture, 'images/avatar.png'),
                ],
                'rider_notes' => $currentTrip->rider_notes,
                'pricing' => $currentTrip->pricing_breakdown_array ?? [],
            ] : null,
            'marker_color' => $markerColor,
        ];
    }

    /**
     * Extract coordinates from location field for non-PostgreSQL databases
     */
    private static function extractLocationCoordinates(?string $location): ?array
    {
        if (empty($location)) {
            return null;
        }

        // Handle WKT format: POINT(lng lat)
        if (preg_match('/POINT\(([+-]?\d*\.?\d+)\s+([+-]?\d*\.?\d+)\)/', $location, $matches)) {
            return [
                'lat' => (float) $matches[2],
                'lng' => (float) $matches[1],
            ];
        }

        // Handle JSON format: {"lat": 123, "lng": 456}
        $decoded = json_decode($location, true);
        if (is_array($decoded) && isset($decoded['lat']) && isset($decoded['lng'])) {
            return [
                'lat' => (float) $decoded['lat'],
                'lng' => (float) $decoded['lng'],
            ];
        }

        return null;
    }

    /**
     * Helper method to handle both local and external image URLs
     */
    private static function getImageUrl(?string $imagePath, string $fallbackPath): string
    {
        if (! $imagePath) {
            return asset($fallbackPath);
        }

        // Check if it's already a full URL (external image)
        if (filter_var($imagePath, FILTER_VALIDATE_URL)) {
            return $imagePath;
        }

        // Check if it starts with http:// or https:// (external image)
        if (str_starts_with($imagePath, 'http://') || str_starts_with($imagePath, 'https://')) {
            return $imagePath;
        }

        // It's a local file path, prepend storage path
        return asset('storage/'.$imagePath);
    }
}
