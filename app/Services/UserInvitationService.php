<?php

namespace App\Services;

use App\Models\User;
use App\Models\UserInvitation;
use App\Notifications\UserInvitationNotification;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class UserInvitationService
{
    /**
     * Create and send a user invitation
     */
    public function createInvitation(string $email, int $invitedBy, int $expirationDays = 7): UserInvitation
    {
        // Check if user already exists
        if (User::where('email', $email)->exists()) {
            throw new \Exception('A user with this email already exists.');
        }

        // Check if there's already a valid invitation for this email
        $existingInvitation = UserInvitation::where('email', $email)
            ->valid()
            ->first();

        if ($existingInvitation) {
            throw new \Exception('A valid invitation for this email already exists.');
        }

        // Create the invitation
        $invitation = UserInvitation::create([
            'email' => $email,
            'token' => UserInvitation::generateToken(),
            'invited_by' => $invitedBy,
            'expires_at' => Carbon::now()->addDays($expirationDays),
        ]);

        // Send the invitation email
        $this->sendInvitationEmail($invitation);

        return $invitation;
    }

    /**
     * Create and send a user invitation for an existing user (used when admin user is created)
     */
    public function createInvitationForExistingUser(string $email, int $invitedBy, int $expirationDays = 7): UserInvitation
    {
        // Check if there's already a valid invitation for this email
        $existingInvitation = UserInvitation::where('email', $email)
            ->valid()
            ->first();

        if ($existingInvitation) {
            throw new \Exception('A valid invitation for this email already exists.');
        }

        // Create the invitation
        $invitation = UserInvitation::create([
            'email' => $email,
            'token' => UserInvitation::generateToken(),
            'invited_by' => $invitedBy,
            'expires_at' => Carbon::now()->addDays($expirationDays),
        ]);

        // Send the invitation email
        $this->sendInvitationEmail($invitation);

        return $invitation;
    }

    /**
     * Create and send a user invitation for a newly created user (they need to set their password)
     */
    public function createInvitationForNewUser(string $email, int $invitedBy, int $expirationDays = 7): UserInvitation
    {
        // Check if there's already a valid invitation for this email
        $existingInvitation = UserInvitation::where('email', $email)
            ->valid()
            ->first();

        if ($existingInvitation) {
            // If invitation already exists, just resend it
            $this->sendInvitationEmail($existingInvitation);

            return $existingInvitation;
        }

        // Create the invitation (user already exists, they just need to set password)
        $invitation = UserInvitation::create([
            'email' => $email,
            'token' => UserInvitation::generateToken(),
            'invited_by' => $invitedBy,
            'expires_at' => Carbon::now()->addDays($expirationDays),
        ]);

        // Send the invitation email
        $this->sendInvitationEmail($invitation);

        return $invitation;
    }

    /**
     * Send invitation email
     */
    public function sendInvitationEmail(UserInvitation $invitation): void
    {
        // Create a temporary user object for the notification
        $tempUser = new User(['email' => $invitation->email]);

        $tempUser->notify(new UserInvitationNotification($invitation));
    }

    /**
     * Accept an invitation and create the user account
     */
    public function acceptInvitation(string $token, string $email, string $password, array $userData = []): User
    {
        return DB::transaction(function () use ($token, $email, $password, $userData) {
            // Find the invitation
            $invitation = UserInvitation::where('token', $token)
                ->where('email', $email)
                ->first();

            if (! $invitation) {
                throw new \Exception('Invalid invitation token.');
            }

            if (! $invitation->isValid()) {
                throw new \Exception('This invitation has expired or has already been used.');
            }

            // Check if user already exists
            $existingUser = User::where('email', $email)->first();

            if ($existingUser) {
                // User exists, just update their password and details
                $existingUser->update(array_merge([
                    'password' => Hash::make($password),
                    'email_verified_at' => now(),
                ], $userData));

                // Mark invitation as accepted
                $invitation->markAsAccepted();

                return $existingUser;
            } else {
                // Create new user
                $user = User::create(array_merge([
                    'email' => $email,
                    'password' => Hash::make($password),
                    'type' => 'admin',
                    'email_verified_at' => now(),
                    'phone_number' => '+218'.rand(900000000, 999999999), // Generate a temporary phone number
                ], $userData));

                // Mark invitation as accepted
                $invitation->markAsAccepted();

                return $user;
            }
        });
    }

    /**
     * Resend an invitation
     */
    public function resendInvitation(UserInvitation $invitation, int $expirationDays = 7): UserInvitation
    {
        if ($invitation->isAccepted()) {
            throw new \Exception('This invitation has already been accepted.');
        }

        // Update the invitation with new token and expiration
        $invitation->update([
            'token' => UserInvitation::generateToken(),
            'expires_at' => Carbon::now()->addDays($expirationDays),
        ]);

        // Send the invitation email
        $this->sendInvitationEmail($invitation);

        return $invitation;
    }

    /**
     * Cancel an invitation
     */
    public function cancelInvitation(UserInvitation $invitation): bool
    {
        if ($invitation->isAccepted()) {
            throw new \Exception('Cannot cancel an invitation that has already been accepted.');
        }

        return $invitation->delete();
    }

    /**
     * Clean up expired invitations
     */
    public function cleanupExpiredInvitations(): int
    {
        return UserInvitation::expired()->delete();
    }

    /**
     * Get invitation by token and email
     */
    public function getInvitationByToken(string $token, string $email): ?UserInvitation
    {
        return UserInvitation::where('token', $token)
            ->where('email', $email)
            ->first();
    }

    /**
     * Validate invitation data
     */
    public function validateInvitation(string $token, string $email): array
    {
        $invitation = $this->getInvitationByToken($token, $email);

        if (! $invitation) {
            return [
                'valid' => false,
                'message' => 'Invalid invitation link.',
                'invitation' => null,
            ];
        }

        if ($invitation->isExpired()) {
            return [
                'valid' => false,
                'message' => 'This invitation has expired.',
                'invitation' => $invitation,
            ];
        }

        if ($invitation->isAccepted()) {
            return [
                'valid' => false,
                'message' => 'This invitation has already been used.',
                'invitation' => $invitation,
            ];
        }

        // Check if user exists and if they have completed their setup
        $existingUser = User::where('email', $email)->first();
        if ($existingUser) {
            // If user exists but has email_verified_at set, they have completed setup
            // Users created by admin will not have email_verified_at until they accept invitation
            if ($existingUser->email_verified_at !== null) {
                return [
                    'valid' => false,
                    'message' => 'A user with this email already exists and has completed setup.',
                    'invitation' => $invitation,
                ];
            }
        }

        return [
            'valid' => true,
            'message' => 'Invitation is valid.',
            'invitation' => $invitation,
        ];
    }
}
