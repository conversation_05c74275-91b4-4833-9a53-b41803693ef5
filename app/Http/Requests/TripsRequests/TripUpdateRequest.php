<?php

namespace App\Http\Requests\TripsRequests;

use App\Enums\Trips\CancellationStage;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class TripUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'departure_location.address' => ['sometimes', 'string'],
            'departure_location.full_address' => ['sometimes', 'string'],
            'departure_location.latitude' => ['sometimes', 'numeric'],
            'departure_location.longitude' => ['sometimes', 'numeric'],
            'arrival_location.latitude' => ['sometimes', 'numeric'],
            'arrival_location.longitude' => ['sometimes', 'numeric'],
            'arrival_location.address' => ['sometimes', 'string'],
            'arrival_location.full_address' => ['sometimes', 'string'],
            'distance' => ['sometimes', 'numeric'],
            'is_favorite' => ['sometimes', 'boolean'],
            'status' => ['sometimes', 'string'],
            // 'driver_id' => ['sometimes', 'integer', 'exists:drivers,id'],
            // 'vehicle_id' => ['sometimes', 'integer', 'exists:vehicles,id'],
            'estimated_departure_time' => ['sometimes', 'date'],
            'actual_departure_time' => ['sometimes', 'date'],
            'estimated_arrival_time' => ['sometimes', 'date'],
            'actual_arrival_time' => ['sometimes', 'date'],
            'pricing_breakdown' => ['sometimes', 'array'],
            'cancelled_by' => ['sometimes', 'string', Rule::in(['rider', 'driver'])],
            'cancellation_stage' => ['sometimes', 'string', Rule::in(array_column(CancellationStage::cases(), 'value'))],
            'rider_notes' => ['sometimes', 'string', 'max:100'],
            'vehicle_category' => ['sometimes', 'string', Rule::in(['passenger', 'freight'])],
            'vehicle_equipments' => ['sometimes', 'required_if:vehicle_category,passenger', 'array'],
            'number_of_seats' => [
                'sometimes',
                'required_if:vehicle_category,passenger',
                'prohibited_if:vehicle_category,freight',
                'integer',
                Rule::in([2, 4, 6]),
            ],
            'weight_category' => [
                'sometimes',
                'required_if:vehicle_category,freight',
                'string',
                Rule::in(['less_than_1000kg', 'more_than_1000kg']),
            ],
            'is_covered' => [
                'sometimes',
                'required_if:vehicle_category,freight',
                'boolean',
            ],
            'is_female' => ['sometimes', 'boolean'],
        ];
    }
}
