<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class VehicleCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection->map(function ($vehicle) {
                return [
                    'id' => $vehicle->id,
                    'brand' => $vehicle->vehicleModel->vehicleBrand->name_en ?? null,
                    'brand_en' => $vehicle->vehicleModel->vehicleBrand->name_en ?? null,
                    'brand_ar' => $vehicle->vehicleModel->vehicleBrand->name_ar ?? null,
                    'model' => $vehicle->vehicleModel->name_en ?? null,
                    'model_en' => $vehicle->vehicleModel->name_en ?? null,
                    'model_ar' => $vehicle->vehicleModel->name_ar ?? null,
                    'plate_number' => $vehicle->license_plate_number ?? null,
                    'seat_number' => $vehicle->seat_number ?? null,
                    'year' => $vehicle->year ?? null,
                    'color' => $vehicle->color ?? null,
                    'global_status' => $vehicle->global_status?->value ?? null,
                    'category' => $vehicle->vehicleType?->category ?? null,
                    'is_covered' => $vehicle->vehicleType?->is_covered ?? null,
                    'weight_category' => $vehicle->vehicleType?->weight_category ?? null,
                    'average_vehicle_rating' => $vehicle->average_vehicle_rating ?? null,
                    'image' => $vehicle->image
                        ? env('APP_URL', '/').'/storage/'.$vehicle->image
                        : env('APP_URL', '/').'/images/vehicle.jpg',
                    'vehicle_equipments' => $vehicle->vehicleEquipments->pluck('id')->toArray(),
                    'vehicle_documents' => [
                        'insurance' => $vehicle->documents->insurance
                            ? env('APP_URL', '/').'/storage/'.$vehicle->documents->insurance
                            : null,
                        'technical_inspection' => $vehicle->documents->technical_inspection
                            ? env('APP_URL', '/').'/storage/'.$vehicle->documents->technical_inspection
                            : null,
                        'roaming_permit' => $vehicle->documents->roaming_permit
                            ? env('APP_URL', '/').'/storage/'.$vehicle->documents->roaming_permit
                            : null,
                        'insurance_expiry' => $vehicle->documents->insurance_expiry ?? null,
                        'technical_inspection_expiry' => $vehicle->documents->technical_inspection_expiry ?? null,
                        'roaming_permit_expiry' => $vehicle->documents->roaming_permit_expiry ?? null,
                    ],
                ];
            }),
        ];
    }
}
