<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class RecentTripsCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     */
    public function toArray(Request $request)
    {
        $data = $this->collection->map(function ($item) {
            return [
                'id' => $item->id,
                'arrivalAddress' => [
                    'arrival_address' => $item->tripLocation?->arrival_address ?? null,
                    'address_label' => $item->arrival_label ?? null,
                    'latitude' => $item->tripLocation?->arrival_lat ?? null,
                    'longitude' => $item->tripLocation?->arrival_lng ?? null,
                    'is_favorite' => $item->arrival_is_favorite ?? false,
                ],
            ];
        });

        return [
            'rides' => $data,
        ];
    }
}
