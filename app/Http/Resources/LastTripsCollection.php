<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class LastTripsCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     * This collection is specifically for the last 5 trips endpoint
     * and only includes arrival addresses with is_favorite field.
     */
    public function toArray(Request $request)
    {
        $data = $this->collection->map(function ($item) {
            return [
                'id' => $item->id,
                'date' => $item->created_at,
                'arrivalAddress' => [
                    'address_id' => $item->arrival_address_id ?? null,
                    'arrival_address' => $item->tripLocation?->arrival_address ?? null,
                    'full_address' => $item->arrival_full_address ?? null,
                    'address_label' => $item->arrival_label ?? null,
                    'latitude' => $item->tripLocation?->arrival_lat ?? null,
                    'longitude' => $item->tripLocation?->arrival_lng ?? null,
                    'is_favorite' => $item->arrival_is_favorite ?? false,
                ],
            ];
        });

        return [
            'rides' => $data,
        ];
    }
}
