<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class RiderFavoritesCollection extends ResourceCollection
{
    /**
     * Transform the resource collection into an array.
     *
     * @return array<int|string, mixed>
     */
    public function toArray(Request $request): array
    {
        $data = [];

        $data = $this->collection->map(function ($item) {
            if ($this->collection[0] instanceof \App\Models\Address) {
                return [
                    'id' => $item->id,
                    'address' => $item->address ?? null,
                    'full_address' => $item->full_address ?? null,
                    'label' => $item->label->label ?? null,
                    'icon' => $item->label ? $item->label->getIconNameAttribute() : null,
                    'icon_svg' => $item->label ? $item->label->getIconSvgAttribute() : null,
                    // 'postal_address' => $item->postal_address ?? null,
                    'latitude' => $item->latitude ?? null,
                    'longitude' => $item->longitude ?? null,
                    'is_shortcut' => (bool) ($item->shortcut ?? false),
                ];
            } elseif ($this->collection[0] instanceof \App\Models\Trip) {
                return [
                    'id' => $item->id,
                    'departureAddress' => [
                        'address' => $item->tripLocation->departure_address ?? null,
                        'longitude' => $item->tripLocation->departure_lng ?? null,
                        'latitude' => $item->tripLocation->departure_lat ?? null,
                        'label' => $item->departure_label ?? null,
                    ],
                    'arrivalAddress' => [
                        'address' => $item->tripLocation->arrival_address ?? null,
                        'longitude' => $item->tripLocation->arrival_lng ?? null,
                        'latitude' => $item->tripLocation->arrival_lat ?? null,
                        'label' => $item->arrival_label ?? null,
                    ],
                ];
            }
        });

        return [
            'data' => $data,
            'meta' => [
                'current_page' => $this->currentPage(),
                'from' => $this->firstItem(),
                'to' => $this->lastItem(),
                'per_page' => $this->perPage(),
                'total' => $this->total(),
                'last_page' => $this->lastPage(),
                'next_page_url' => $this->nextPageUrl(),
                'prev_page_url' => $this->previousPageUrl(),
            ],
        ];
    }
}
