<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class VehicleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {

        return [
            'id' => $this->id,
            'brand' => $this->vehicleModel->vehicleBrand->name_en ?? null,
            'brand_en' => $this->vehicleModel->vehicleBrand->name_en ?? null,
            'brand_ar' => $this->vehicleModel->vehicleBrand->name_ar ?? null,
            'model' => $this->vehicleModel->name_en ?? null,
            'model_en' => $this->vehicleModel->name_en ?? null,
            'model_ar' => $this->vehicleModel->name_ar ?? null,
            'plate_number' => $this->license_plate_number ?? null,
            'seat_number' => $this->seat_number ?? null,
            'year' => $this->year ?? null,
            'color' => $this->color ?? null,
            'category' => $this->vehicleType?->category ?? null,
            'is_covered' => $vehicle->vehicleType?->is_covered ?? null,
            'weight_category' => $vehicle->vehicleType?->weight_category ?? null,
            'global_status' => $this->global_status?->value ?? null,
            'average_vehicle_rating' => $this->getDisplayableAverageRating() ?? null,
            'image' => $this->image
                ? env('APP_URL', '/').'/storage/'.$this->image
                : env('APP_URL', '/').'/images/vehicle.jpg',
            'vehicle_equipments' => $this->vehicleEquipments->pluck('id')->toArray(),
            'vehicle_documents' => [
                'insurance' => $this->documents->insurance
                    ? env('APP_URL', '/').'/storage/'.$this->documents->insurance
                    : null,
                'technical_inspection' => $this->documents->technical_inspection
                    ? env('APP_URL', '/').'/storage/'.$this->documents->technical_inspection
                    : null,
                'roaming_permit' => $this->documents->roaming_permit
                    ? env('APP_URL', '/').'/storage/'.$this->documents->roaming_permit
                    : null,
                'insurance_expiry' => $this->documents->insurance_expiry ?? null,
                'technical_inspection_expiry' => $this->documents->technical_inspection_expiry ?? null,
                'roaming_permit_expiry' => $this->documents->roaming_permit_expiry ?? null,
            ],
        ];
    }
}
