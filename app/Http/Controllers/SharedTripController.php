<?php

namespace App\Http\Controllers;

use App\Models\Trip;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SharedTripController extends Controller
{
    /**
     * Display the shared trip view
     */
    public function show(Request $request, $shareToken)
    {
        // Verify the signature
        if (! $request->hasValidSignature()) {
            abort(401, 'This link has expired or is invalid');
        }

        // Find the trip by share token
        $trip = Trip::where('share_token', $shareToken)
            ->with([
                'rider.user',
                'driver.user',
                'vehicle.vehicleModel.vehicleBrand',
                'tripLocation',
                'vehicleType',
            ])
            ->firstOrFail();

        // Check if trip is active
        $isActive = in_array($trip->status->value, ['assigned', 'driver_arriving', 'driver_arrived', 'on_trip', 'waiting_for_driver_confirmation']);

        // Get driver's current location from database
        $driverLocation = null;
        if ($isActive && $trip->driver) {
            $driverLocation = DB::selectOne(
                'SELECT ST_X(location::geometry) as lng, ST_Y(location::geometry) as lat
                FROM drivers
                WHERE id = ?',
                [$trip->driver->id]
            );

            // Log the raw driver location for debugging
            Log::info('Driver location from DB query', [
                'driver_id' => $trip->driver->id,
                'location' => $driverLocation,
            ]);
        }

        // Calculate ETA
        $etaText = 'Trip completed';
        $etaMinutes = null;

        if ($isActive && $trip->estimated_arrival_time) {
            $etaMinutes = now()->diffInMinutes($trip->estimated_arrival_time, false);
            if ($etaMinutes > 0) {
                // Round the minutes to the nearest integer
                $roundedMinutes = round($etaMinutes);
                $etaText = $roundedMinutes.' min';
            } else {
                $etaText = 'Arriving';
            }
        } elseif ($trip->status->value === 'completed' && $trip->actual_departure_time && $trip->actual_arrival_time) {
            $duration = $trip->actual_departure_time->diffInMinutes($trip->actual_arrival_time);
            $etaText = $duration.' min';
            $etaMinutes = $duration;
        } else {
            $etaText = 'Trip completed';
        }

        // Prepare trip data for the map
        $tripData = [
            'current_location' => $driverLocation ? [
                'lat' => (float) $driverLocation->lat,
                'lng' => (float) $driverLocation->lng,
            ] : null,
            'departure' => $trip->tripLocation ? [
                'lat' => (float) $trip->tripLocation->departure_lat,
                'lng' => (float) $trip->tripLocation->departure_lng,
            ] : null,
            'arrival' => $trip->tripLocation ? [
                'lat' => (float) $trip->tripLocation->arrival_lat,
                'lng' => (float) $trip->tripLocation->arrival_lng,
            ] : null,
            'etaText' => $etaText,
            'etaMinutes' => $etaMinutes ? round($etaMinutes) : null, // Round the numeric value
            'polyline' => $trip->tripLocation->polyline ?? null,
            'isActive' => $isActive,
        ];

        // Log the trip data for debugging
        Log::info('Trip data for shared trip view', [
            'trip_id' => $trip->id,
            'tripData' => $tripData,
        ]);

        return view('trips.shared-trip', compact('trip', 'tripData'));
    }
}
