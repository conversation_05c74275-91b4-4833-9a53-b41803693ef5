<?php

namespace App\Http\Controllers;

use App\Services\GoogleMapsService;
use Illuminate\Http\Request;

class MapController extends Controller
{
    public function getDistanceInfo(Request $request)
    {
        $origin = $request->input('origin'); // e.g. "40.6655101,-73.8918897"
        $destination = $request->input('destination'); // e.g. "40.6905615,-73.9976592"

        // Parse coordinates from string format to array format
        [$originLat, $originLng] = explode(',', $origin);
        [$destLat, $destLng] = explode(',', $destination);

        $originArray = ['lat' => (float) $originLat, 'lng' => (float) $originLng];
        $destinationArray = ['lat' => (float) $destLat, 'lng' => (float) $destLng];

        // Use GoogleMapsService instead of direct API call for consistency and caching
        $googleMapsService = app(GoogleMapsService::class);
        $routeData = $googleMapsService->getDistanceAndTime($originArray, $destinationArray, true);

        if (! $routeData) {
            return response()->json(['status' => 'ZERO_RESULTS'], 200);
        }

        // Format response to match Distance Matrix API format for backward compatibility
        $response = [
            'destination_addresses' => [$destination],
            'origin_addresses' => [$origin],
            'rows' => [
                [
                    'elements' => [
                        [
                            'distance' => [
                                'text' => $routeData['distance_text'],
                                'value' => $routeData['distance_value'],
                            ],
                            'duration' => [
                                'text' => $routeData['duration_text'],
                                'value' => $routeData['duration_value'],
                            ],
                            'status' => 'OK',
                        ],
                    ],
                ],
            ],
            'status' => 'OK',
        ];

        // Add traffic duration if available
        if (isset($routeData['duration_in_traffic_text'])) {
            $response['rows'][0]['elements'][0]['duration_in_traffic'] = [
                'text' => $routeData['duration_in_traffic_text'],
                'value' => $routeData['duration_in_traffic_value'],
            ];
        }

        return response()->json($response);
    }
}
