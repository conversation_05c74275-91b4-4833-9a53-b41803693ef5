<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\VehicleCollection;
use App\Http\Resources\VehicleResource;
use App\Http\Responses\ApiResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;

class DriversVehicleController extends Controller
{
    /**
     * List vehicles
     *
     * @response array{message: string, location: array , data: string, status: integer}
     */
    public function ListVehicles(Request $request)
    {

        if (Auth::user()->type !== 'driver') {
            return ApiResponse::error(null, 'Unauthorized access', 403, null);
        }

        $user = Auth::user()->load('driver');
        if (! $user->driver) {
            return ApiResponse::error(null, 'User is not a driver', 404, null);
        }

        // Get vehicles excluding deleted ones
        $vehicles = $user->driver->vehicles()
            ->where('global_status', '!=', \App\Enums\Vehicles\VehicleStatus::deleted)
            ->get();

        return ApiResponse::success(new VehicleCollection($vehicles), 'Vehicles retrieved successfully', 200, null);
    }

    public function vehicleDetails(Request $request, $vehicleId)
    {
        if (Auth::user()->type !== 'driver') {
            return ApiResponse::error(null, 'Unauthorized access', 403, null);
        }
        $vehicle = Auth::user()->driver->vehicles()
            ->where('id', $vehicleId)
            ->where('global_status', '!=', \App\Enums\Vehicles\VehicleStatus::deleted)
            ->with('vehicleEquipments', 'documents')
            ->first();
        if (! $vehicle) {
            return ApiResponse::error(null, 'Vehicle not found', 404, null);
        }

        return ApiResponse::success(new VehicleResource($vehicle), 'Vehicle retrieved successfully', 200, null);
    }

    /**
     * Delete vehicle
     *
     * @response array{message: string, location: array , data: string, status: integer}
     */
    public function DeleteVehicle($vehicleId)
    {
        $user = Auth::user()->load('driver');

        if (! $user->driver) {
            return ApiResponse::error(null, 'User is not a driver', 404, null);
        }

        $vehicle = $user->driver->vehicles()->where('id', $vehicleId)->first();

        if (! $vehicle) {
            return ApiResponse::error(null,
                $vehicleId
            ? "No vehicle found with the given ID: $vehicleId."
            : 'No vehicle found for the current driver. Please add a vehicle first.', 404, null);
        }

        // Set the vehicle global_status to deleted and soft delete it
        $vehicle->global_status = \App\Enums\Vehicles\VehicleStatus::deleted;
        $vehicle->save();

        if ($vehicle->delete()) {
            return ApiResponse::success(null, 'Vehicle deleted successfully', 200, null);
        }

        return ApiResponse::error(null, 'Failed to delete vehicle', 500, null);

    }
}
