<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\RiderPreferencesResource;
use App\Http\Responses\ApiResponse;
use App\Models\VehicleBrand;
use App\Models\VehicleEquipment;
use App\Models\VehicleType;

class ConfigController extends Controller
{
    /**
     * Get general configuration options for rider registration
     *
     *
     * @response array{data : RiderPreferencesResource , message: 'General configuration data', status: 200,error : string}
     */
    public function configuration()
    {
        $vehicleTypes = VehicleType::where('status', true)
            ->get(['id', 'name_en', 'name_ar', 'image', 'description_ar',
                'description_en', 'category', 'is_covered', 'weight_category']);
        $vehicleEquipments = VehicleEquipment::where('status', true)
            ->get(['id', 'name_en', 'name_ar', 'icon']);
        $vehicle_brands = VehicleBrand::where('status', true)
            ->with(['vehicleModels' => function ($query) {
                $query->where('status', true)
                    ->select('id', 'vehicle_brand_id', 'name_en', 'name_ar');
            }])->orderBy('created_at', 'desc')
            ->get(['id', 'name_en', 'name_ar']);
        $driverGender = ['male', 'female', 'both'];
        $seatsNumber = [2, 4, 6];
        $availableIcons = $this->getAvailableIcons();

        $data = collect([
            'driver_gender' => $driverGender,
            'car_brands' => $vehicle_brands,
            'seats_number' => $seatsNumber,
            'car_equipments' => $vehicleEquipments,
            'car_types' => $vehicleTypes,
            'available_icons' => $availableIcons,
        ]);

        return ApiResponse::success(new RiderPreferencesResource($data), 'General configuration data', 200);
    }

    /**
     * Get available icons for address labels and other UI elements
     *
     * @return \Illuminate\Support\Collection
     */
    private function getAvailableIcons()
    {
        return \App\Models\AvailableIcon::active()
            ->orderBy('category')
            ->orderBy('display_name')
            ->get()
            ->map(function ($icon) {
                return [
                    'id' => $icon->id,
                    'icon_name' => $icon->icon_name,
                    'display_name' => $icon->display_name,
                    'category' => $icon->category,
                    'icon_svg' => $icon->svg,
                ];
            });
    }
}
