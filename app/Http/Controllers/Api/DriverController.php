<?php

namespace App\Http\Controllers\Api;

use App\Enums\Drivers\DriverGlobalStatus;
use App\Enums\Trips\TripStatus;
use App\Enums\UserStatus;
use App\Enums\Vehicles\VehicleStatus;
use App\Events\DriverEvents\DriverLocationUpdated;
use App\Events\DriverEvents\RideRequest;
use App\Events\TripEvents\DriverArrived;
use App\Events\TripEvents\RequestResponse;
use App\Events\TripEvents\SearchResponse;
use App\Events\TripEvents\TripCompleted;
use App\Http\Controllers\Controller;
use App\Http\Requests\DriverVehicleStoreRequest;
use App\Http\Requests\VehicleUpdateRequest;
use App\Http\Resources\DriverResource;
use App\Http\Resources\UserResource;
use App\Http\Responses\ApiResponse;
use App\Models\Driver;
use App\Models\Trip;
use App\Models\Vehicle;
use App\Models\VehicleType;
use App\Notifications\DriverSafetyNotification;
use App\Services\DriverRequestService;
use App\Services\GoogleMapsService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class DriverController extends Controller
{
    protected $driverRequestService;

    public function __construct(DriverRequestService $driverRequestService)
    {
        $this->driverRequestService = $driverRequestService;
    }

    public function show(Request $request, Driver $driver): DriverResource
    {
        return new DriverResource($driver);
    }

    /**
     * Upload documents
     *
     * @response array{message: string, location: array , data: string, status: integer}
     */
    public function uploadDocuments(Request $request)
    {
        $files = ['document_front', 'document_back', 'license_front', 'license_back'];

        // Load the user and their documents
        $user = Auth::user()->load(['driver' => function ($query) {
            $query->withTrashed();
        }]);

        $driverDocument = $user->driver->documents;

        // If no driver document exists, return an error
        if (! $driverDocument) {
            return ApiResponse::error(null, 'An error occurred while uploading the document.', 400, null);
        }

        // Validate the request data
        $validated = $request->validate([
            'license_expiry' => ['sometimes', 'date', 'after_or_equal:'.now()->addYear()->toDateString()],
            'rejectionSubmit' => ['sometimes', 'string'],
            'document_front' => ['sometimes', 'file', 'mimes:jpeg,png,pdf', 'min:1', 'max:10240'],
            'document_back' => ['sometimes', 'file', 'mimes:jpeg,png,pdf', 'min:1', 'max:10240'],
            'license_front' => ['sometimes', 'file', 'mimes:jpeg,png,pdf', 'min:1', 'max:10240'],
            'license_back' => ['sometimes', 'file', 'mimes:jpeg,png,pdf', 'min:1', 'max:10240'],
        ]);

        try {
            // Check which files are actually included in the request
            $filesToUpdate = array_filter($files, fn ($field) => $request->hasFile($field));

            // Process the files to be uploaded
            foreach ($filesToUpdate as $field) {
                $file = $request->file($field);

                if ($file && $file->isValid()) {
                    $path = $file->store('driver/personal_documents', 'public');
                    $driverDocument->update([$field => $path]);
                } else {
                    return ApiResponse::error(null, "An error occurred while uploading the file $field", 500, null);
                }
            }

            // Update the license expiry if provided
            if (isset($validated['license_expiry'])) {
                $driverDocument['license_expiry'] = $validated['license_expiry'];
            }

            // Save the updated document data
            $driverDocument->save();

            if (isset($validated['rejectionSubmit']) && $validated['rejectionSubmit'] === 'true' && $user->driver != null) {
                if ($user->driver->deleted_at === null && $user->driver->global_status->value === DriverGlobalStatus::rejected->value) {
                    $user->driver->update([
                        'global_status' => DriverGlobalStatus::in_progress->value,
                        'previous_global_status' => DriverGlobalStatus::rejected->value]);
                }
            }

            return ApiResponse::success(null, 'Documents uploaded successfully', 200);
        } catch (\Exception $e) {
            return ApiResponse::error(null, 'An error occurred while uploading documents.', 500, $e->getMessage());
        }
    }

    /**
     * Create new vehicle
     *
     * @response array{message: string, location: array , data: string, status: integer}
     */
    public function addVehicle(DriverVehicleStoreRequest $request)
    {
        $validated = $request->validated();
        $user = Auth::user()->load(['driver' => function ($query) {
            return $query->withTrashed();
        }]);

        // Check if driver already has 3 active vehicles (excluding deleted ones)
        $vehicleCount = $user->driver->vehicles()
            ->where('global_status', '!=', \App\Enums\Vehicles\VehicleStatus::deleted)
            ->count();
        if ($vehicleCount >= 3) {
            return ApiResponse::error(null, 'You cannot add more than 3 vehicles to your account.', 422);
        }

        if (isset($validated['share_benefits'])) {
            if (strtolower($validated['share_benefits']) === 'false' || $validated['share_benefits'] === false) {
                return ApiResponse::error(null, 'Form validation failed the driver must enable the "Share Benefits" option.', 422);
            }
        }

        if (Vehicle::where('license_plate_number', $validated['license_plate_number'])->exists()) {
            return ApiResponse::error(null, 'Licence plate number already exists', 422);
        }

        $photo_path = $request->hasFile('vehicle_photo')
            ? $request->File('vehicle_photo')->store('vehicle/picture', 'public')
            : null;

        $insurance_path = $request->hasFile('insurance')
            ? $request->file('insurance')->store('driver/vehicle_documents', 'public')
            : null;

        $technical_inspection_path = $request->hasFile('technical_inspection')
            ? $request->file('technical_inspection')->store('driver/vehicle_documents', 'public')
            : null;

        $roaming_permit_path = $request->hasFile('roaming_permit')
            ? $request->file('roaming_permit')->store('driver/vehicle_documents', 'public')
            : null;

        if ($validated['vehicle_type'] === 'passenger') {
            $vehicleType = $this->createDefaultVehicleType('passenger');
            // if the driver is a female she can pick up only female gender.
            $vehicle = $user->driver->vehicles()->create([
                'image' => $photo_path,
                'license_plate_number' => $validated['license_plate_number'],
                'seat_number' => $validated['seats_number'],
                'color' => $validated['vehicle_color'],
                'year' => $validated['year'],
                'vehicle_model_id' => $validated['vehicle_model'],
                'vehicle_type_id' => $vehicleType,
                'is_female_only' => Auth()->user()->gender === 'female' ? true : false,
                'global_status' => VehicleStatus::in_progress->value, // Set vehicle to in_progress for approval
            ]);

            if (! empty($validated['vehicle_equipments'])) {
                $vehicleEquipments = json_decode($validated['vehicle_equipments'], true);
                $vehicle->vehicleEquipments()->attach(
                    collect($vehicleEquipments)->mapWithKeys(fn ($id) => [
                        $id => [
                            'created_at' => now(),
                            'updated_at' => now(),
                        ],
                    ])->all()
                );
                // $vehicle->vehicleEquipments()->sync($vehicleEquipments);
            }

        } elseif ($validated['vehicle_type'] === 'freight') {
            $is_covered = $validated['is_covered'] ?? null;
            $weight_category = $validated['weight_category'] ?? null;

            $vehicleType = $this->createDefaultVehicleType('freight', $is_covered, $weight_category);

            $vehicle = $user->driver->vehicles()->create([
                'image' => $photo_path,
                'license_plate_number' => $validated['license_plate_number'],
                'color' => $validated['vehicle_color'],
                'year' => $validated['year'],
                'vehicle_model_id' => $validated['vehicle_model'],
                'vehicle_type_id' => $vehicleType,
                'is_female_only' => Auth::user()->gender === 'female' ? true : false,
                'global_status' => VehicleStatus::in_progress->value, // Set vehicle to in_progress for approval
            ]);

        }

        $vehicle->documents()->create([
            'vehicle_id' => $vehicle->id,
            'insurance' => $insurance_path,
            'technical_inspection' => $technical_inspection_path,
            'roaming_permit' => $roaming_permit_path,
            'insurance_expiry' => $validated['insurance_expiry'] ?? null,
            'technical_inspection_expiry' => $validated['technical_inspection_expiry'] ?? null,
            'roaming_permit_expiry' => $validated['roaming_permit_expiry'] ?? null,
        ]);

        // For active drivers adding new vehicles, keep driver status as active
        // Only change driver status to in_progress if they are not already active
        if ($user->driver != null && $user->driver->deleted_at === null && $user->driver->global_status->value != DriverGlobalStatus::blocked->value) {
            // If driver is already active, don't change their status - only the vehicle needs approval
            if ($user->driver->global_status->value !== DriverGlobalStatus::active->value) {
                $user->driver->update([
                    'global_status' => DriverGlobalStatus::in_progress->value,
                    'previous_global_status' => DriverGlobalStatus::pending->value,
                ]);
            }
        } elseif ($user->driver != null && $user->driver->global_status->value === DriverGlobalStatus::blocked->value) {
            $user->driver->update([
                'previous_global_status' => DriverGlobalStatus::in_progress->value,
            ]);
        }

        return ApiResponse::success($vehicle, 'The driver’s vehicle has been successfully created.', 201);
    }

    public function createDefaultVehicleType(string $category, ?string $is_covered = null, ?string $weight_category = null)
    {
        $attributes = [
            'category' => $category,
            'is_covered' => strtolower($is_covered) === 'false' ? false : true,
            'weight_category' => $weight_category === 'over_1000' ? 'more_than_1000kg' : 'less_than_1000kg',
        ];

        $values = [
            'name_en' => 'default_'.ucfirst($category).'_type',
            'additional_base_fare' => 0,
            'additional_price_per_km' => 0,
            'base_fare_adjustment' => 0,
            'distance_fare_adjustment' => 0,
            'status' => false,
        ];

        if ($category === 'passenger') {
            $values['category'] = $category;
            $values['name_en'] = 'Default_passenger_type';

            $vehicleType = VehicleType::firstOrCreate(
                ['category' => $category, 'name_en' => 'Default_passenger_type'],
                $values
            );
        } else {
            $vehicleType = VehicleType::firstOrCreate($attributes, $values);
        }

        return $vehicleType->id;
    }

    /**
     * Update vehicle
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function UpdateVehicle(VehicleUpdateRequest $request, ?string $vehicleId = null)
    {
        try {
            $validated = $request->validated();
            $user = Auth::user()->load(['driver' => function ($query) {
                return $query->withTrashed();
            }]);

            // Find the vehicle to update
            $vehicle = $vehicleId
                ? Vehicle::find($vehicleId)
                : optional($user->driver)->vehicles()->orderBy('created_at')->first();

            if (! $vehicle) {
                return ApiResponse::error(
                    null,
                    $vehicleId
                        ? "No vehicle found with the given ID: $vehicleId."
                        : 'No vehicle found for the current driver. Please add a vehicle first.',
                    404
                );
            }

            // Process file uploads
            $photo_path = $request->hasFile('vehicle_photo')
                ? $request->File('vehicle_photo')->store('pictures', 'public')
                : null;

            $insurance_path = $request->hasFile('insurance')
                ? $request->file('insurance')->store('driver/vehicle_documents', 'public')
                : null;

            $technical_inspection_path = $request->hasFile('technical_inspection')
                ? $request->file('technical_inspection')->store('driver/vehicle_documents', 'public')
                : null;

            $roaming_permit_path = $request->hasFile('roaming_permit')
                ? $request->file('roaming_permit')->store('driver/vehicle_documents', 'public')
                : null;

            // Update vehicle based on type
            if ($validated['vehicle_type'] === 'passenger') {
                $vehicle->update([
                    'image' => $photo_path ?? $vehicle->image,
                    'license_plate_number' => $validated['license_plate_number'] ?? $vehicle->license_plate_number,
                    'seat_number' => $validated['seats_number'] ?? $vehicle->seat_number,
                    'color' => $validated['vehicle_color'] ?? $vehicle->color,
                    'year' => $validated['year'] ?? $vehicle->year,
                    'vehicle_model_id' => $validated['vehicle_model'] ?? $vehicle->vehicle_model_id,
                ]);

                // Handle vehicle equipments with improved error handling
                if (! empty($validated['vehicle_equipments'])) {
                    try {
                        $vehicleEquipments = json_decode($validated['vehicle_equipments'], true);
                        if (is_array($vehicleEquipments)) {
                            $vehicle->vehicleEquipments()->sync($vehicleEquipments);
                        } else {
                            Log::warning('Invalid vehicle equipments format', [
                                'vehicle_id' => $vehicle->id,
                                'equipments' => $validated['vehicle_equipments'],
                            ]);
                        }
                    } catch (\Exception $e) {
                        Log::error('Failed to sync vehicle equipments', [
                            'vehicle_id' => $vehicle->id,
                            'error' => $e->getMessage(),
                        ]);
                    }
                }

                if ($vehicle->global_status->value !== 'active') {
                    $vehicleType = $this->createDefaultVehicleType('passenger');
                    $vehicle->update(['vehicle_type_id' => $vehicleType]);
                }

            } elseif ($validated['vehicle_type'] === 'freight') {
                $vehicle->update([
                    'image' => $photo_path ?? $vehicle->image,
                    'license_plate_number' => $validated['license_plate_number'] ?? $vehicle->license_plate_number,
                    'color' => $validated['vehicle_color'] ?? $vehicle->color,
                    'year' => $validated['year'] ?? $vehicle->year,
                    'vehicle_model_id' => $validated['vehicle_model'] ?? $vehicle->vehicle_model_id,
                ]);

                if ($vehicle->global_status->value !== 'active') {
                    $vehicleType = $this->createDefaultVehicleType(
                        'freight',
                        $validated['is_covered'] ?? null,
                        $validated['weight_category'] ?? null
                    );
                    $vehicle->update(['vehicle_type_id' => $vehicleType]);
                }
            }

            // Update vehicle documents
            $document = $vehicle->documents()->first();
            if ($document) {
                $document->update([
                    'insurance' => $insurance_path ?? $document->insurance,
                    'technical_inspection' => $technical_inspection_path ?? $document->technical_inspection,
                    'roaming_permit' => $roaming_permit_path ?? $document->roaming_permit,
                    'insurance_expiry' => $validated['insurance_expiry'] ?? $document->insurance_expiry,
                    'technical_inspection_expiry' => $validated['technical_inspection_expiry'] ?? $document->technical_inspection_expiry,
                    'roaming_permit_expiry' => $validated['roaming_permit_expiry'] ?? $document->roaming_permit_expiry,
                ]);
            }

            // Check if user is active and vehicle global_status is rejected, then set to in_progress
            if ($user->status->value === 'active' && $vehicle->global_status->value === 'rejected') {
                $vehicle->update([
                    'global_status' => VehicleStatus::in_progress->value,
                ]);
            }

            // Update driver status
            if ($user->driver != null && $user->driver->deleted_at === null && $user->driver->global_status->value != DriverGlobalStatus::blocked->value && $user->driver->global_status->value != DriverGlobalStatus::active->value) {
                $user->driver->update([
                    'global_status' => DriverGlobalStatus::in_progress->value,
                    'previous_global_status' => DriverGlobalStatus::rejected->value,
                ]);
            }

            // Fix: Change success message and status code for update operation
            return ApiResponse::success($vehicle, 'The drivers vehicle has been successfully updated.', 200);
        } catch (\Exception $e) {
            Log::error('Failed to update vehicle', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return ApiResponse::error(null, 'An error occurred while updating the vehicle: '.$e->getMessage(), 500);
        }
    }

    public function UpdateLocation(array $data)
    {
        // Calculate and send safety notifications based on trip progress

        Log::info('this is the data ', [$data]);
        $lng = $data['lng'];
        $lat = $data['lat'];

        $driverId = $data['driver_id'];

        $driver = Driver::where('id', $driverId)->first();
        Log::info('updating the location of this is the driver', [$driver]);

        if (! $driver) {
            return ApiResponse::error(null, 'Driver not found.', 404, null);
        }
        // here we'r adding the driver location with the postGIS
        $driver->location = DB::raw("ST_SetSRID(ST_MakePoint($lng, $lat), 4326)");

        // Update the last_heartbeat timestamp
        $driver->last_heartbeat = now();

        $driver->save();

        $assignedTrip = Trip::where('driver_id', $driverId)
            ->where('status', TripStatus::assigned->value)
            ->first();

        $driver_arriving = Trip::where('driver_id', $driverId)
            ->where('status', TripStatus::driver_arriving->value)
            ->first();

        $trip_completed = Trip::where('driver_id', $driverId)
            ->where('status', TripStatus::on_trip->value)
            ->first();

        Log::info('Assigned trip found:', ['trip' => $assignedTrip]);

        if ($assignedTrip) {
            $assignedTrip->update([
                'status' => TripStatus::driver_arriving->value,
            ]);

            Log::info('Trip status updated to driver_arriving', [
                'trip_id' => $assignedTrip->id,
                'new_status' => TripStatus::driver_arriving->value,
            ]);
        } elseif ($driver_arriving) {
            $pickup_lng = $driver_arriving->tripLocation->departure_lng;
            $pickup_lat = $driver_arriving->tripLocation->departure_lat;

            Log::info('this is the pickup location of the trip ', ['pickup_lng' => $pickup_lng, 'pickup_lat' => $pickup_lat]);

            // Check if driver is within 60 meters of arrival point using PostGIS
            $distance = DB::select("
            SELECT ST_Distance(
                ST_Transform(ST_SetSRID(ST_MakePoint($lng, $lat), 4326), 3857),
                ST_Transform(ST_SetSRID(ST_MakePoint($pickup_lng, $pickup_lat), 4326), 3857)
            ) as distance
        ")[0]->distance;

            Log::info('Distance to pickup (update location event) point:', [
                'distance_meters' => $distance,
                'coordinates' => [
                    'driver' => ['lat' => $lat, 'lng' => $lng],
                    'destination' => ['lat' => $pickup_lat, 'lng' => $pickup_lng],
                ],
            ]);

            if ($distance <= 60) {
                $driver_arriving->update([
                    'status' => TripStatus::driver_arrived->value,
                ]);

                Log::info('Trip status updated to driver_arrived', [
                    'trip_id' => $driver_arriving->id,
                    'new_status' => TripStatus::driver_arrived->value,
                ]);

                // send an event driver_arrived.
                broadcast(new DriverArrived($driver_arriving, 'driver_arrived'));
            }
        } elseif ($trip_completed) {
            $dropoff_lng = $trip_completed->tripLocation->arrival_lng;
            $dropoff_lat = $trip_completed->tripLocation->arrival_lat;
            Log::info('Trip arrival coordinations  ', [$dropoff_lat, $dropoff_lng]);

            $this->sendSafetyNotificationsIfNeeded($trip_completed);

            $distance = DB::select("
                SELECT ST_Distance(
                    ST_Transform(ST_SetSRID(ST_MakePoint($lng, $lat), 4326), 3857),
                    ST_Transform(ST_SetSRID(ST_MakePoint($dropoff_lng, $dropoff_lat), 4326), 3857)
                ) as distance
            ")[0]->distance;

            Log::info('Distance to dropoff point:', [
                'distance_meters' => $distance,
                'coordinates' => [
                    'driver' => ['lat' => $lat, 'lng' => $lng],
                    'destination' => ['lat' => $dropoff_lat, 'lng' => $dropoff_lng],
                ],
            ]);

            if ($distance <= 60) {
                Log::info('Driver has arrived to the drop off point');
                $trip_completed->update([
                    'status' => TripStatus::waiting_for_driver_confirmation->value,
                ]);

                broadcast(new TripCompleted($trip_completed, 'waiting_driver_to_complete'));
            }
        }

        broadcast(new DriverLocationUpdated($driver, $lat, $lng));
    }

    /**
     * Verify email
     *
     * @response array{message: string, location: array , data: string, status: integer}
     */
    public function updateLocationHttp(Request $request)
    {
        $validated = $request->validate([
            'lng' => 'required|numeric',
            'lat' => 'required|numeric',
            'driver_id' => 'required|numeric',
        ]);

        try {
            if (! Auth::user()->driver || Auth::user()->driver->id != $validated['driver_id']) {
                return ApiResponse::error(
                    null,
                    'Unauthorized: You can only update your own location',
                    401
                );
            }

            $driver = Driver::where('id', $validated['driver_id'])->first();

            if (! $driver) {
                return ApiResponse::error(null, 'Driver not found.', 404, null);
            }

            // Update driver location using PostGIS
            $driver->location = DB::raw("ST_SetSRID(ST_MakePoint({$validated['lng']}, {$validated['lat']}), 4326)");

            // Update the last_heartbeat timestamp
            $driver->last_heartbeat = now();

            $driver->save();

            // Check for trips in different states
            $assignedTrip = Trip::where('driver_id', $validated['driver_id'])
                ->where('status', TripStatus::assigned->value)
                ->first();

            $driver_arriving = Trip::where('driver_id', $validated['driver_id'])
                ->where('status', TripStatus::driver_arriving->value)
                ->first();

            $trip_completed = Trip::where('driver_id', $validated['driver_id'])
                ->where('status', TripStatus::on_trip->value)
                ->first();

            Log::info('Assigned trip found:', ['trip' => $assignedTrip]);

            if ($assignedTrip) {
                $assignedTrip->update([
                    'status' => TripStatus::driver_arriving->value,
                ]);

                Log::info('Trip status updated to driver_arriving', [
                    'trip_id' => $assignedTrip->id,
                    'new_status' => TripStatus::driver_arriving->value,
                ]);
            } elseif ($driver_arriving) {
                $pickup_lng = $driver_arriving->tripLocation->departure_lng;
                $pickup_lat = $driver_arriving->tripLocation->departure_lat;

                Log::info('Pickup location of the trip:', [
                    'pickup_lng' => $pickup_lng,
                    'pickup_lat' => $pickup_lat,
                ]);

                // Check if driver is within 60 meters of arrival point using PostGIS
                $distance = DB::select("
                    SELECT ST_Distance(
                        ST_Transform(ST_SetSRID(ST_MakePoint({$validated['lng']}, {$validated['lat']}), 4326), 3857),
                        ST_Transform(ST_SetSRID(ST_MakePoint($pickup_lng, $pickup_lat), 4326), 3857)
                    ) as distance
                ")[0]->distance;

                Log::info('Distance to pickup point:', [
                    'distance_meters' => $distance,
                    'coordinates' => [
                        'driver' => ['lat' => $validated['lat'], 'lng' => $validated['lng']],
                        'destination' => ['lat' => $pickup_lat, 'lng' => $pickup_lng],
                    ],
                ]);

                if ($distance <= 60) {
                    $driver_arriving->update([
                        'status' => TripStatus::driver_arrived->value,
                    ]);

                    Log::info('Trip status updated to driver_arrived', [
                        'trip_id' => $driver_arriving->id,
                        'new_status' => TripStatus::driver_arrived->value,
                    ]);

                    // Send an event driver_arrived
                    broadcast(new DriverArrived($driver_arriving, 'driver_arrived'));
                }
            } elseif ($trip_completed) {
                $dropoff_lng = $trip_completed->tripLocation->arrival_lng;
                $dropoff_lat = $trip_completed->tripLocation->arrival_lat;
                Log::info('Trip arrival coordinates:', [$dropoff_lat, $dropoff_lng]);

                $this->sendSafetyNotificationsIfNeeded($trip_completed);

                $distance = DB::select("
                    SELECT ST_Distance(
                        ST_Transform(ST_SetSRID(ST_MakePoint({$validated['lng']}, {$validated['lat']}), 4326), 3857),
                        ST_Transform(ST_SetSRID(ST_MakePoint($dropoff_lng, $dropoff_lat), 4326), 3857)
                    ) as distance
                ")[0]->distance;

                Log::info('Distance to dropoff point:', [
                    'distance_meters' => $distance,
                    'coordinates' => [
                        'driver' => ['lat' => $validated['lat'], 'lng' => $validated['lng']],
                        'destination' => ['lat' => $dropoff_lat, 'lng' => $dropoff_lng],
                    ],
                ]);

                if ($distance <= 60) {
                    Log::info('Driver has arrived at the drop off point');
                    $trip_completed->update([
                        'status' => TripStatus::waiting_for_driver_confirmation->value,
                    ]);

                    broadcast(new TripCompleted($trip_completed, 'waiting_driver_to_complete'));
                }
            }

            // Broadcast the location update
            broadcast(new DriverLocationUpdated($driver, $validated['lat'], $validated['lng']));

            return ApiResponse::success([
                'message' => 'Location updated successfully',
                'location' => [
                    'lat' => $validated['lat'],
                    'lng' => $validated['lng'],
                ],
            ], 'Location updated successfully', 200);

        } catch (\Exception $e) {
            Log::error('Failed to update driver location', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return ApiResponse::error(null, 'Failed to update location: '.$e->getMessage(), 500);
        }
    }

    public function RideRequestResponse(array $data)
    {
        Log::info('Processing ride request response', [
            'driver_id' => $data['driver_id'],
            'trip_id' => $data['trip_id'],
            'response' => $data['response'],
        ]);
        $trip = Trip::where('id', $data['trip_id'])
            ->with([
                'rider.user',
                'driver.user',
                'vehicle.vehicleModel.vehicleBrand',
                'vehicle.vehicleType',
                'tripLocation',
                'tripRatings',
            ])
            ->first();

        // Initialize location variables with default values
        $dropoff_lng = $data['dropoff_lng'] ?? 0;
        $dropoff_lat = $data['dropoff_lat'] ?? 0;
        $pickup_lat = $data['pickup_lat'] ?? 0;
        $pickup_lng = $data['pickup_lng'] ?? 0;

        Log::info('trip status : ', [$trip->status->value]);
        if ($trip->status->value != TripStatus::dispatched->value) {
            Log::info('Trip already assigned or not available', [
                'trip_id' => $trip->id,
                'current_status' => $trip->status->value,
            ]);

            broadcast(new RequestResponse($trip, 'trip_already_assigned', (int) $data['driver_id']));

            return;
        }

        if ($data['response'] === 'accept') {

            if ($trip->tripLocation->arrival_lng === $dropoff_lng && $trip->tripLocation->arrival_lat === $dropoff_lat
            && $trip->tripLocation->departure_lat === $pickup_lat && $trip->tripLocation->departure_lng === $pickup_lng
            ) {
                // Use a transaction with pessimistic locking to prevent race conditions
                $result = DB::transaction(function () use ($data) {
                    // Lock the trip record to prevent concurrent modifications
                    $trip = Trip::where('id', $data['trip_id'])->lockForUpdate()->first();

                    if (! $trip) {
                        Log::error('Trip not found for acceptance', ['trip_id' => $data['trip_id']]);

                        return ['success' => false, 'reason' => 'trip_not_found'];
                    }

                    // Check if trip is still available for assignment
                    if ($trip->status->value !== TripStatus::dispatched->value) {
                        Log::info('Trip already assigned or not available', [
                            'trip_id' => $trip->id,
                            'current_status' => $trip->status->value,
                        ]);

                        return [
                            'success' => false,
                            'reason' => 'trip_already_assigned',
                            'trip' => $trip,
                        ];
                    }

                    // Validate that the vehicle belongs to the driver
                    $driver = Driver::with('vehicles')->find($data['driver_id']);
                    if (! $driver) {
                        Log::error('Driver not found for trip assignment', ['driver_id' => $data['driver_id']]);

                        return ['success' => false, 'reason' => 'driver_not_found'];
                    }

                    $driverVehicleIds = $driver->vehicles->pluck('id')->toArray();
                    if (! in_array($data['vehicle_id'], $driverVehicleIds)) {
                        Log::error('Vehicle does not belong to driver', [
                            'driver_id' => $data['driver_id'],
                            'vehicle_id' => $data['vehicle_id'],
                            'driver_vehicles' => $driverVehicleIds,
                        ]);

                        return ['success' => false, 'reason' => 'invalid_vehicle_assignment'];
                    }

                    // Trip is available - assign it to this driver
                    Log::info('Assigning trip to driver', [
                        'trip_id' => $trip->id,
                        'driver_id' => $data['driver_id'],
                        'vehicle_id' => $data['vehicle_id'],
                    ]);

                    // Update trip status immediately to prevent other drivers from accepting
                    $trip->update([
                        'status' => TripStatus::assigned->value,
                        'driver_id' => $data['driver_id'],
                        'vehicle_id' => $data['vehicle_id'],
                    ]);

                    // Update driver status
                    $driver = Driver::find($data['driver_id']);
                    if ($driver && $driver->user) {
                        $driver->user->update(['status' => UserStatus::BUSY->value]);
                    }

                    // Update rider status
                    if ($trip->rider && $trip->rider->user) {
                        $trip->rider->user->update(['status' => UserStatus::BUSY->value]);
                    }

                    return [
                        'success' => true,
                        'reason' => 'assigned',
                        'trip' => $trip,
                    ];
                }, 5); // 5 retries for deadlock
            } else {
                $tripDetails = [
                    'rider' => $trip->rider,
                    'pricing' => $trip->pricing_breakdown,
                    'trip_location' => $trip->tripLocation,
                ];
                broadcast(new RideRequest($trip->driver_id, $tripDetails, true));
            }
        } elseif ($data['response'] === 'reject') {
            Log::info('Driver rejected trip', [
                'trip_id' => $trip->id,
                'driver_id' => $data['driver_id'],
                'reason' => $data['reason'] ?? 'No reason provided',
            ]);

            // For rejection, use a simpler transaction
            $result = DB::transaction(function () use ($data) {
                $trip = Trip::where('id', $data['trip_id'])->first();

                if (! $trip) {
                    Log::error('Trip not found for rejection', ['trip_id' => $data['trip_id']]);

                    return ['success' => false, 'reason' => 'trip_not_found'];
                }

                $trip->refusedDrivers()->attach($data['driver_id'], [
                    'reason' => $data['reason'] ?? 'No reason provided',
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);

                Log::info('Driver rejected trip', [
                    'trip_id' => $trip->id,
                    'driver_id' => $data['driver_id'],
                    'reason' => $data['reason'] ?? 'No reason provided',
                ]);

                return [
                    'success' => true,
                    'reason' => 'rejected',
                    'trip' => $trip,
                ];
            });
        }

        // Process the result
        if (isset($result['success'])) {
            if ($data['response'] === 'accept') {
                if ($result['success']) {
                    // Trip was successfully assigned to this driver
                    $trip = $result['trip'];

                    // Calculate ETA for driver to reach pickup point
                    $origin = [
                        'lat' => $data['lat'],
                        'lng' => $data['lng'],
                    ];
                    $destination = [
                        'lat' => $trip->tripLocation->departure_lat,
                        'lng' => $trip->tripLocation->departure_lng,
                    ];

                    $googleMapsService = app(GoogleMapsService::class);
                    $googleData = $googleMapsService->getDistanceAndTime($origin, $destination);

                    $estimatedPickupTime = now()->addSeconds($googleData['duration_value']);

                    // Get the original trip duration (from pickup to dropoff)
                    // This was already calculated when the trip was created
                    $originalEstimatedDepartureTime = Carbon::parse($trip->estimated_departure_time);
                    $originalEstimatedArrivalTime = Carbon::parse($trip->estimated_arrival_time);
                    $pickupToDropoffDuration = abs($originalEstimatedArrivalTime->diffInSeconds($originalEstimatedDepartureTime));
                    $oldduration = $originalEstimatedArrivalTime->diffInSeconds($originalEstimatedDepartureTime);
                    // Calculate new estimated arrival time by adding the original trip duration to the new pickup time
                    $estimatedArrivalTime = $estimatedPickupTime->copy()->addSeconds($pickupToDropoffDuration);

                    // Update both times
                    $trip->update([
                        'estimated_departure_time' => $estimatedPickupTime->toDateTimeString(),
                        'estimated_arrival_time' => $estimatedArrivalTime->toDateTimeString(),
                    ]);

                    Log::info('Updated trip times', [
                        'trip_id' => $trip->id,
                        'old duration ' => $oldduration,
                        'duration from pickup to drop off ' => $pickupToDropoffDuration,
                        'estimated_departure_time' => $estimatedPickupTime->toDateTimeString(),
                        'estimated_arrival_time' => $estimatedArrivalTime->toDateTimeString(),
                        'driver_to_pickup_duration' => $googleData['duration_value'],
                        'pickup_to_dropoff_duration' => $pickupToDropoffDuration,
                    ]);

                    Log::info('Driver assigned with ETA', [
                        'trip_id' => $trip->id,
                        'driver_id' => $data['driver_id'],
                        'duration' => $googleData['duration_text'],
                        'eta' => $estimatedPickupTime->toDateTimeString(),
                    ]);

                    // Notify the accepting driver
                    broadcast(new RequestResponse($trip, 'assigned', $data['driver_id']));

                    // Notify the rider
                    broadcast(new SearchResponse($trip, 'assigned', $data['lng'] ?? 0, $data['lat'] ?? 0));

                    // Notify all other contacted drivers that the trip is no longer available
                    $contactedDrivers = $trip->contacted_drivers ?? [];
                    if (! empty($contactedDrivers)) {
                        foreach ($contactedDrivers as $contactedDriverId) {
                            if ((int) $contactedDriverId !== (int) $data['driver_id']) {
                                broadcast(new RequestResponse($trip, 'trip_already_assigned', $contactedDriverId));
                            }
                        }
                    }
                } else {
                    // Trip couldn't be assigned - notify the driver
                    broadcast(new RequestResponse($result['trip'], $result['reason'], $data['driver_id']));
                }
            }
        }

        // Release the driver lock after processing
        $this->driverRequestService->handleResponse(
            $data['driver_id'],
            $data['trip_id'],
            $data['response']
        );
    }

    /**
     * driver profile
     *
     * @response UserResource
     *
     **/
    public function profile(Request $request)
    {
        $user = Auth::user();
        if (! $user) {
            return ApiResponse::error(
                null,
                'User not found',
                404, null
            );

        }

        return ApiResponse::success(new UserResource($user), 'Profile retrieved successfully', 200, null);
    }

    /**
     * Send safety notifications to driver based on trip progress
     */
    private function sendSafetyNotificationsIfNeeded(Trip $trip)
    {
        if (! $trip || ! in_array($trip->status->value, [TripStatus::on_trip->value, TripStatus::assigned->value,
            TripStatus::driver_arriving->value, TripStatus::driver_arrived->value,
        ])) {
            return;
        }
        Log::info('entering the safety notification creation');

        // Get driver's current location
        $driver = $trip->driver;
        if (! $driver || ! $driver->location) {
            return;
        }

        // Get trip start and end points
        $startLat = $trip->tripLocation->departure_lat;
        $startLng = $trip->tripLocation->departure_lng;
        $endLat = $trip->tripLocation->arrival_lat;
        $endLng = $trip->tripLocation->arrival_lng;

        // Extract driver's current coordinates
        $driverLocation = DB::select('
            SELECT ST_X(location::geometry) as lng, ST_Y(location::geometry) as lat 
            FROM drivers WHERE id = ?
        ', [$driver->id]);

        if (empty($driverLocation)) {
            return;
        }

        $driverLat = $driverLocation[0]->lat;
        $driverLng = $driverLocation[0]->lng;

        // Calculate total trip distance
        $totalDistance = DB::select('
            SELECT ST_Distance(
                ST_Transform(ST_SetSRID(ST_MakePoint(?, ?), 4326), 3857),
                ST_Transform(ST_SetSRID(ST_MakePoint(?, ?), 4326), 3857)
            ) as distance
        ', [$startLng, $startLat, $endLng, $endLat])[0]->distance;

        // Calculate distance traveled so far
        $distanceTraveled = DB::select('
            SELECT ST_Distance(
                ST_Transform(ST_SetSRID(ST_MakePoint(?, ?), 4326), 3857),
                ST_Transform(ST_SetSRID(ST_MakePoint(?, ?), 4326), 3857)
            ) as distance
        ', [$startLng, $startLat, $driverLng, $driverLat])[0]->distance;

        // Calculate progress percentage based on distance
        $progressPercentage = $totalDistance > 0 ? min(100, ($distanceTraveled / $totalDistance) * 100) : null;
        Log::info('Distance-based progress percentage: ', [$progressPercentage]);

        // Get appropriate message based on trip progress
        $driverMessage = $this->getDriverSafetyMessage($progressPercentage);
        $riderMessage = $this->getRiderSafetyMessage($progressPercentage);

        // Use cache to avoid sending duplicate notifications
        $cacheKey = "safety_notification_{$trip->id}_{$this->getSafetyStage($progressPercentage)}";

        if (! Cache::has($cacheKey) && $driverMessage) {
            // Send notification to driver
            if ($trip->driver && $trip->driver->user) {
                $trip->driver->user->notify(new DriverSafetyNotification(null, [
                    'title' => 'تذكير للسلامة',
                    'description' => $driverMessage,
                ]));

                // Cache that we sent this notification (expire after a reasonable time)
                $cacheDuration = 120; // minutes (2 hours)
                Cache::put($cacheKey, true, now()->addMinutes($cacheDuration));
            }

            if ($trip->rider && $trip->rider->user && $riderMessage) {
                $trip->rider->user->notify(new DriverSafetyNotification(null, [
                    'title' => 'تذكير للسلامة',
                    'description' => $riderMessage,
                ]));
            }
        }
    }

    /**
     * Get safety stage based on trip progress
     */
    private function getSafetyStage(?float $progressPercentage)
    {
        if ($progressPercentage === null) {
            return null;
        }

        if ($progressPercentage < 50) {
            return 'start';
        } elseif ($progressPercentage < 80 && $progressPercentage >= 50) {
            return 'middle';
        } else {
            return 'end';
        }
    }

    /**
     * Get appropriate safety message based on trip progress for drivers
     */
    private function getDriverSafetyMessage(?float $progressPercentage)
    {
        if ($progressPercentage === null) {
            return null;
        }

        if ($progressPercentage < 30) {
            return 'لا تنسَ ربط حزام الأمان';
        } elseif ($progressPercentage < 70) {
            return 'الرجاء القيادة بحذر، حياتك أمانة';
        } else {
            return 'حافظ على مسافة الأمان بينك وبين السيارات الأخرى';
        }
    }

    private function getRiderSafetyMessage(?float $progressPercentage)
    {
        if ($progressPercentage === null) {
            return null;
        }

        if ($progressPercentage < 30) {
            return 'تأكد من غلق الباب بإحكام بعد الركوب';
        } elseif ($progressPercentage < 70) {
            return 'في حالة الطوارئ، اتصل بخدمات الطوارئ ';
        } else {
            return 'لا تنسَ أخذ أغراضك الشخصية قبل النزول';
        }
    }
}
