<?php

namespace App\Notifications;

use App\Models\UserInvitation;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Lang;

class UserInvitationNotification extends Notification
{
    use Queueable;

    public function __construct(private readonly UserInvitation $invitation) {}

    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    public function toMail(object $notifiable): MailMessage
    {
        $invitedBy = $this->invitation->invitedBy;
        $expirationDays = $this->invitation->expires_at->diffInDays(now());

        return (new MailMessage)
            ->subject(Lang::get('You\'ve been invited to join :appName', ['appName' => config('app.name')]))
            ->greeting(Lang::get('Hello!'))
            ->line(Lang::get('You have been invited by :inviterName to join :appName as an administrator.', [
                'inviterName' => $invitedBy->name ?? $invitedBy->email,
                'appName' => config('app.name'),
            ]))
            ->line(Lang::get('To accept this invitation and set up your account, click the button below:'))
            ->action(Lang::get('Accept Invitation'), $this->invitation->getInvitationUrl())
            ->line(Lang::get('This invitation will expire in :count days.', ['count' => $expirationDays]))
            ->line(Lang::get('If you did not expect to receive this invitation, you can safely ignore this email.'))
            ->salutation(Lang::get('Best regards,') . "\n" . config('app.name') . ' ' . Lang::get('Team'));
    }
}
