<?php

namespace App\Notifications\Firebase_notifications;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use NotificationChannels\Fcm\FcmChannel;
use NotificationChannels\Fcm\FcmMessage;
use Throwable;

class Silent_Notify_User extends Notification implements ShouldQueue
{
    use Queueable, SerializesModels;

    protected $data;

    protected $userId;

    public $tries = 3;

    public $maxExceptions = 3;

    public $backoff = 10;

    public function __construct($user_id, $data)
    {
        $this->userId = $user_id;
        $this->data = $data;
    }

    public function via($notifiable)
    {
        return [FcmChannel::class];
    }

    protected function getUser()
    {
        return User::findOrFail($this->userId);
    }

    public function toFcm($notifiable): FcmMessage
    {
        try {
            $message = new FcmMessage;

            return $message
                ->data([
                    'title' => $this->data['title'],
                    'notification_id' => uniqid('fcm_'),
                    'silent' => 'true',
                ])
                ->custom([
                    'android' => [
                        'priority' => 'high',
                        'data' => [
                            'silent' => true,
                        ],
                    ],
                    'apns' => [
                        'headers' => [
                            'apns-priority' => '5',
                            'apns-push-type' => 'background',
                        ],
                        'payload' => [
                            'aps' => [
                                'content-available' => 1,
                                'badge' => 0,
                                'sound' => '',
                            ],
                        ],
                    ],
                ]);
        } catch (Throwable $e) {
            Log::error('Silent FCM Notification Creation Failed', [
                'user_id' => $this->userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw $e;
        }
    }

    public function failed(Throwable $exception)
    {
        try {
            $user = $this->getUser();
            $tokens = $user->tokens->pluck('token')->toArray();
        } catch (Throwable $e) {
            $tokens = [];
        }

        Log::error('Silent FCM Notification Failed to Send', [
            'user_id' => $this->userId,
            'title' => $this->data['title'],
            'error' => $exception->getMessage(),
            'tokens' => $tokens,
        ]);
    }
}
