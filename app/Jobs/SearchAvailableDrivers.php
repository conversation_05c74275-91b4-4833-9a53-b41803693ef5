<?php

namespace App\Jobs;

use App\Enums\Trips\TripStatus;
use App\Events\TripEvents\SearchResponse;
use App\Http\Controllers\Api\TripController;
use App\Models\Trip;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SearchAvailableDrivers implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    // Set a very high timeout to accommodate the entire search process
    public $timeout = 300; // 5 minutes

    // Important: Set tries to 1 to prevent retries
    public $tries = 1;

    public $deleteWhenMissingModels = true;

    // Disable automatic retry on timeout
    public $failOnTimeout = true;

    protected $tripData;

    protected $tripId;

    public function __construct(array $tripData, int $tripId)
    {
        $this->tripData = $tripData;
        $this->tripId = $tripId;
    }

    public function handle()
    {
        try {
            Log::info('Starting driver search job', [
                'trip_id' => $this->tripId,
                'data' => $this->tripData,
            ]);

            // Increase PHP execution time limit for this job
            set_time_limit(300);

            app(TripController::class)->getAvailableDrivers($this->tripData, $this->tripId);

        } catch (\Exception $e) {
            Log::error('Driver search job failed', [
                'trip_id' => $this->tripId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            $trip = Trip::where('id', $this->tripId)
                ->with([
                    'rider.user',
                    'driver.user',
                    'vehicle.vehicleModel.vehicleBrand',
                    'vehicle.vehicleType',
                    'tripLocation',
                    'tripRatings',
                ])
                ->first();
            if ($trip) {
                $trip->update(['status' => TripStatus::timeout->value]);
                broadcast(new SearchResponse($trip, 'timeout'));
            }

            // Don't rethrow the exception - we want the job to end here
        }
    }

    public function failed(\Throwable $exception)
    {
        Log::error('Driver search job failed completely', [
            'trip_id' => $this->tripId,
            'error' => $exception->getMessage(),
        ]);

        $trip = Trip::where('id', $this->tripId)
            ->with([
                'rider.user',
                'driver.user',
                'vehicle.vehicleModel.vehicleBrand',
                'vehicle.vehicleType',
                'tripLocation',
                'tripRatings',
            ])
            ->first();
        if ($trip) {
            $trip->update(['status' => TripStatus::timeout->value]);
            broadcast(new SearchResponse($trip, 'timeout'));
        }
    }
}
