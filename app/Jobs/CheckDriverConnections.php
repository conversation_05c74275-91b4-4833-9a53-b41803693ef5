<?php

namespace App\Jobs;

use App\Enums\UserStatus;
use App\Models\Driver;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class CheckDriverConnections implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of seconds after which the job should timeout.
     */
    public $timeout = 120;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        Log::info('Starting driver connection check');

        $this->checkStaleConnections();

        Log::info('Completed driver connection check');
    }

    /**
     * Check for stale driver connections and set them offline
     */
    private function checkStaleConnections(): void
    {
        // Set offline threshold to 2 minutes ago
        $offlineThreshold = now()->subMinutes(2);

        Log::info('Checking for stale driver connections', [
            'offline_threshold' => $offlineThreshold->toDateTimeString(),
        ]);

        // Find drivers who should be set to offline
        // (online drivers who haven't had WebSocket activity recently)
        $driversToSetOffline = Driver::whereHas('user', function ($query) {
            $query->where('status', UserStatus::ONLINE->value);
        })
            ->where(function ($query) use ($offlineThreshold) {
                $query->where('last_heartbeat', '<', $offlineThreshold)
                    ->orWhereNull('last_heartbeat');
            })
            ->with('user')
            ->get();

        Log::info('Found drivers to set offline', [
            'count' => $driversToSetOffline->count(),
        ]);

        foreach ($driversToSetOffline as $driver) {
            try {
                // Update user status to offline
                $driver->user->update(['status' => UserStatus::OFFLINE->value]);

                Log::info('Driver automatically set to offline due to stale connection', [
                    'driver_id' => $driver->id,
                    'user_id' => $driver->user_id,
                    'last_heartbeat' => $driver->last_heartbeat,
                    'threshold' => $offlineThreshold->toDateTimeString(),
                ]);

            } catch (\Exception $e) {
                Log::error('Failed to set driver offline', [
                    'driver_id' => $driver->id,
                    'error' => $e->getMessage(),
                ]);
            }
        }
    }
}
